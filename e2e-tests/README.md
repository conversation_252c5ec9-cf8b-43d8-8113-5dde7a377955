# End-to-End (E2E) Tests

This directory contains E2E tests for the `sea-flux-native` application:

- For Web - [Playwright](https://playwright.dev/) is used.

---

## Setup

1. **Configure environment variables:**

   - Navigate to root `e2e-tests` folder and copy `.env.example` to create a `.env` file:

     ```sh
     cp .env.example .env
     ```

   - Replace the variable placeholder values in your `.env` file (secret values found in LastPass 'E2E Tests Secrets' note), e.g.:
     ```
     E2E_ADMIN_PASSWORD=<grab_value_from_LastPass>
     ```

2. **Install dependencies in directory for type of e2e tests you want to run (e.g. `/web`):**

   ```sh
   yarn install
   ```

---

## Running the tests

From the `/web` folder:

2. **Run E2E tests in headless mode:**

   ```sh
   yarn test
   ```

3. **Run E2E tests with the Playwright UI:**
   ```sh
   yarn test --ui
   ```

> **Note:** The application dev server starts automatically via `webServer` config in playwright.config.ts file.

Further options:

1. **Run E2E tests for a particular section in the app:**
   Since tests are broken down by section in the app, you can specify a particular section to run by specifying a particular file, e.g.:
   ```sh
   yarn test __tests__/vessel/safety/safety-checks.spec.ts --ui
   ```

---

## Project Structure

- `__tests__/` – Test files
- `helpers/` – Shared helper functions
- `data/` – Test data

---

## Contributing

- Follow [Playwright best practices](https://playwright.dev/docs/best-practices).
- **Use user-visible locators** - When possible, select elements the same way users would interact with them ([see Playwright's locators](https://playwright.dev/docs/locators#quick-guide) for more info).
- **Otherwise, use `data-testid` attributes** - When user-visible locators aren't sufficient, use `data-testid` attributes for reliable element selection. For example:

```typescript
// This ensures playwright can reliably target a specific safety item row and column (with its DocumentID)
await page.getByTestId("sea-table-cell-C5QVQoh83CvPTXh3YIfw-safety-item");
```

- Keep tests independent and atomic.
- Use helper functions for common actions.
- Update the `.env.example` file if have modified the required environment variables.
- Update this README if needed.

---

## Troubleshooting

- Ensure all environment variables are set in `.env` (refer to `.env.example` for most up-to-date variables required for tests to run).
- For more help, see the [Playwright documentation](https://playwright.dev/docs/intro).
