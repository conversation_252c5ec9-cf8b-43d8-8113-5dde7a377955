import { adminUser } from "../data/users";
import { Page } from "@playwright/test";
import { TestLogger } from "../helpers/global";

type Credentials = {
  email: string;
  password: string;
};

export async function login(
  page: Page,
  { email, password }: Credentials = adminUser,
) {
  TestLogger.info("Starting login process...");

  await page.goto("/login", {
    waitUntil: "commit", // Wait for the page to start loading in case Expo is slow
    timeout: process.env.CI ? 120000 : 30000,
  });

  await page.fill('input[type="email"]', email);
  await page.fill('input[type="password"]', password);
  await page.getByText("Log In").click();
  await page.waitForURL("/fleet-dashboard");

  TestLogger.success("Login successful, user redirected to Fleet Dashboard.");
}
