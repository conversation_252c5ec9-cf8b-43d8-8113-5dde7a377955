export function getEnvVariable(name: string): string {
  const value = process.env[name] as string | undefined;
  if (!value) {
    throw new Error(
      `\x1b[33m⚠️  Warning: Environment variable ${name} is undefined or empty.\x1b[0m\n`,
    );
  } else {
    return value;
  }
}

// This logger is used to provide logging output during test execution.
export class TestLogger {
  static info(message: string) {
    process.stdout.write(`ℹ️ ${message}\n`);
  }

  static success(message: string) {
    process.stdout.write(`✅ ${message}\n`);
  }
}
