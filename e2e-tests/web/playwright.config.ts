import { defineConfig, devices } from "@playwright/test";
/**
 * Playwright configuration file for end-to-end tests.
 * This file sets up the testing environment, including browser configurations,
 * test directories, and environment variables.
 */
/**
 * Read environment variables from file.
 * https://github.com/motdotla/dotenv
 */
import dotenv from "dotenv";
import path from "path";
dotenv.config({ path: path.resolve(__dirname, "../.env") });

/**
 * See https://playwright.dev/docs/test-configuration.
 */
export default defineConfig({
  testDir: "./__tests__",
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 0,
  /* Opt out of parallel tests on CI. */
  workers: process.env.CI ? 1 : undefined,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: "html",
  /* Shared timeout for all tests */
  /* This is the maximum time a test can run before it is considered failed. */
  timeout: process.env.CI ? 120000 : 30000,
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: "http://localhost:8081",
    /* Navigation timeout in milliseconds to allow for the app bundle to load */
    navigationTimeout: process.env.CI ? 90000 : 30000,
    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: "on-first-retry",
  },

  /* Configure projects for major browsers */
  projects: [
    {
      name: "chromium",
      use: { ...devices["Desktop Chrome"] },
    },
  ],

  /* Run local dev server before starting the tests if not CI run */
  webServer: process.env.CI
    ? undefined
    : {
        command: "cd ../../ && yarn start:test",
        url: "http://localhost:8081",
        reuseExistingServer: !process.env.CI,
        timeout: 120 * 1000, // 2 minutes
        stdout: "pipe",
        stderr: "pipe",
      },
});
