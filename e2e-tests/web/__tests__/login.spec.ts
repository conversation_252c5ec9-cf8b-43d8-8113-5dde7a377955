import { test, expect } from "@playwright/test";
import { login } from "../helpers/login";
import { adminUser } from "../data/users";

test.describe("Login Page", () => {
  test("should log in as Demo NZ manager and see Fleet Dashboard", async ({
    page,
  }) => {
    await login(page);
    await expect(page).toHaveURL("/fleet-dashboard");
    await expect(page.getByText(`Hi ${adminUser.firstName}`)).toBeVisible();
    await expect(page.getByText(`${adminUser.vesselName}`)).toBeVisible();
  });
});
