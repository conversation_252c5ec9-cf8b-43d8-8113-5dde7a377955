# Project Title (Sea Flux)

## Node Version

This project requires Node.js version 20.x

## How to Start the Project

1. Clone the repository:
   ```bash
   git clone "the repo url"
   ```
2. Navigate to the project directory:
   ```bash
   cd sea-flux-native
   ```
3. Install the dependencies: (install nvm first)
   ***(if your default node version is >=20 then you can skip this step)***

> Please use this guide to install
> nvm. [NVM install guide](https://www.freecodecamp.org/news/node-version-manager-nvm-install-guide/)

***For Windows*** [Download](https://github.com/coreybutler/nvm-windows/releases/download/1.2.2/nvm-setup.exe)

***For MacOS*** [Install](https://formulae.brew.sh/formula/nvm)

   ```bash
   nvm install 20
   nvm use 20
   ```

NOTE: when create terminal please run this command first
or you can use `node version 20` globally using nvm

   ```bash
   nvm alias default 20
   nvm use default
   ```

4. Install the dependencies:

If you don't have yarn installed

  ```bash
  npm i -g yarn
  ```

TroubleShoot:
> yarn : File C:\nvm4w\nodejs\yarn.ps1 cannot be loaded because running scripts is disabled on this system.

> run below command

```bash
$ Set-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy Unrestricted
 ```

   ```bash
   yarn install
   ```

5. Start the project:
   ```bash
   yarn start
   ```

# Setup React Native Environment ([Official Guide](https://reactnative.dev/docs/set-up-your-environment))

### 1. Make sure Android SDK is available

- Download Android Studio from https://developer.android.com/studio
- Install Android Studio and run one demo application. (It is optional but it will make your life easier)
- Install Android SDK Platform-Tools and Android SDK Build-Tools from the SDK Manager. It can be installed seperately or
  from android studio.
- Set **ANDROID_HOME** environment variable for windows. For `linux/macos`, add ANDROID_HOME to `~/.zshrc` or
  `~/.bashrc`. See the [official guide](https://reactnative.dev/docs/set-up-your-environment#android-sdk) for more
  details
- Add **ANDROID_HOME**/platform-tools to PATH for windows. For `linux/macos`, add ANDROID_HOME/platform-tools to PATH.
  See the [official guide](https://reactnative.dev/docs/set-up-your-environment#android-sdk) for more details

**Example SDK path for windows:**
> C:\Users\<USER>\AppData\Local\Android\Sdk (Fow windows)

**MacOS/Linux Command**
> ```$ export ANDROID_HOME=$HOME/Library/Android/sdk (Command For MacOS/Linux)```

# Install java 17 Guide ([Windows](https://reactnative.dev/docs/set-up-your-environment?os=windows) | [MacOS/Linux](https://reactnative.dev/docs/set-up-your-environment?os=macos))

```shell
# For Windows
$ choco install -y microsoft-openjdk17

# For MacOS/Linux
brew install --cask zulu@17

# Get path to where cask was installed to find the JDK installer
brew info --cask zulu@17

# ==> zulu@17: <version number>
# https://www.azul.com/downloads/
# Installed
# /opt/homebrew/Caskroom/zulu@17/<version number> (185.8MB) (note that the path is /usr/local/Caskroom on non-Apple Silicon Macs)
# Installed using the formulae.brew.sh API on 2024-06-06 at 10:00:00

# Navigate to the folder
open /opt/homebrew/Caskroom/zulu@17/<version number> # or /usr/local/Caskroom/zulu@17/<version number>
```

- Set **JAVA_HOME** environment variable for `windows`. For `linux/macos`, add JAVA_HOME to `~/.zshrc` or `~/.bashrc`.
  See the [official guide](https://reactnative.dev/docs/set-up-your-environment#android-sdk) for more details
- After adding those in ~/.zshrc or ~/.bash_profile must run the below command

```bash
$ source ~/.zshrc 
or 
$ source ~/.bash_profile
```

### 2. Install Node.js and Yarn

- Install Node.js using nvm
- Install Yarn using npm
  ```bash
  npm install -g yarn
  ```
- Install dependencies
  ```bash
  yarn install
  ```
- Start the project
  ```bash
  # Start the project
  yarn start 
  # Run on Android (Make sure a device is connected or an emulator is running)
  yarn android
  # Run on iOS (Make sure a device is connected or an emulator is running)
  yarn ios 
  # Run on Web
  yarn web
  ```

# Install Pod for iOS

1. cd ios
2. pod install

> Note: if error to upgrade the ruby

```bash
brew install rbenv
rbenv init

#Add this to your ~/.zshrc or ~/.bashrc and restart your terminal:
eval "$(rbenv init -)"

rbenv install 3.2.0
rbenv global 3.2.0

gem install cocoapods

#Verify Installation
pod --version
ruby -v
```

> Note: If error: tool 'xcodebuild' requires Xcode, but active developer directory '/Library/Developer/CommandLineTools'
> is a command line tools instance

```bash
sudo xcode-select -s /Applications/Xcode.app/Contents/Developer
```

> Note: if error when install pod, try to remove pod and install again

```bash
rm -rf Pods Podfile.lock
pod install
```

> Note: if error with boringssl, try below command before install pod. It's 500 MB postBuffer for git. (Have a very good
> internet connection a minimum of 5 MB/s Download speed and also if it doesn't work try to use VPN)

```bash
git config --global http.postBuffer 524288000
```

# NDK Issue

> Note: if you get NDK error, try to install ***NDK v26.1.10909125***

# ***Note***

For initial run on Android or iOS, you need to run the project on the specific platform first.

For Android

```bash
yarn android
```

For iOS

```bash
yarn ios
```

> Then to run the project on `web` simply press `w` on the terminal or on the expo go terminal.

# SVG Support

```TSX
import Bower from '@assets/icons/bower.svg';
import Pdf from '@assets/icons/pdf.svg';

<Bower width={100} height={100} />
<Pdf width={100} height={100} />
```

# Font Support

```TSX
import { useFonts } from 'expo-font'

const [fontsLoaded] = useFonts({
  'Metropolis': require('./assets/fonts/Metropolis-Regular.ttf'),
});

if (!fontsLoaded) {
  return <Text>Loading...</Text>;
}
```

If you want to add more font, you can add it in `assets/fonts` and add it in `useFonts`

Also add to tailwind config

```JS
module.exports = {
    content: [
        "./App.{js,jsx,ts,tsx}",
        "./src/**/*.{js,jsx,ts,tsx}",
    ],
    theme: {
        fontFamily: {
            'sea': ['Metropolis', 'sans-serif'],
            'Metropolis': ['Metropolis-SemiBold'],
        },
    },
}
```

# 🔥 Firebase Integration Guide

## Overview

This project supports Firebase across **Web**, **Android**, and **iOS** platforms, centralized in `src/lib/firebase/`.

## 🚀 Supported Firebase Features

### 🔥 Firebase Exports

**Instances:**

- `auth`
- `analytics`
- `firestore`
- `storage`
- `functions`

**Interfaces:**

- `SplittableBatch` - for this project specific implementation

**Methods:**

- `splittableBatch()` - for this project specific implementation

### 🔐 Authentication

**Methods:**

- `login()`
- `signOut()`
- `getCurrentUser()`
- `getIdToken()`
- `signInWithCustomToken()`
- `createUserWithEmailAndPassword()`

- Real-time: `onAuthStateChanged()`

**Types:**

- `User`

### 📊 Firestore

**Methods:**

- CRUD Operations: `collection()`, `doc()`, `setDoc()`, `addDoc()`, `deleteDoc()`
- Querying: `query()`, `where()`, `orderBy()`, `limit()`
- Real-time: `onSnapshot()`
- Batch Operations: `splittableBatch()`, `writeBatch()`

**Types:**

- `DocumentSnapshot`
- `QuerySnapshot`
- `CollectionReference`

### 💾 Storage

**Methods:**

- Upload: `uploadBytesResumable()`, `uploadBytes()`
- Download: `getDownloadURL()`, `getBytes()`, `getBlob()`
- Management: `ref()`, `deleteObject()`, `listAll()`

**Types:**

- `StorageReference`
- `StorageTaskSnapshot`

### 🌐 Functions

**Methods:**

- `httpsCallable()`

**Constants:**

- `functionsRegion`

### 📈 Analytics

**Methods:**

- `logEvent()`
- `setUserProperties()`

### 🐞 Crashlytics

**Status:** Enabled ✅

## 💡 Quick Start

Explore our comprehensive Firebase integration in `src/lib/firebase/`! The implementation is in the project files.

## 📝 Notes

- Analytics events are automatically prefixed with platform name (`web_` or `native_`)
- Functions are configured to use the `australia-southeast1` region by default
- All services handle both web and native platforms automatically
- Type safety is enforced across all Firebase interactions

## 🚨 Platform-Specific Exceptions

When checking if a document exists in Firestore, the API differs between web and native platforms:

To check if a document exists instead of ```doc.exists()``` (web), or ```doc.exists``` (native), use utility function
```docExists(...)``` that comes from ```@src/lib/firebase```

# 🔥 FileSystem Guide

`src/shared-state/FileSyncSystem/fileSystem.ts`

## Overview

The FileSystem component provides a unified interface for file operations across all platforms `(web, android, ios)`. It
supports the following operations:

- `mkdir` - Create a directory
- `readdir` - Read a directory
- `stat` - Get file information
- `writeFile` - Write a file
- `readFile` - Read a file
- `deleteFile` - Delete a file
- `rename` - Rename a file

You can check if the file caching is working by running the project and check the `FileSystem` folder in the device
storage.

## Android (Using Terminal)

```bash
$ adb shell
$ run-as nz.co.orchid.seaflux
$ cd files
$ ls -la

# Now you can see the files in the device storage using terminal
```

## Android (Using Android Studio Device Explorer)

- Open Android Studio
- Connect your device
- Click on the `View > Tool Windows > Device Explorer`
- Expand the `data > data > nz.co.orchid.seaflux > files` section
- You can see the files in the device storage and also can open it.

## iOS (Using Terminal)

```bash
$ cd /Users/<USER>/Library/Developer/CoreSimulator/Devices/{device-id}/data/Containers/Data/Application/{application-id}/Documents

# Now you can see the files in the device storage using terminal
```

## Web

```bash
# Run the project on web
yarn web
```

## Web uses indexedDB to cache the files

You can check if the file caching is working by running the project and check the folder in the browser.

> Right click on the page and click on `Inspect` and go to `Application` tab and expand the `IndexedDB` section.

> You can see the files in the `Filesystem` folder.

> You can also clear the cache by clicking on the `Clear` button.


