const { withAndroidManifest, withInfoPlist } = require('expo/config-plugins');

/**
 * This file was created with the help of
 * https://docs.expo.dev/config-plugins/plugins/
 */


/**
 * Custom AndroidManifest.xml plugin
 */
const withAndroidManifestPlugin = (config) => {
  return withAndroidManifest(config, config => {
    const mainApplication = config?.modResults?.manifest?.application?.[0];

    if (mainApplication) {
      // Set AndroidManifest.xml <application/> values
      console.log('Customising AndroidManifest.xml...');

      // The following code is to solve an error that occurs when writing a large file to disk on Android.
      // Source of error: "reader.readAsDataURL(data);" within lib/fileSystem.ts writeFile function
      //
      // Error message in app:
      // --------------------------
      // Could not invoke FileReaderModule.readAsDataURL
      // null
      // Failed to allocate XXXXXXXXXX byte  allocation with <PERSON><PERSON>YYYYYYYYYYY free bytes and 92MB until OOM,
      // target footprint ZZZZZZZZZZZ, growth limit WWWWWWWWWWWWW
      // --------------------------
      //
      // Solution was gleaned from:
      // https://stackoverflow.com/questions/32244851/androidjava-lang-outofmemoryerror-failed-to-allocate-a-23970828-byte-allocatio?rq=2
      mainApplication.$['android:largeHeap'] = 'true';
      //mainApplication.$['android:hardwareAccelerated'] = 'false'; // This suggestion doesn't seem to be needed to resolve the issue
    }

    return config;
  });
};

/**
 * Custom Info.plist plugin
 */
const withInfoPlistPlugin = (config) => {
  return withInfoPlist(config, config => {
    //console.log('Customising Info.plist...');
    // ...
    return config;
  });
};

/**
 * Combines our plugins into one
 */
const withPlugin = (config) => {
  config = withAndroidManifestPlugin(config);
  config = withInfoPlistPlugin(config);
  return config;
};

module.exports = withPlugin;
