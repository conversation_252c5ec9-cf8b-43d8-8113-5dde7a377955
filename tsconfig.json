{"extends": "expo/tsconfig.base", "compilerOptions": {"experimentalDecorators": true, "emitDecoratorMetadata": true, "moduleResolution": "bundler", "strict": true, "module": "esnext", "jsx": "react-native", "baseUrl": ".", "paths": {"@src/*": ["./src/*"], "@assets/*": ["./assets/*"], "@firebase/auth": ["./node_modules/@firebase/auth/dist/index.rn.d.ts"]}, "typeRoots": ["./node_modules/@types", "./src/types"]}, "expo": {"resolvePlatformExtension": true}}