# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

.idea

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts
.vscode/

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision
ios/
android/

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo

app-example

*storybook.log
/storybook-static/

# ESLint
.eslintcache
