import { registerRootComponent } from "expo";

let AppEntryPoint;

if (process.env.EXPO_PUBLIC_STORYBOOK_ENABLED === "true") {
  AppEntryPoint = require("./.rnstorybook").default;
  // TODO: SF-289 - Uncomment this only when working with storybook
  // registerRootComponent(AppEntryPoint);
} else {
  // Load the app immediately
  AppEntryPoint = require("expo-router/entry");

  // Initialize Firebase after the app loads
  Promise.resolve().then(async () => {
    try {
      /**
       * This is intentionally done. Manually import the firebase config to avoid circular dependencies
       */
      const { ServiceContainer } = await import(
        "@src/domain/di/ServiceContainer"
      );
      const { IFirebase } = await import("@src/domain/IFirebase");
      const { SERVICES } = await import("@src/domain/di/ServiceRegistry");

      const firebase = ServiceContainer.get<IFirebase>(SERVICES.IFirebase);
      await firebase.initialize();
      console.log("Firebase initialized successfully");
    } catch (error) {
      console.error("Failed to initialize Firebase:", error);
    }
  });
}

export default AppEntryPoint;
