<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>668969870326-tsuq7ei68maunk8dm64110f7r1oe2gmu.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.668969870326-tsuq7ei68maunk8dm64110f7r1oe2gmu</string>
	<key>API_KEY</key>
	<string>AIzaSyB_P9YUSEHHuTE_P1KZGxA1GRJy4b_KVmY</string>
	<key>GCM_SENDER_ID</key>
	<string>668969870326</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.sea-flux.sea-flux.preview</string>
	<key>PROJECT_ID</key>
	<string>sea-flux-staging</string>
	<key>STORAGE_BUCKET</key>
	<string>sea-flux-staging.appspot.com</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:668969870326:ios:146672b6330143ed369dd7</string>
	<key>DATABASE_URL</key>
	<string>https://sea-flux-staging-default-rtdb.firebaseio.com</string>
</dict>
</plist>