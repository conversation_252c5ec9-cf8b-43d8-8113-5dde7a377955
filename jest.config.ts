import type { Config } from 'jest'
import { createJsWithBabelPreset } from 'ts-jest'

const jsWithBabelPreset = createJsWithBabelPreset({
  tsconfig: 'tsconfig.json', // or create tsconfig.spec.json if you prefer
  babelConfig: true,
})

const jestConfig: Config = {
  preset: 'react-native',
  transform: jsWithBabelPreset.transform,
  // testEnvironment: 'jsdom',
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  moduleNameMapper: {
    '^@src/(.*)$': '<rootDir>/src/$1',
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  globals: {
    'ts-jest': {
      diagnostics: false, // Disable TypeScript diagnostics
    },
  },
}

export default jestConfig
