import { SeaBreadcrumbs } from '@src/components/_atoms/SeaBreadcrumbs/SeaBreadcrumbs'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaIconProps } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { theme } from '@src/theme'
import React, { ReactElement } from 'react'
import { ScrollView, StyleSheet, TouchableOpacity, View, ViewStyle } from 'react-native'
import SeaFileImage from '@src/components/_atoms/SeaFileImage/SeaFileImage'
import { SeaTableImage } from '@src/components/_atoms/SeaTable/SeaTableImage'
import { SeaPageCardSubNav, SeaSubNav, SubNav, SubNavType } from '../SeaSubNav/SeaSubNav'
import Animated, { interpolate, useAnimatedStyle, useSharedValue, runOnJS } from 'react-native-reanimated'
import { Extrapolation } from 'react-native-reanimated/src/interpolation'

export type SecondaryActionButton =
  | [] // 0 buttons
  | [React.ReactElement<typeof SeaButton>] // 1 button
  | [React.ReactElement<typeof SeaButton>, React.ReactElement<typeof SeaButton>] // 2 buttons
  | [React.ReactElement<typeof SeaButton>, React.ReactElement<typeof SeaButton>, React.ReactElement<typeof SeaButton>]

export type SeaPageCardProps = {
  primaryActionButton?: React.ReactElement<typeof SeaButton>
  secondaryActionButton?: SecondaryActionButton // 3 buttons
  subNav?: SubNav[]
  subNavType?: SubNavType
  style?: ViewStyle
  contentStyle?: ViewStyle
  wrapperStyle?: ViewStyle
  hideBreadcrumbs?: boolean
  hidePath?: boolean
  hideHeader?: boolean
  onBackPress?: () => void
  children?: React.ReactNode
  titleComponent?: React.ReactElement<typeof SeaPageCardTitle>
  scrollDrivenOffset?: Animated.SharedValue<number>
  collapseRange?: number
}

export function SeaPageCard({
  titleComponent,
  primaryActionButton,
  secondaryActionButton,
  subNav,
  subNavType = SubNavType.Link,
  children,
  style,
  contentStyle,
  wrapperStyle,
  hideBreadcrumbs = false,
  hidePath = false,
  onBackPress,
  scrollDrivenOffset,
  collapseRange = 100, // Default collapse range
}: SeaPageCardProps) {
  const { isMobileWidth, isTabletWidth, isDesktopWidth } = useDeviceWidth()
  const titleHeight = useSharedValue(0)

  const onTitleLayout = (event: any) => {
    const { height } = event.nativeEvent.layout
    titleHeight.value = height
  }

  const titleAnimatedStyle = useAnimatedStyle(() => {
    if (!scrollDrivenOffset || titleHeight.value === 0) return {}

    const progress = Math.min(scrollDrivenOffset.value / collapseRange, 1)

    return {
      height: interpolate(progress, [0, 1], [titleHeight.value, 0], Extrapolation.CLAMP),
      opacity: interpolate(progress, [0, 1], [1, 0], Extrapolation.CLAMP),
      marginBottom: interpolate(progress, [0, 1], [10, 0], Extrapolation.CLAMP),
      overflow: 'hidden',
    }
  }, [scrollDrivenOffset, collapseRange, titleHeight])

  // Also animate the parent container to shrink the gap
  const titleContainerStyle = useAnimatedStyle(() => {
    if (!scrollDrivenOffset) return {}

    const progress = Math.min(scrollDrivenOffset.value / collapseRange, 1)

    return {
      gap: interpolate(progress, [0, 1], [10, 0], Extrapolation.CLAMP),
    }
  }, [scrollDrivenOffset, collapseRange])

  return (
    <View style={[styles.container, isMobileWidth && styles.mobileContainer, style]}>
      <View style={[styles.defaultVerticalPadding, isMobileWidth && styles.mobileVerticalPadding]}>
        {/** Title and Actions */}
        <View style={[styles.defaultHorizontalPadding, isMobileWidth && styles.mobileHorizontalPadding, wrapperStyle]}>
          <>
            {(isMobileWidth || isTabletWidth || isDesktopWidth) && (
              <Animated.View style={titleContainerStyle}>
                <SeaStack direction="column" gap={10} align={'start'}>
                  <SeaStack direction="row" gap={10} align={'center'} justify={'between'} width={'100%'}>
                    <View style={{ flexDirection: 'column', flexShrink: 1, justifyContent: 'space-between' }}>
                      {!hideBreadcrumbs ? (
                        <SeaBreadcrumbs hidePath={hidePath} onBackPress={onBackPress} />
                      ) : (
                        (titleComponent ?? <></>)
                      )}
                    </View>
                    <SeaStack direction="row" gap={10} style={{ flexShrink: 1 }}>
                      {secondaryActionButton && secondaryActionButton}
                      {primaryActionButton && primaryActionButton}
                    </SeaStack>
                  </SeaStack>
                  {!hideBreadcrumbs && titleComponent ? (
                    <Animated.View style={titleAnimatedStyle} onLayout={onTitleLayout}>
                      {titleComponent}
                    </Animated.View>
                  ) : (
                    <></>
                  )}
                </SeaStack>
              </Animated.View>
            )}
          </>
        </View>

        {/** Children Content */}
        <View style={[contentStyle]}>{children}</View>
      </View>

      {subNav && <SeaSubNav subNav={subNav} subNavType={subNavType} />}
    </View>
  )
}

interface SeaPageCardContentSectionProps {
  children: React.ReactNode
  contentStyle?: ViewStyle
}

/**
 * This component is used to wrap the content of a page card. It is used to add padding and margin to the content.
 * Handles Desktop, Tablet and Mobile styles
 *
 * @param children
 * @param contentStyle
 */
export const SeaPageCardContentSection = ({ children, contentStyle }: SeaPageCardContentSectionProps) => {
  const { isMobileWidth } = useDeviceWidth()

  return (
    <View
      style={[
        styles.defaultHorizontalPadding,
        styles.defaultVerticalPadding,
        isMobileWidth && styles.mobileHorizontalPadding,
        isMobileWidth && styles.mobileVerticalPadding,
        contentStyle,
      ]}>
      {children}
    </View>
  )
}

interface SeaPageCardTitleProps {
  title?: string
  additionalElements?: ReactElement
  files?: string[]
  customTitleRow?: ReactElement
}

/**
 * This component is used to render the title of a page card.
 * It has a default placeholder for the title elements
 *
 * @constructor
 */
export const SeaPageCardTitle = ({ title, additionalElements, files, customTitleRow }: SeaPageCardTitleProps) => {
  const { isMobileWidth } = useDeviceWidth()

  const dimension = isMobileWidth ? 60 : 80

  return (
    <>
      {customTitleRow ??
        (title || files ? (
          <SeaStack direction={'row'} justify={'start'} gap={isMobileWidth ? 15 : 20}>
            {files && (
              <View style={{ borderRadius: 12, width: dimension }}>
                <SeaFileImage
                  files={files}
                  style={{ height: dimension, width: dimension }}
                  mustGetDefaultImage={false}
                />
              </View>
            )}
            {title && (
              <View
                style={{
                  flexWrap: 'wrap',
                  flexShrink: 1,
                }}>
                <SeaTypography variant="title" textStyle={styles.title} containerStyle={{ marginBottom: 0 }}>
                  {title}
                </SeaTypography>
                {additionalElements}
              </View>
            )}
          </SeaStack>
        ) : (
          <></>
        ))}
    </>
  )
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor: theme.colors.white,
    borderRadius: 26,
    marginBottom: 20,
  },
  mobileContainer: {
    borderBottomRightRadius: 0,
    borderBottomLeftRadius: 0,
  },
  defaultVerticalPadding: {
    paddingVertical: 20,
  },
  defaultHorizontalPadding: {
    paddingHorizontal: 20,
  },
  mobileVerticalPadding: {
    paddingVertical: 10,
  },
  mobileHorizontalPadding: {
    paddingHorizontal: 10,
  },
  header: {
    marginBottom: 14,
  },
  title: {
    marginBottom: 0,
  },
  subNav: {
    paddingHorizontal: 20,
    paddingVertical: 0,
    borderTopColor: theme.colors.borderColor,
    borderTopWidth: 2,
  },
  subNavWithButtons: {
    paddingVertical: 20,
  },
  subNavScrollable: {
    columnGap: 20,
  },
  subNavScrollableWithButtons: {
    columnGap: 10,
  },
  mobileSubNav: {
    paddingHorizontal: 12,
  },
  subNavButton: {
    paddingVertical: 10,
    paddingTop: 13,
    borderBottomWidth: 3,
    borderBottomColor: 'transparent',
  },
  subNavButtonActive: {
    fontWeight: '500',
    color: theme.colors.black,
    borderBottomWidth: 3,
    borderBottomColor: theme.colors.primary,
  },
})
