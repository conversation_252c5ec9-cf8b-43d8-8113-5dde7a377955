import SeaHorizontalStackedBarGraph from '@src/components/_atoms/_reporting/_graphs/SeaHorizontalStackedBarGraph/SeaHorizontalStackedBarGraph'
import reporting from '@src/lib/reporting'
import { renderVesselName } from '@src/shared-state/Core/vessels'
import { GraphData } from '@src/components/_atoms/_reporting/_graphs/SeaHorizontalBarGraph/SeaHorizontalBarGraph'
import { useMemo } from 'react'
import { Incident } from '@src/shared-state/HealthSafety/incidents'

export const IncidentsByVesselGraph = ({
  selectedVesselIds,
  filteredCurrentIncidents,
  selectedIncidentStatusNames,
  selectedIncidentStatuses,
  onClick,
}: {
  selectedVesselIds: string[]
  filteredCurrentIncidents: Incident[] | undefined
  selectedIncidentStatusNames: (string | undefined)[]
  selectedIncidentStatuses: string[]
  onClick?: () => void
}) => {
  const currentIncidentsGraphData = useMemo(() => {
    if (filteredCurrentIncidents && selectedVesselIds) {
      const array: GraphData[] = []
      const byVesselId: {
        [vesselId: string]: GraphData
      } = {}
      const indexByStatus = {
        draft: 0,
        forReview: 1,
        inReview: 2,
        completed: 3,
      }
      selectedVesselIds.forEach((vesselId: string) => {
        const item = {
          name: renderVesselName(vesselId),
          values: [0, 0, 0, 0],
        }
        byVesselId[vesselId] = item
        array.push(item)
      })
      filteredCurrentIncidents.forEach(incident => {
        if (selectedIncidentStatuses.indexOf(incident.state) >= 0 && byVesselId[incident.vesselId]) {
          byVesselId[incident.vesselId].values![indexByStatus[incident.state]]++
        }
      })
      return array
    }
    return undefined
  }, [filteredCurrentIncidents, selectedVesselIds, selectedIncidentStatuses])

  return (
    <SeaHorizontalStackedBarGraph
      n={1}
      title="Current Incidents/Events by Vessel"
      subTitle="(Live Status)"
      mode="dashboard"
      // visible={visible}
      // onClick={(e) => {
      //   setModalToShow('incidentStatus');
      // }}
      onClick={onClick}
      data={currentIncidentsGraphData}
      units="Events/Incidents"
      categories={selectedIncidentStatusNames}
      colourPalette={reporting.colours.incidentStatuses}
    />
  )
}
