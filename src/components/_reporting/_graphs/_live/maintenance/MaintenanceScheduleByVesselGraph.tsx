import { useCallback, useMemo } from 'react'
import { GraphData } from '@src/components/_atoms/_reporting/_graphs/SeaHorizontalBarGraph/SeaHorizontalBarGraph'
import { renderVesselName } from '@src/shared-state/Core/vessels'
import { sharedState } from '@src/shared-state/shared-state'
import reporting from '@src/lib/reporting'
import SeaHorizontalStackedBarGraph from '@src/components/_atoms/_reporting/_graphs/SeaHorizontalStackedBarGraph/SeaHorizontalStackedBarGraph'
import { ScheduledMaintenanceTask } from '@src/shared-state/VesselMaintenance/maintenanceSchedule'

export const MaintenanceScheduleByVesselGraph = ({
  selectedVesselIds,
  scheduledMaintenanceTasks,
  selectedTaskStatusNames,
  onClick,
}: {
  selectedVesselIds: string[]
  scheduledMaintenanceTasks: ScheduledMaintenanceTask[]
  selectedTaskStatusNames: (string | undefined)[]
  onClick?: () => void
}) => {
  const today = sharedState.today.use()!

  const maintenanceTasksGraphData = useMemo(() => {
    if (scheduledMaintenanceTasks && selectedVesselIds) {
      const array: GraphData[] = []
      const byVesselId: {
        [vesselId: string]: GraphData
      } = {}
      const indexByStatus = {
        overdue: 0,
        upcoming: 1,
      }

      selectedVesselIds.forEach((vesselId: string) => {
        const item = {
          name: renderVesselName(vesselId),
          values: [0, 0],
        }
        byVesselId[vesselId] = item
        array.push(item)
      })

      scheduledMaintenanceTasks.forEach(item => {
        if (byVesselId[item.vesselId]) {
          if (item.dateDue && item.dateDue < today) {
            // Overdue item
            byVesselId[item.vesselId].values![indexByStatus.overdue]++
          } else {
            // Upcoming item
            byVesselId[item.vesselId].values![indexByStatus.upcoming]++
          }
        }
      })
      return array
    }
    return undefined
  }, [scheduledMaintenanceTasks, selectedVesselIds, today])

  return (
    <SeaHorizontalStackedBarGraph
      n={1}
      title={'Maintenance Schedule Tasks by Vessel'}
      subTitle="(Live Status)"
      mode="dashboard"
      // visible={visible}
      onClick={onClick}
      data={maintenanceTasksGraphData}
      units="Tasks"
      categories={selectedTaskStatusNames}
      colourPalette={reporting.colours.taskStatuses}
    />
  )
}
