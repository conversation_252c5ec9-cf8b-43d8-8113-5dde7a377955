import SeaHorizontalStackedBarGraph from '@src/components/_atoms/_reporting/_graphs/SeaHorizontalStackedBarGraph/SeaHorizontalStackedBarGraph'
import reporting from '@src/lib/reporting'
import { GraphData } from '@src/components/_atoms/_reporting/_graphs/SeaHorizontalBarGraph/SeaHorizontalBarGraph'
import { renderVesselName } from '@src/shared-state/Core/vessels'
import { useMemo } from 'react'
import { Job, jobPriorities } from '@src/shared-state/VesselMaintenance/jobs'

export const JobListByVesselGraph = ({
  selectedVesselIds,
  filteredJobs,
  selectedJobPriorityNames,
  onClick,
}: {
  selectedVesselIds: string[]
  filteredJobs: Job[] | undefined
  selectedJobPriorityNames: (string | undefined)[]
  onClick?: () => void
}) => {
  const jobsGraphData = useMemo(() => {
    if (filteredJobs && selectedVesselIds) {
      const array: GraphData[] = []
      const byVesselId: {
        [vesselId: string]: GraphData
      } = {}
      const jobPriorityIds = Object.keys(jobPriorities)

      selectedVesselIds.forEach((vesselId: string) => {
        const item = {
          name: renderVesselName(vesselId),
          values: jobPriorityIds.map(() => {
            return 0
          }),
        }
        byVesselId[vesselId] = item
        array.push(item)
      })

      filteredJobs.forEach(item => {
        if (byVesselId[item.vesselId]) {
          byVesselId[item.vesselId].values![jobPriorityIds.indexOf(item.priority)]++
        }
      })
      return array
    }
    return undefined
  }, [filteredJobs, selectedVesselIds])

  return (
    <SeaHorizontalStackedBarGraph
      n={3}
      title={'Job List Tasks by Vessel'}
      subTitle="(Live Status)"
      mode="dashboard"
      // visible={visible}
      // onClick={(e) => {
      //   setModalToShow("jobPriorities");
      // }}
      onClick={onClick}
      data={jobsGraphData}
      units="Jobs"
      categories={selectedJobPriorityNames}
      colourPalette={reporting.colours.jobPriorities}
    />
  )
}
