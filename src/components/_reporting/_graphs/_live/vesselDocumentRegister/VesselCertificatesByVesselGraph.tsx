import { sharedState } from '@src/shared-state/shared-state'
import { useMemo, useState } from 'react'
import { taskStatuses } from '@src/lib/util'
import { useReportingVesselCertificates } from '@src/shared-state/Reporting/DocumentReporting/useReportingVesselCertificates'
import { GraphData } from '@src/components/_atoms/_reporting/_graphs/SeaHorizontalBarGraph/SeaHorizontalBarGraph'
import { renderVesselName } from '@src/shared-state/Core/vessels'
import SeaHorizontalStackedBarGraph from '@src/components/_atoms/_reporting/_graphs/SeaHorizontalStackedBarGraph/SeaHorizontalStackedBarGraph'
import reporting from '@src/lib/reporting'
import { PostFetchAction } from '@src/app/(home)/(wrapper)/reporting/_layout'
import { VesselCertificate } from '@src/shared-state/VesselDocuments/vesselCertificates'

interface VesselCertificatesByVesselGraphProps {
  selectedVesselIds: string[]
  filteredVesselCertificates: VesselCertificate[] | undefined
  selectedStatusNames: (string | undefined)[]
  onClick?: () => void
}

export const VesselCertificatesByVesselGraph = ({
  selectedVesselIds,
  filteredVesselCertificates,
  selectedStatusNames,
  onClick,
}: VesselCertificatesByVesselGraphProps) => {
  const today = sharedState.today.use()!

  const vesselCertificatesGraphData = useMemo(() => {
    if (filteredVesselCertificates && selectedVesselIds) {
      const array = [] as GraphData[]
      const byVesselId: {
        [vesselId: string]: GraphData
      } = {}
      const indexByStatus = {
        overdue: 0,
        upcoming: 1,
      }

      selectedVesselIds.forEach((vesselId: string) => {
        const item = {
          name: renderVesselName(vesselId),
          values: [0, 0],
        }
        byVesselId[vesselId] = item
        array.push(item)
      })

      filteredVesselCertificates.forEach(item => {
        if (byVesselId[item.vesselId]) {
          if (item.dateExpires && item.dateExpires < today) {
            // Overdue item
            byVesselId[item.vesselId].values![indexByStatus.overdue]++
          } else {
            // Upcoming item
            byVesselId[item.vesselId].values![indexByStatus.upcoming]++
          }
        }
      })
      return array
    }
    return undefined
  }, [filteredVesselCertificates, selectedVesselIds, today])

  return (
    <SeaHorizontalStackedBarGraph
      n={1}
      title={'Vessel Certificates by Vessel'}
      subTitle="(Live Status)"
      mode="dashboard"
      visible={true}
      onClick={onClick}
      data={vesselCertificatesGraphData}
      units="Tasks"
      categories={selectedStatusNames}
      colourPalette={reporting.colours.taskStatuses}
    />
  )
}
