import { useMemo } from 'react'
import { GraphData } from '@src/components/_atoms/_reporting/_graphs/SeaHorizontalBarGraph/SeaHorizontalBarGraph'
import { renderVesselName } from '@src/shared-state/Core/vessels'
import SeaHorizontalStackedBarGraph from '@src/components/_atoms/_reporting/_graphs/SeaHorizontalStackedBarGraph/SeaHorizontalStackedBarGraph'
import reporting from '@src/lib/reporting'
import { MaintenanceTaskCompleted } from '@src/shared-state/VesselMaintenance/maintenanceTasksCompleted'

export const CompletedMaintenanceTasksByVesselGraph = ({
  selectedVesselIds,
  completedMaintenanceTasks,
  selectedCompletedTaskTypeNames,
  onClick,
}: {
  selectedVesselIds: string[]
  completedMaintenanceTasks: MaintenanceTaskCompleted[] | undefined
  selectedCompletedTaskTypeNames: (string | undefined)[]
  onClick?: () => void
}) => {
  const completedMaintenanceTasksGraphData = useMemo(() => {
    if (completedMaintenanceTasks && selectedVesselIds) {
      const array: GraphData[] = []
      const byVesselId: {
        [vesselId: string]: GraphData
      } = {}
      const indexByStatus = {
        scheduled: 0,
        unscheduled: 1,
      }

      selectedVesselIds.forEach((vesselId: string) => {
        const item = {
          name: renderVesselName(vesselId),
          values: [0, 0],
        }
        byVesselId[vesselId] = item
        array.push(item)
      })

      completedMaintenanceTasks.forEach(item => {
        if (byVesselId[item.vesselId]) {
          if (item.type === 'scheduled') {
            // Scheduled
            byVesselId[item.vesselId].values![indexByStatus.scheduled] += item.timesCompleted || 0
          } else {
            // Unscheduled / job
            byVesselId[item.vesselId].values![indexByStatus.unscheduled]++
          }
        }
      })

      return array
    }
    return undefined
  }, [completedMaintenanceTasks, selectedVesselIds])

  return (
    <SeaHorizontalStackedBarGraph
      n={2}
      title={'Completed Maintenance Tasks by Vessel'}
      // subTitle={dateRangeSubTitle}
      subTitle={'Date Range DEFAULT'}
      mode="dashboard"
      // visible={visible}
      // onClick={e => {
      //   setModalToShow('completedTasks')
      // }}
      onClick={onClick}
      data={completedMaintenanceTasksGraphData}
      units="Tasks"
      categories={selectedCompletedTaskTypeNames}
      colourPalette={reporting.colours.completedMaintenanceTasks}
    />
  )
}
