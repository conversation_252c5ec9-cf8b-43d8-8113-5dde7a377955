import { useMemo } from 'react'
import SeaHorizontalBarGraph, {
  GraphData,
} from '@src/components/_atoms/_reporting/_graphs/SeaHorizontalBarGraph/SeaHorizontalBarGraph'
import { renderVesselName } from '@src/shared-state/Core/vessels'
import { colours } from '@src/lib/reporting'
import { SafetyCheckCompleted } from '@src/shared-state/VesselSafety/useCompletedSafetyCheckItems'
import { useSafetyReportSettings } from '@src/app/(home)/(wrapper)/reporting/reportingSafety/_layout'
import { useRouter } from 'expo-router'
import { Routes } from '@src/navigation/constants'
import { getRoutePath } from '@src/navigation/utils'

export const CompletedSafetyCheckByVesselGraph = <T extends SafetyCheckCompleted>({
  selectedVesselIds,
  completedSafetyChecks,
  onClick,
}: {
  selectedVesselIds: string[]
  completedSafetyChecks: T[] | undefined
  onClick?: () => void
}) => {
  const completedSafetyChecksGraphData = useMemo(() => {
    if (selectedVesselIds.length === 0) {
      // Nothing to show
      return []
    }
    if (completedSafetyChecks) {
      const array: GraphData[] = []
      const byVesselId: {
        [vesselId: string]: GraphData
      } = {}

      selectedVesselIds.forEach((vesselId: string) => {
        const item = {
          name: renderVesselName(vesselId),
          value: 0,
        }
        byVesselId[vesselId] = item
        array.push(item)
      })

      completedSafetyChecks.forEach(item => {
        if (byVesselId[item.vesselId]) {
          byVesselId[item.vesselId].value! += item.timesCompleted || 0
        }
      })

      return array
    }
    return undefined
  }, [completedSafetyChecks, selectedVesselIds])

  return (
    <SeaHorizontalBarGraph
      n={3}
      title={'Completed Safety Check Tasks'}
      // subTitle={dateRangeSubTitle}
      subTitle={'Date Range DEFAULT'}
      mode="dashboard"
      // visible={visible}
      onClick={onClick}
      data={completedSafetyChecksGraphData}
      sortData={true}
      units="Tasks Completed"
      colourPalette={[colours.mint]}
      hashNamesForColours={false}
    />
  )
}
