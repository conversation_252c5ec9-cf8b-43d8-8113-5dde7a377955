import SeaPieGraph from '@src/components/_atoms/_reporting/_graphs/SeaPieGraph/SeaPieGraph'
import { useMemo } from 'react'
import { GraphData } from '@src/components/_atoms/_reporting/_graphs/SeaHorizontalBarGraph/SeaHorizontalBarGraph'
import { Incident } from '@src/shared-state/HealthSafety/incidents'

const incidentTypes: Record<string, string> = {
  incident: 'Incident',
  injury: 'Injury',
  nearMiss: 'Near Miss',
  illness: 'Illness',
  event: 'Event (General)',
}

export const IncidentTypesByVesselGraph = ({
  filteredIncidents,
  onClick,
}: {
  filteredIncidents: Incident[] | undefined
  onClick?: () => void
}) => {
  const incidentTypesGraphData = useMemo(() => {
    if (filteredIncidents) {
      const totals: {
        [key: string]: number
      } = {}
      Object.keys(incidentTypes).forEach((key: string) => {
        totals[key] = 0
      })
      filteredIncidents.forEach(incident => {
        totals[incident.type]++
      })
      // Convert to format SeaPieGraph expects
      const data: GraphData[] = []
      Object.keys(incidentTypes).forEach(key => {
        data.push({
          name: incidentTypes[key],
          value: totals[key],
        })
      })
      return data
    }
    return undefined
  }, [filteredIncidents])

  return (
    <SeaPieGraph
      n={2}
      title="Incident/Event Types"
      // subTitle={dateRangeSubTitle}
      subTitle={'Date Range DEFAULT'}
      mode="dashboard"
      // visible={visible}
      // onClick={e => {
      //   //setShowViewIncidentTypesReport(true);
      //   setModalToShow('incidentTypes')
      // }}
      onClick={onClick}
      data={incidentTypesGraphData}
      showAllLabels={true}
    />
  )
}
