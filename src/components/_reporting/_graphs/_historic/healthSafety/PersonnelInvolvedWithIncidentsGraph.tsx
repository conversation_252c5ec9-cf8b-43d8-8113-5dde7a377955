import { sharedState } from '@src/shared-state/shared-state'
import { Incident } from '@src/shared-state/HealthSafety/incidents'
import SeaPieGraph from '@src/components/_atoms/_reporting/_graphs/SeaPieGraph/SeaPieGraph'
import { useMemo } from 'react'
import { GraphData } from '@src/components/_atoms/_reporting/_graphs/SeaHorizontalBarGraph/SeaHorizontalBarGraph'

const incidentWhoInvolvedTypes: Record<string, string> = {
  employee: 'Employee(s)',
  contractor: 'Contractor(s)',
  trainnee: 'Trainnee(s)',
  volunteer: 'Volunteer(s)',
  guest: 'Guest(s)',
  public: 'Member(s) of the public',
}

export const PersonnelInvolvedWithIncidentsGraph = ({
  filteredIncidents,
  onClick,
}: {
  filteredIncidents: Incident[] | undefined
  onClick?: () => void
}) => {
  const incidentWhoInvolvedGraphData = useMemo(() => {
    if (filteredIncidents) {
      const totals: {
        [key: string]: number
      } = {}
      Object.keys(incidentWhoInvolvedTypes).forEach((type: string) => {
        totals[type] = 0
      })
      filteredIncidents.forEach(incident => {
        incident?.whoInvolvedTypes?.forEach((type: string) => {
          totals[type]++
        })
      })
      // Convert to format SeaPieGraph expects
      const data: GraphData[] = []
      Object.keys(incidentWhoInvolvedTypes).forEach((type: string) => {
        data.push({
          name: incidentWhoInvolvedTypes[type],
          value: totals[type],
        })
      })
      return data
    }
    return undefined
  }, [filteredIncidents])

  return (
    <SeaPieGraph
      n={8}
      title="Personnel Involved with Incidents/Events"
      // subTitle={dateRangeSubTitle}
      subTitle={'Date Range DEFAULT'}
      mode="dashboard"
      // visible={visible}
      // onClick={(e) => {
      //   setModalToShow('incidentPersonnelInvolved');
      // }}
      onClick={onClick}
      data={incidentWhoInvolvedGraphData}
      showAllLabels={true}
    />
  )
}
