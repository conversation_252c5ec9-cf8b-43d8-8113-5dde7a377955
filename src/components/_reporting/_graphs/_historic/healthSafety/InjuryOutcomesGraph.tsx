import SeaPieGraph from '@src/components/_atoms/_reporting/_graphs/SeaPieGraph/SeaPieGraph'
import { useMemo } from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { GraphData } from '@src/components/_atoms/_reporting/_graphs/SeaHorizontalBarGraph/SeaHorizontalBarGraph'
import { Incident } from '@src/shared-state/HealthSafety/incidents'

const injuryConclusions: Record<string, string> = {
  none: 'None',
  basicFirstAid: 'Basic First Aid',
  doctor: 'Doctor - No lost work time injury',
  doctorLost: 'Doctor - Lost work time injury',
  hospital: 'Hospitalisation - No lost work time injury',
  hospitalLost: 'Hospitalisation - Lost work time injury',
}

export const InjuryOutcomesGraph = ({
  filteredIncidents,
  onClick,
}: {
  filteredIncidents: Incident[] | undefined
  onClick?: () => void
}) => {
  const incidentReviews = sharedState.incidentReviews.use()

  const incidentInjuryConclusionsGraphData = useMemo(() => {
    if (filteredIncidents && incidentReviews?.byId) {
      const totals: {
        [key: string]: number
      } = {}
      Object.keys(injuryConclusions).forEach((conclusion: string) => {
        totals[conclusion] = 0
      })
      filteredIncidents.forEach(incident => {
        if (incident.state === 'completed') {
          const review = incidentReviews.byId[incident.id]
          review?.injuryConclusions?.forEach((conclusion: string) => {
            totals[conclusion]++
          })
        }
      })
      // Convert to format SeaPieGraph expects
      const data: GraphData[] = []
      Object.keys(injuryConclusions).forEach((conclusion: string) => {
        data.push({
          name: injuryConclusions[conclusion],
          value: totals[conclusion],
        })
      })
      return data
    }
    return undefined
  }, [filteredIncidents, incidentReviews])

  return (
    <SeaPieGraph
      n={9}
      title="Injury Outcomes"
      // subTitle={dateRangeSubTitle}
      subTitle={'Date Range DEFAULT'}
      mode="dashboard"
      // visible={visible}
      // onClick={(e) => {
      //   setModalToShow('incidentInjuryOutcomes');
      // }}
      onClick={onClick}
      data={incidentInjuryConclusionsGraphData}
      showAllLabels={true}
    />
  )
}
