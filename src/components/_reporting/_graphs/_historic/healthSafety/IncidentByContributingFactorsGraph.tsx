import SeaHorizontalBarGraph, {
  GraphData,
} from '@src/components/_atoms/_reporting/_graphs/SeaHorizontalBarGraph/SeaHorizontalBarGraph'
import { colours } from '@src/lib/reporting'
import { useMemo } from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { Incident } from '@src/shared-state/HealthSafety/incidents'

export const IncidentByContributingFactorsGraph = ({
  filteredIncidents,
  onClick,
}: {
  filteredIncidents: Incident[] | undefined
  onClick?: () => void
}) => {
  const incidentCauses = sharedState.incidentCauses.use()

  const filteredContributingFactorIncidents = useMemo(() => {
    if (filteredIncidents) {
      const filtered: Incident[] = []
      filteredIncidents.forEach(incident => {
        if (incident.causeIds?.length > 0) {
          filtered.push(incident)
        }
      })
      return filtered
    }
    return undefined
  }, [filteredIncidents])

  const incidentContributingFactorsGraphData = useMemo(() => {
    if (filteredContributingFactorIncidents && incidentCauses?.ids) {
      const data: GraphData[] = []
      const totals: {
        [key: string]: number
      } = {}
      incidentCauses.ids.forEach((causeId: string) => {
        totals[causeId] = 0
      })
      filteredContributingFactorIncidents.forEach(incident => {
        incident.causeIds?.forEach((causeId: string) => {
          totals[causeId]++
        })
      })
      // Convert to format SeaHorizontalBarGraph expects
      incidentCauses.ids.forEach((causeId: string) => {
        data.push({
          name: incidentCauses.byId[causeId].name,
          value: totals[causeId],
        })
      })
      return data
    }
    return undefined
  }, [filteredContributingFactorIncidents, incidentCauses])

  return (
    <SeaHorizontalBarGraph
      n={4}
      title="Incident/Event Contributing Factors"
      // subTitle={dateRangeSubTitle}
      subTitle={'Date Range DEFAULT'}
      mode="dashboard"
      // visible={visible}
      // onClick={(e) => {
      //   setModalToShow('contributingFactors');
      // }}
      onClick={onClick}
      data={incidentContributingFactorsGraphData}
      sortData={true}
      units="Events / Incidents"
      colourPalette={[colours.skyBlue]}
      hashNamesForColours={false}
      yLabelWidth={250}
    />
  )
}
