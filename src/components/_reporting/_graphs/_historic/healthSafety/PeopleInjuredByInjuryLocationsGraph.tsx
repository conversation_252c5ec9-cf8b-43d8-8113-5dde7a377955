import { sharedState } from '@src/shared-state/shared-state'
import { Incident } from '@src/shared-state/HealthSafety/incidents'
import { useMemo } from 'react'
import SeaHorizontalBarGraph, {
  GraphData,
} from '@src/components/_atoms/_reporting/_graphs/SeaHorizontalBarGraph/SeaHorizontalBarGraph'
import { colours } from '@src/lib/reporting'

export const PeopleInjuredByInjuryLocationsGraph = ({
  filteredIncidents,
  onClick,
}: {
  filteredIncidents: Incident[] | undefined
  onClick?: () => void
}) => {
  const injuryLocations = sharedState.injuryLocations.use()

  const filteredLocationIncidents = useMemo(() => {
    if (filteredIncidents) {
      const filtered: Incident[] = []
      filteredIncidents.forEach(incident => {
        if (incident.injuries?.length && incident.injuries?.length > 0) {
          for (let i = 0; i < incident.injuries.length; i++) {
            if (incident.injuries[i].locationIds?.length && (incident.injuries[i].locationIds?.length as number) > 0) {
              filtered.push(incident)
              break
            }
          }
        }
      })
      return filtered
    }
    return undefined
  }, [filteredIncidents])

  const incidentLocationsGraphData = useMemo(() => {
    if (filteredLocationIncidents && injuryLocations?.ids) {
      const data: GraphData[] = []
      const totals: {
        [key: string]: number
      } = {}
      injuryLocations.ids.forEach((locationId: string) => {
        totals[locationId] = 0
      })
      filteredLocationIncidents.forEach(incident => {
        incident.injuries?.forEach(injury => {
          injury.locationIds?.forEach((locationId: string) => {
            totals[locationId]++
          })
        })
      })
      // Convert to format SeaHorizontalBarGraph expects
      injuryLocations.ids.forEach((locationId: string) => {
        data.push({
          name: injuryLocations.byId[locationId].name,
          value: totals[locationId],
        })
      })
      return data
    }
    return undefined
  }, [filteredLocationIncidents, injuryLocations])

  return (
    <SeaHorizontalBarGraph
      n={6}
      title="Injury Locations"
      // subTitle={dateRangeSubTitle}
      subTitle={'Date Range DEFAULT'}
      mode="dashboard"
      // visible={visible}
      // onClick={(e) => {
      //   setModalToShow('injuryLocations');
      // }}
      onClick={onClick}
      data={incidentLocationsGraphData}
      sortData={true}
      units="People Injured"
      colourPalette={[colours.lavender]}
      hashNamesForColours={false}
      yLabelWidth={200}
    />
  )
}
