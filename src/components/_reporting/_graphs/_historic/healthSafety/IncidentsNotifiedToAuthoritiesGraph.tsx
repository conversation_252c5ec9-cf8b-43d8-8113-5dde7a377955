import { sharedState } from '@src/shared-state/shared-state'
import SeaPieGraph from '@src/components/_atoms/_reporting/_graphs/SeaPieGraph/SeaPieGraph'
import { useMemo } from 'react'
import { GraphData } from '@src/components/_atoms/_reporting/_graphs/SeaHorizontalBarGraph/SeaHorizontalBarGraph'
import { Incident } from '@src/shared-state/HealthSafety/incidents'

const authoritiesNotifiedForReviewByRegion: Record<string, string[]> = {
  au: ['n', 'amsa', 'whs', 'amsaWhs'],
  nz: ['n', 'mnz', 'ws', 'mnzWs'],
  uk: ['n', 'mca', 'hse', 'miab', 'mcaHse', 'mcaMiab', 'hseMiab', 'mcaHseMiab'],
  default: ['n', 'mca', 'hse', 'miab', 'mcaHse', 'mcaMiab', 'hseMiab', 'mcaHseMiab'],
}

export const authoritiesNotifiedForReviewLabels: Record<string, string> = {
  n: 'Not notified',
  y: 'Notified',
  amsa: 'AMSA',
  whs: 'WHS',
  amsaWhs: 'AMSA and WHS',
  mnz: 'Maritime New Zealand',
  ws: 'Work Safe',
  mnzWs: 'MNZ and WS',
  mca: 'MCA',
  hse: 'HSE',
  miab: 'MAIB',
  mcaHse: 'MCA and HSE',
  mcaMiab: 'MCA and MAIB',
  hseMiab: 'HSE and MAIB',
  mcaHseMiab: 'MCA, HSE, and MAIB',
}

export const IncidentsNotifiedToAuthoritiesGraph = ({
  filteredIncidents,
  onClick,
}: {
  filteredIncidents: Incident[] | undefined
  onClick?: () => void
}) => {
  const incidentReviews = sharedState.incidentReviews.use()
  const licenseeSettings = sharedState.licenseeSettings.use()

  const notifiableIncidentsGraphData = useMemo(() => {
    if (filteredIncidents && incidentReviews?.byId && licenseeSettings) {
      const totals: {
        [key: string]: number
      } = {}
      const notifiableTypes = authoritiesNotifiedForReviewByRegion[licenseeSettings.region]
      notifiableTypes.forEach((type: string) => {
        totals[type] = 0
      })
      filteredIncidents.forEach(incident => {
        if (incident.state === 'completed') {
          const review = incidentReviews.byId[incident.id]
          if (review?.notifiedAuthorities) {
            totals[review.notifiedAuthorities]++
          }
        }
      })
      // Convert to format SeaPieGraph expects
      const data: GraphData[] = []
      notifiableTypes.forEach((type: string) => {
        data.push({
          name: authoritiesNotifiedForReviewLabels[type],
          value: totals[type],
        })
      })
      return data
    }
    return undefined
  }, [filteredIncidents, incidentReviews, licenseeSettings])

  return (
    <SeaPieGraph
      n={7}
      title="Incidents/Events Notified to Authorities"
      // subTitle={dateRangeSubTitle}
      subTitle={'Date Range DEFAULT'}
      mode="dashboard"
      // visible={visible}
      // onClick={(e) => {
      //   setModalToShow('incidentsNotified');
      // }}
      onClick={onClick}
      data={notifiableIncidentsGraphData}
      showAllLabels={true}
    />
  )
}
