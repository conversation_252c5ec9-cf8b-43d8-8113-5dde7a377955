import SeaHorizontalBarGraph, {
  GraphData,
} from '@src/components/_atoms/_reporting/_graphs/SeaHorizontalBarGraph/SeaHorizontalBarGraph'
import { colours } from '@src/lib/reporting'
import { useMemo } from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { Incident } from '@src/shared-state/HealthSafety/incidents'

export const PeopleInjuredByInjuryTypesGraph = ({
  filteredIncidents,
  onClick,
}: {
  filteredIncidents: Incident[] | undefined
  onClick?: () => void
}) => {
  const injuryTypes = sharedState.injuryTypes.use()

  const filteredInjuryTypeIncidents = useMemo(() => {
    if (filteredIncidents) {
      const filtered: Incident[] = []
      filteredIncidents.forEach(incident => {
        if (incident.injuries && incident.injuries?.length > 0) {
          for (let i = 0; i < incident.injuries.length; i++) {
            if (incident.injuries[i].typeIds?.length && (incident.injuries[i].typeIds?.length as number) > 0) {
              filtered.push(incident)
              break
            }
          }
        }
      })
      return filtered
    }
    return undefined
  }, [filteredIncidents])

  const incidentInjuryTypesGraphData = useMemo(() => {
    if (filteredInjuryTypeIncidents && injuryTypes?.ids) {
      const data: GraphData[] = []
      const totals: {
        [key: string]: number
      } = {}
      injuryTypes.ids.forEach((typeId: string) => {
        totals[typeId] = 0
      })
      filteredInjuryTypeIncidents.forEach(incident => {
        incident.injuries?.forEach(injury => {
          injury.typeIds?.forEach((typeId: string) => {
            totals[typeId]++
          })
        })
      })
      // Convert to format SeaHorizontalBarGraph expects
      injuryTypes.ids.forEach((typeId: string) => {
        data.push({
          name: injuryTypes.byId[typeId].name,
          value: totals[typeId],
        })
      })
      return data
    }
    return undefined
  }, [filteredInjuryTypeIncidents, injuryTypes])

  return (
    <SeaHorizontalBarGraph
      n={5}
      title="Injury Types"
      // subTitle={dateRangeSubTitle}
      subTitle={'Date Range DEFAULT'}
      mode="dashboard"
      // visible={visible}
      // onClick={(e) => {
      //   setModalToShow('injuryTypes');
      // }}
      onClick={onClick}
      data={incidentInjuryTypesGraphData}
      sortData={true}
      units="People Injured"
      colourPalette={[colours.orangeSoda]}
      hashNamesForColours={false}
      yLabelWidth={200}
    />
  )
}
