import SeaPieGraph from '@src/components/_atoms/_reporting/_graphs/SeaPieGraph/SeaPieGraph'
import { useMemo } from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { GraphData } from '@src/components/_atoms/_reporting/_graphs/SeaHorizontalBarGraph/SeaHorizontalBarGraph'
import { Incident } from '@src/shared-state/HealthSafety/incidents'

export const IncidentByCategoriesGraph = ({
  filteredIncidents,
  onClick,
}: {
  filteredIncidents: Incident[] | undefined
  onClick?: () => void
}) => {
  const incidentCategories = sharedState.incidentCategories.use()

  const incidentCategoriesGraphData = useMemo(() => {
    if (filteredIncidents && incidentCategories?.byId) {
      const totals: {
        [key: string]: number
      } = {}
      const categoryIds = [] as string[]
      incidentCategories.ids.forEach((categoryId: string) => {
        if (incidentCategories.byId[categoryId].state === 'active') {
          categoryIds.push(categoryId)
          totals[categoryId] = 0
        }
      })
      filteredIncidents.forEach(incident => {
        if (incident.categoryId && totals[incident.categoryId] !== undefined) {
          totals[incident.categoryId]++
        }
      })
      // Convert to format SeaPieGraph expects
      const data: GraphData[] = []
      categoryIds.forEach(key => {
        data.push({
          name: incidentCategories.byId[key].name,
          value: totals[key],
        })
      })
      return data
    }
    return undefined
  }, [filteredIncidents, incidentCategories])

  return (
    <SeaPieGraph
      n={3}
      title="Incident/Event Categories"
      // subTitle={dateRangeSubTitle}
      subTitle={'Date Range DEFAULT'}
      mode="dashboard"
      // visible={visible}
      // onClick={(e) => {
      //   setModalToShow('incidentCategories');
      // }}
      onClick={onClick}
      data={incidentCategoriesGraphData}
      hashNamesForColours={true}
    />
  )
}
