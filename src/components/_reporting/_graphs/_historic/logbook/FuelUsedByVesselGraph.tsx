import { Voyage } from '@src/shared-state/VesselLogbook/voyages'
import { useMemo } from 'react'
import { GraphData } from '@src/components/_atoms/_reporting/_graphs/SeaHorizontalBarGraph/SeaHorizontalBarGraph'
import { renderVesselName } from '@src/shared-state/Core/vessels'
import { calcFuelUsedFromVoyage } from '@src/lib/util'
import SeaHorizontalStackedBarGraph from '@src/components/_atoms/_reporting/_graphs/SeaHorizontalStackedBarGraph/SeaHorizontalStackedBarGraph'
import reporting from '@src/lib/reporting'

interface FuelUsedByVesselGraphProps {
  voyages: Voyage[]
  vesselIds?: string[]
}

export const FuelUsedByVesselGraph = ({ voyages, vesselIds }: FuelUsedByVesselGraphProps) => {
  const currentFuelUsedGraphData = useMemo(() => {
    if (voyages && vesselIds) {
      const array: GraphData[] = []
      const byVesselId: {
        [vesselId: string]: GraphData
      } = {}
      const indexByVoyageType = {
        singleDay: 0,
        multiDay: 1,
        multiTrip: 2,
      }
      vesselIds.forEach((vesselId: string) => {
        const item = {
          name: renderVesselName(vesselId),
          values: [0, 0, 0],
        }
        byVesselId[vesselId] = item
        array.push(item)
      })
      voyages.forEach(voyage => {
        if (
          byVesselId[voyage.vesselId] &&
          indexByVoyageType[voyage.tripType as keyof typeof indexByVoyageType] !== undefined
        ) {
          const voyageIndex: number = indexByVoyageType[voyage.tripType as keyof typeof indexByVoyageType]
          console.log(
            `voyageIndex: ${voyageIndex}, voyage.fuelStart: ${voyage.fuelStart}, voyage.fuelEnd: ${voyage.fuelEnd}, voyage.fuelBunkered: ${voyage.fuelBunkered}`
          )
          byVesselId[voyage.vesselId].values![voyageIndex] += calcFuelUsedFromVoyage(voyage)
        }
      })
      return array
    }
  }, [voyages, vesselIds])

  return (
    <SeaHorizontalStackedBarGraph
      n={2}
      title={'Fuel Used by Vessel (L)'}
      // subTitle={dateRangeSubTitle}
      subTitle={'Date Range DEFAULT'}
      mode="dashboard"
      // visible={visible}
      // onClick={(e) => {
      //   setModalToShow('fuelUsed');
      // }}
      data={currentFuelUsedGraphData}
      units="Litres"
      categories={['Single Day', 'Multi Day', 'Multi Trip']}
      colourPalette={reporting.colours.default}
    />
  )
}
