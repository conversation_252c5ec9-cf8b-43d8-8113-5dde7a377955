import { Voyage } from '@src/shared-state/VesselLogbook/voyages'
import { useMemo } from 'react'
import { GraphData } from '@src/components/_atoms/_reporting/_graphs/SeaHorizontalBarGraph/SeaHorizontalBarGraph'
import { renderVesselName } from '@src/shared-state/Core/vessels'
import SeaHorizontalStackedBarGraph from '@src/components/_atoms/_reporting/_graphs/SeaHorizontalStackedBarGraph/SeaHorizontalStackedBarGraph'
import reporting from '@src/lib/reporting'

interface VoyagesByVesselGraphProps {
  voyages: Voyage[]
  vesselIds?: string[]
}

export const VoyagesByVesselGraph = ({ voyages, vesselIds }: VoyagesByVesselGraphProps) => {
  const currentVoyagesGraphData = useMemo(() => {
    if (voyages && vesselIds) {
      const array: GraphData[] = []
      const byVesselId: {
        [vesselId: string]: GraphData
      } = {}
      const indexByVoyageType = {
        singleDay: 0,
        multiDay: 1,
        multiTrip: 2,
      }
      vesselIds.forEach((vesselId: string) => {
        const item = {
          name: renderVesselName(vesselId),
          values: [0, 0, 0],
        }
        byVesselId[vesselId] = item
        array.push(item)
      })
      voyages.forEach(voyage => {
        // Updated to use filteredItems.byVesselId
        if (
          byVesselId[voyage.vesselId] &&
          indexByVoyageType[voyage.tripType as keyof typeof indexByVoyageType] !== undefined
        ) {
          const voyageIndex: number = indexByVoyageType[voyage.tripType as keyof typeof indexByVoyageType]
          byVesselId[voyage.vesselId].values![voyageIndex]++
        }
      })
      return array
    }
    return undefined
  }, [voyages, vesselIds])

  return (
    <SeaHorizontalStackedBarGraph
      n={1}
      title="Voyages by Vessel"
      subTitle={'Date Range DEFAULT'}
      mode="dashboard"
      // visible={visible}
      // onClick={(e) => {
      //   setModalToShow('logbookEntries');
      // }}
      data={currentVoyagesGraphData}
      units="Voyages"
      categories={['Single Day', 'Multi Day', 'Multi Trip']}
      colourPalette={reporting.colours.default}
    />
  )
}
