import SeaHorizontalBarGraph from '@src/components/_atoms/_reporting/_graphs/SeaHorizontalBarGraph/SeaHorizontalBarGraph'
import { useMemo } from 'react'
import { UserType } from '@src/shared-state/Core/user'
import { sharedState } from '@src/shared-state/shared-state'
import { useReportingActionLog } from '@src/shared-state/Reporting/CrewReporting/useReportingActionLog'
import { renderFullName, renderFullNameForUserId } from '@src/shared-state/Core/users'
import reporting, { colours } from '@src/lib/reporting'
import SeaHorizontalStackedBarGraph from '@src/components/_atoms/_reporting/_graphs/SeaHorizontalStackedBarGraph/SeaHorizontalStackedBarGraph'
import { ActionLogEntry } from '@src/shared-state/General/actionLog'

interface ActionLogsGraphProps {
  filteredActionLog: ActionLogEntry[] | undefined
  filteredUsers?: {
    list: UserType[]
    byId: {
      [userId: string]: UserType
    }
  }
}

export const ActionLogsGraph = ({ filteredActionLog, filteredUsers }: ActionLogsGraphProps) => {
  const actionLogGraphData = useMemo(() => {
    if (filteredActionLog && filteredUsers?.list) {
      const array = [] as { name: string; value: number }[]

      const byUserId: {
        [userId: string]: {
          name: string
          value: number
        }
      } = {}

      filteredUsers.list.forEach((u: UserType) => {
        const item = {
          name: renderFullName(u),
          value: 0,
        }
        byUserId[u.id as string] = item
        array.push(item)
      })

      filteredActionLog.forEach(action => {
        if (action.userId && !byUserId[action.userId]) {
          byUserId[action.userId] = {
            name: renderFullNameForUserId(action.userId),
            value: 0,
          }
        }
        byUserId[action.userId].value++
      })

      return array
    }
    return undefined
  }, [filteredActionLog, filteredUsers])

  return (
    <>
      <SeaHorizontalBarGraph
        // n={n++}
        n={2}
        title="Actions Logged by Crew Member"
        subTitle={'Date Range DEFAULT'}
        mode="dashboard"
        visible={true}
        // onClick={e => {
        //   setModalToShow('crewActionsLogged')
        // }}
        data={actionLogGraphData}
        sortData={true}
        units="Actions Logged"
        colourPalette={[colours.beige]}
        hashNamesForColours={false}
        yLabelWidth={140}
      />
    </>
  )
}
