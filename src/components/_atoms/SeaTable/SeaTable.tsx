import React, { Dispatch, ReactElement, ReactNode, useCallback, useMemo, useReducer, useRef, useState } from 'react'
import {
  FlatList,
  LayoutChangeEvent,
  Platform,
  Pressable,
  ScrollView,
  SectionList,
  StyleProp,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
  // Animated,
} from 'react-native'
import { FontAwesome6 } from '@expo/vector-icons'
import Animated, {
  interpolate,
  useAnimatedScrollHandler,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated'
import { SeaStatusType } from '@src/types/Common'
import { fontFamily } from '@src/theme/typography'
import { theme } from '@src/theme'
import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { SeaStack } from '../SeaStack/SeaStack'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { colors } from '@src/theme/colors'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { SeaLoadingSpinner } from '@src/components/_atoms/SeaLoadingSpinner/SeaLoadingSpinner'
import { useFocusEffect } from '@react-navigation/native'
import { debounce } from 'lodash'
import { Extrapolation } from 'react-native-reanimated/src/interpolation'

const OPEN_GROUP_ACTION = 'OPEN_GROUP'
const CLOSE_GROUP_ACTION = 'CLOSE_GROUP'
const EXPAND_ALL_ACTION = 'EXPAND_ALL'
const COLLAPSE_ALL_ACTION = 'COLLAPSE_ALL'

const ROW_HEIGHT = 60
const ROW_BOTTOM_MARGIN = 5

const COMPACT_ROW_HEIGHT = 30
const COMPACT_ROW_PADDING_VERTICAL = 8
const COMPACT_ROW_BOTTOM_MARGIN = 4

export enum CompactValueWidth {
  ExtraSmall = 1,
  Small = 2,
  Medium = 3,
  Large = 4,
  ExtraLarge = 5,
}

export enum CompactRowPosition {
  Title = 'title',
  SubTitle = 'SubTitle',
  TopRightCorner = 'topRight',
  BottomRightCorner = 'bottomRight',
}

export interface CompactModeOptions {
  hideRow?: boolean
  label?: {
    show: boolean
    valueWidth?: CompactValueWidth
    name?: string
  }
  rowPosition?: CompactRowPosition
  isThumbnail?: boolean
  isSecondaryTitle?: boolean
}

export interface SeaTableColumn<T> {
  label: string | ((data: T, isCompactView?: boolean) => string)
  /** An optional handler that will be executed upon tapping the column header */
  onPress?: () => void
  compactModeOptions?: CompactModeOptions
  value?: (data: T, isCompactView?: boolean) => Element | string
  icon?: (data: T, isCompactView?: boolean) => ReactElement<typeof SeaIcon>
  render?: (data: T, isCompactView?: boolean) => React.ReactNode
  /** Width in units. If provided, will override widthPercentage */
  width?: number
  /* The percent of the available space that the column should occupy. Value must be between 0 and 1 */
  widthPercentage?: number
  isHidden?: boolean
  style?: StyleProp<ViewStyle>
}

export interface SeaTableRow<T> {
  data: T
  onPress?: (data: T) => void
  group?: (data: T) => string
  status?: SeaStatusType
  style?: StyleProp<ViewStyle>
}

export interface SeaTableProps<T> {
  columns: SeaTableColumn<T>[]
  rows: SeaTableRow<T>[]
  showGroupedTable?: boolean
  sortGroupsByTitle?: (a: string, b: string) => number
  sortFunction?: (a: T, b: T) => number
  /** Allow the table to scroll horizontally */
  scrollable?: boolean
  /* Compact view will be shown if screen width is less than this number */
  compactViewBreakpoint?: number
  style?: StyleProp<ViewStyle>
}

function reducer(state: Record<string, boolean>, action: { type: string; category: string; categories?: string[] }) {
  if (action.type === EXPAND_ALL_ACTION) {
    const newState = { ...state }
    if (action.categories && action.categories.length > 0) {
      action.categories.forEach(category => (newState[category] = true))
    } else {
      Object.keys(newState).forEach(key => (newState[key] = true))
    }
    return newState
  }

  if (action.type === COLLAPSE_ALL_ACTION) {
    const newState = { ...state }
    Object.keys(newState).forEach(key => (newState[key] = false))
    return newState
  }

  if (action.type === OPEN_GROUP_ACTION) {
    return { ...state, [action.category]: !state[action.category] }
  }

  return state
}

/**
 * TODO: Mobile view for the table. Waiting for the designs
 */
export const SeaTable = <T,>({
  columns,
  rows,
  style,
  showGroupedTable,
  sortGroupsByTitle,
  sortFunction,
  scrollable,
  // scrollY,
  // onScrollHandler,
  scrollDrivenOffset,
}: SeaTableProps<T>) => {
  const [containerWidth, setContainerWidth] = useState<number>(0)
  const [isLayoutReady, setIsLayoutReady] = useState(false)

  const { isMobileWidth } = useDeviceWidth()

  const isCompactMode = useMemo(() => {
    return isMobileWidth
  }, [isMobileWidth])

  const onLayout = useCallback(
    (event: LayoutChangeEvent) => {
      const layoutWidth = event.nativeEvent.layout.width
      if (layoutWidth > 0 && layoutWidth !== containerWidth) {
        setContainerWidth(layoutWidth)
        setIsLayoutReady(true)
      }
    },
    [containerWidth]
  )

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedOnLayout = useCallback(
    debounce((event: LayoutChangeEvent) => onLayout(event), 100),
    [onLayout]
  )

  const tableWidth = useMemo(() => {
    return showGroupedTable ? containerWidth - 80 : containerWidth
  }, [containerWidth, showGroupedTable])

  const { totalFixedWidth, remainingWidth, flexibleColumns, totalPercentage } = useMemo(() => {
    console.log('TTT - recalculate')

    const visibleColumns = columns.filter(c => !c.isHidden)

    // Total fixed width from columns with explicit pixel widths
    const totalFixedWidth = visibleColumns.reduce((sum, c) => {
      return sum + (c.width ?? 0)
    }, 0)

    // Remaining width available for flexible columns
    const remainingWidth = tableWidth - totalFixedWidth

    // All flexible columns (no fixed pixel width)
    const flexibleColumns = visibleColumns.filter(c => c.width == null)

    // Sum of widthPercentage values or default to equal distribution
    const totalPercentage = flexibleColumns.reduce((sum, c) => {
      return sum + (c.widthPercentage ?? 1 / flexibleColumns.length)
    }, 0)

    return {
      totalFixedWidth,
      remainingWidth,
      flexibleColumns,
      totalPercentage,
    }
  }, [columns, tableWidth])

  const getCellPixelWidthNEW = useCallback(
    (column: SeaTableColumn<T>) => {
      // If the column has fixed width, return that
      if (column?.width != null) return column.width

      // If flexible, calculate proportional width
      const colPercentage = column.widthPercentage ?? 1 / flexibleColumns.length
      const effectivePercentage = colPercentage / totalPercentage

      return remainingWidth * effectivePercentage
    },
    [flexibleColumns.length, remainingWidth, totalPercentage]
  )

  return (
    <View
      onLayout={debouncedOnLayout}
      // onLayout={onLayout}
      style={[styles.container, style, isMobileWidth ? { paddingHorizontal: 5 } : {}]}>
      {isLayoutReady ? (
        <>
          {showGroupedTable ? (
            <GroupedTable
              columns={columns}
              rows={rows}
              sortGroupsByTitle={sortGroupsByTitle}
              sortFunction={sortFunction}
              isCompactMode={isCompactMode}
              tableWidth={containerWidth - 80}
              getCellPixelWidthNEW={getCellPixelWidthNEW}
              // scrollY={scrollY}
              // onScrollHandler={onScrollHandler}
              scrollDrivenOffset={scrollDrivenOffset}
            />
          ) : !isCompactMode && scrollable ? (
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View>
                {!isCompactMode && (
                  <Header columns={columns} tableWidth={containerWidth} getCellPixelWidthNEW={getCellPixelWidthNEW} />
                )}
                <Table
                  columns={columns}
                  rows={rows}
                  isCompactMode={isCompactMode}
                  tableWidth={!isCompactMode ? containerWidth : containerWidth} // Leave space for the scroll indicator
                  getCellPixelWidthNEW={getCellPixelWidthNEW}
                  // scrollY={scrollY}
                  // onScrollHandler={onScrollHandler}
                  scrollDrivenOffset={scrollDrivenOffset}
                />
              </View>
            </ScrollView>
          ) : (
            <>
              {!isCompactMode && (
                <Header columns={columns} tableWidth={containerWidth} getCellPixelWidthNEW={getCellPixelWidthNEW} />
              )}
              <Table
                columns={columns}
                rows={rows}
                isCompactMode={isCompactMode}
                tableWidth={containerWidth}
                getCellPixelWidthNEW={getCellPixelWidthNEW}
                // scrollY={scrollY}
                // onScrollHandler={onScrollHandler}
                scrollDrivenOffset={scrollDrivenOffset}
              />
            </>
          )}
        </>
      ) : (
        <SeaLoadingSpinner />
      )}
    </View>
  )
}

interface TableProps<T> {
  rows: SeaTableRow<T>[]
  columns: SeaTableColumn<T>[]
  isCompactMode: boolean
  tableWidth: number
}

const Table = <T,>({
  rows,
  columns,
  isCompactMode,
  tableWidth,
  getCellPixelWidthNEW,
  // scrollY,
  // onScrollHandler,
  scrollDrivenOffset,
}: TableProps<T>) => {
  const renderItem = useCallback(
    ({ item: row, index }: { item: SeaTableRow<T>; index: number }) => {
      const rowStyleOverrides: ViewStyle = {
        borderWidth: 1,
        borderColor: theme.colors.borderColor,
        backgroundColor: theme.colors.white,
      }

      const errorStyleOverrides: ViewStyle = {
        borderLeftColor: getColorForStatus(SeaStatusType.Error),
        borderLeftWidth: 8,
      }

      const sharedStyle = [
        row.style,
        rowStyleOverrides,
        row.status === SeaStatusType.Error ? errorStyleOverrides : { paddingLeft: 18 }, // This padding ensures the table doesn't get out of alignment when rows have the 'error tab'
        row.status === SeaStatusType.Critical ? { backgroundColor: theme.colors.status.criticalTextSecondary } : {},
      ]

      const isDraft = row.status === SeaStatusType.Draft

      return isCompactMode ? (
        <MemoizedCompactRow
          key={index}
          row={row}
          columns={columns}
          tableWidth={tableWidth}
          style={sharedStyle}
          isDraft={isDraft}
          // getCellPixelWidthNEW={getCellPixelWidthNEW}
        />
      ) : (
        <MemoizedRow
          key={index}
          row={row}
          columns={columns}
          tableWidth={tableWidth}
          style={sharedStyle}
          isDraft={isDraft}
          getCellPixelWidthNEW={getCellPixelWidthNEW}
        />
      )
    },
    [columns, getCellPixelWidthNEW, isCompactMode, tableWidth] // 👈 dependencies that impact this function
  )

  // Use memo to prevent unnecessary re-renders
  const keyExtractor = useMemo(() => (item: SeaTableRow<T>, index: number) => `row-${index}`, [])

  /** RN - Reanimated - NEW */

  // const onScrollHandler = useAnimatedScrollHandler({
  //   onScroll: event => {
  //     scrollY.value = event.contentOffset.y
  //   },
  // })
  //
  // const headerStyle = useAnimatedStyle(() => ({
  //   height: interpolate(scrollY.value, [0, 150], [100, 40], Extrapolation.CLAMP),
  //   transform: [
  //     {
  //       scale: interpolate(scrollY.value, [0, 150], [1, 0.8], Extrapolation.CLAMP),
  //     },
  //   ],
  //   opacity: interpolate(scrollY.value, [0, 150], [1, 0.5], Extrapolation.CLAMP),
  // }))

  /** Take 2 */
  // const onScrollHandler = useAnimatedScrollHandler({
  //   onScroll: event => {
  //     // keep scroll value >= 0
  //     scrollY.value = Math.max(event.contentOffset.y, 0)
  //   },
  // })
  //
  // const MAX_HEADER_HEIGHT = 100
  // const MIN_HEADER_HEIGHT = 40
  // const SCROLL_RANGE = 150
  //
  // const headerStyle = useAnimatedStyle(() => {
  //   const height = interpolate(
  //     scrollY.value,
  //     [0, SCROLL_RANGE],
  //     [MAX_HEADER_HEIGHT, MIN_HEADER_HEIGHT],
  //     Extrapolation.CLAMP
  //   )
  //
  //   const scale = interpolate(scrollY.value, [0, SCROLL_RANGE], [1, 0.8], Extrapolation.CLAMP)
  //
  //   const opacity = interpolate(scrollY.value, [0, SCROLL_RANGE], [1, 0.5], Extrapolation.CLAMP)
  //
  //   return {
  //     height,
  //     transform: [{ scale }],
  //     opacity,
  //   }
  // })

  /** Take 3 */
  const MAX_HEADER_HEIGHT = 100
  const MIN_HEADER_HEIGHT = 40
  const COLLAPSE_RANGE = MAX_HEADER_HEIGHT - MIN_HEADER_HEIGHT // 60

  const prevY = useSharedValue(0)
  // const headerOffset = useSharedValue(0) // 0..COLLAPSE_RANGE (how much header is hidden)

  const onScrollHandler = useAnimatedScrollHandler({
    onScroll: e => {
      const y = e.contentOffset.y
      const dy = y - prevY.value
      prevY.value = y
      scrollDrivenOffset.value = Math.max(0, Math.min(COLLAPSE_RANGE, scrollDrivenOffset.value + dy))
    },
  })

  const headerStyle = useAnimatedStyle(() => {
    const progress = scrollDrivenOffset.value / COLLAPSE_RANGE
    return {
      height: MAX_HEADER_HEIGHT - progress * COLLAPSE_RANGE,
      opacity: interpolate(progress, [0, 1], [1, 0.5], Extrapolation.CLAMP),
    }
  })

  // progress: 0 (expanded) -> 1 (collapsed)
  // const headerStyle = useAnimatedStyle(() => {
  //   const progress = headerOffset.value / COLLAPSE_RANGE
  //   return {
  //     // if you like height-based:
  //     height: MAX_HEADER_HEIGHT - progress * COLLAPSE_RANGE,
  //     // extras: scale & opacity tied to the same progress
  //     transform: [{ scale: interpolate(progress, [0, 1], [1, 0.8], Extrapolation.CLAMP) }],
  //     opacity: interpolate(progress, [0, 1], [1, 0.5], Extrapolation.CLAMP),
  //   }
  // })

  /** RN - Reanimated - NEW */

  return (
    <View style={{ flex: 1 }}>
      {/** OLD - react-native ANIMATIONS */}
      {/*/!* Shrinkable Header *!/*/}
      {/*<Animated.View*/}
      {/*  style={{*/}
      {/*    height: scrollY.interpolate({*/}
      {/*      inputRange: [0, 150], // scroll distance*/}
      {/*      outputRange: [100, 40], // shrink header height*/}
      {/*      extrapolate: 'clamp',*/}
      {/*    }),*/}
      {/*    backgroundColor: 'lightblue',*/}
      {/*    justifyContent: 'center',*/}
      {/*    alignItems: 'center',*/}
      {/*  }}>*/}
      {/*  <Animated.Text*/}
      {/*    style={{*/}
      {/*      fontSize: scrollY.interpolate({*/}
      {/*        inputRange: [0, 150],*/}
      {/*        outputRange: [24, 14],*/}
      {/*        extrapolate: 'clamp',*/}
      {/*      }),*/}
      {/*    }}>*/}
      {/*    Shrinking Header*/}
      {/*  </Animated.Text>*/}
      {/*</Animated.View>*/}
      <Animated.View style={[{ backgroundColor: 'skyblue' }, headerStyle]}>
        <Text style={{ fontSize: 20 }}>Shrinking Header</Text>
      </Animated.View>
      <Animated.FlatList
        data={rows}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        initialNumToRender={50}
        maxToRenderPerBatch={10}
        windowSize={20}
        showsVerticalScrollIndicator={true}
        // scrollEnabled={true}
        // persistentScrollbar={true}
        // style={{ flex: 1, backgroundColor: 'blue' }}
        // contentContainerStyle={{
        //   flexGrow: 1,
        //   width: '100%',
        //   // paddingBottom: 20,
        //   // paddingHorizontal: 20,
        // }}
        // scrollEnabled={false}
        // nestedScrollEnabled={false}
        // TODO: Ideally pass the layout here
        // getItemLayout={getItemLayout}
        // removeClippedSubviews={true}
        // removeClippedSubviews={false}
        // updateCellsBatchingPeriod={50}

        /** Scroll Stuff */
        scrollEventThrottle={16}
        /** OLD SCROLL */
        // onScroll={Animated.event(
        //   [{ nativeEvent: { contentOffset: { y: scrollY } } }],
        //   { useNativeDriver: false } // height can't use native driver
        // )}

        /** NEW SCROLL */
        onScroll={onScrollHandler}
        // scrollEventThrottle={16} // 👈 required for smooth updates
        contentContainerStyle={{
          // paddingTop: MAX_HEADER_HEIGHT, // 👈 leaves space for header
          flexGrow: 1,
          width: '100%',
        }}
      />
    </View>
  )
}

// const GroupedSection = <T,>({
//   isOpen,
//   dispatch,
//   category,
//   groupRows,
//   columns,
//   isCompactMode,
//   isFirstGroup,
//   tableWidth,
//   getCellPixelWidthNEW,
// }: {
//   category: string
//   groupRows: SeaTableRow<T>[]
//   isOpen: boolean
//   dispatch: Dispatch<{ type: string; category: string }>
//   columns: SeaTableColumn<T>[]
//   isCompactMode: boolean
//   isFirstGroup: boolean
//   tableWidth: number
// }) => {
//   // const height = useSharedValue(isOpen ? 1 : 0)
//   // height.value = withTiming(isOpen ? 1 : 0, { duration: 250 })
//
//   // Use different approach for mobile platforms in compact mode
//   const isMobileNative = Platform.OS !== 'web' && isCompactMode
//
//   // const animatedStyle = useAnimatedStyle(
//   //   () => ({
//   //     height: isMobileNative
//   //       ? undefined // Don't set height on mobile native
//   //       : height.value *
//   //         groupRows.length *
//   //         (isCompactMode
//   //           ? columns.length * (COMPACT_ROW_HEIGHT + COMPACT_ROW_BOTTOM_MARGIN) +
//   //             COMPACT_ROW_PADDING_VERTICAL * 2 +
//   //             ROW_BOTTOM_MARGIN
//   //           : ROW_HEIGHT + ROW_BOTTOM_MARGIN),
//   //     overflow: isMobileNative ? 'visible' : 'hidden',
//   //     display: isMobileNative && !isOpen ? 'none' : 'flex',
//   //   }),
//   //   [isMobileNative, isOpen, isCompactMode, groupRows.length, columns.length, tableWidth]
//   // )
//
//   // const animatedStyle = {
//   //   height: isMobileNative
//   //     ? undefined // Don't set height
//   //     : undefined,
//   //   overflow: isMobileNative ? 'visible' : 'hidden',
//   //   display: isMobileNative && !isOpen ? 'none' : 'flex',
//   // }
//
//   return (
//     <>
//       <TouchableOpacity
//         onPress={() =>
//           dispatch({
//             type: isOpen ? CLOSE_GROUP_ACTION : OPEN_GROUP_ACTION,
//             category,
//           })
//         }>
//         <View
//           style={[
//             styles.headerRow,
//             {
//               marginTop: isFirstGroup ? 0 : 20,
//             },
//           ]}>
//           <Text style={styles.headerRowTitle}>
//             {category} {''}
//             <Text
//               style={{
//                 fontWeight: '300',
//               }}>
//               | {groupRows.length}
//             </Text>
//           </Text>
//           <FontAwesome6 size={14} name={isOpen ? 'chevron-up' : 'chevron-down'} color={theme.colors.text.primary} />
//         </View>
//       </TouchableOpacity>
//
//       {true || isMobileNative ? (
//         // For mobile native in compact mode, use simple conditional rendering
//         isOpen && (
//           <View>
//             <Table
//               rows={groupRows}
//               columns={columns}
//               isCompactMode={isCompactMode}
//               tableWidth={tableWidth}
//               getCellPixelWidthNEW={getCellPixelWidthNEW}
//             />
//           </View>
//         )
//       ) : (
//         // For web or non-compact mode, use the animation
//         // <Animated.View style={animatedStyle}>
//         //   <Table rows={groupRows} columns={columns} isCompactMode={isCompactMode} tableWidth={tableWidth} />
//         // </Animated.View>
//         <></>
//       )}
//     </>
//   )
// }

const GroupedSection = <T,>({
  category,
  // isCompactMode,
  rowCount,
  isOpen,
  onToggle,
  isFirstGroup,
}: {
  category: string
  rowCount: number
  isOpen: boolean
  onToggle: () => void
  isFirstGroup: boolean
  // isCompactMode: boolean
}) => {
  // const height = useSharedValue(isOpen ? 1 : 0)
  // height.value = withTiming(isOpen ? 1 : 0, { duration: 250 })

  // Use different approach for mobile platforms in compact mode
  // const isMobileNative = Platform.OS !== 'web' && isCompactMode

  // const animatedStyle = useAnimatedStyle(
  //   () => ({
  //     height: isMobileNative
  //       ? undefined // Don't set height on mobile native
  //       : height.value *
  //         groupRows.length *
  //         (isCompactMode
  //           ? columns.length * (COMPACT_ROW_HEIGHT + COMPACT_ROW_BOTTOM_MARGIN) +
  //             COMPACT_ROW_PADDING_VERTICAL * 2 +
  //             ROW_BOTTOM_MARGIN
  //           : ROW_HEIGHT + ROW_BOTTOM_MARGIN),
  //     overflow: isMobileNative ? 'visible' : 'hidden',
  //     display: isMobileNative && !isOpen ? 'none' : 'flex',
  //   }),
  //   [isMobileNative, isOpen, isCompactMode, groupRows.length, columns.length, tableWidth]
  // )

  // const animatedStyle = {
  //   height: isMobileNative
  //     ? undefined // Don't set height
  //     : undefined,
  //   overflow: isMobileNative ? 'visible' : 'hidden',
  //   display: isMobileNative && !isOpen ? 'none' : 'flex',
  // }

  return (
    <>
      <TouchableOpacity onPress={onToggle}>
        <View
          style={[
            styles.headerRow,
            {
              marginTop: isFirstGroup ? 0 : 20,
            },
          ]}>
          <Text style={styles.headerRowTitle}>
            {category} {''}
            <Text
              style={{
                fontWeight: '300',
              }}>
              | {rowCount}
            </Text>
          </Text>
          <FontAwesome6 size={14} name={isOpen ? 'chevron-up' : 'chevron-down'} color={theme.colors.text.primary} />
        </View>
      </TouchableOpacity>

      {/*{true || isMobileNative ? (*/}
      {/*  // For mobile native in compact mode, use simple conditional rendering*/}
      {/*  isOpen && (*/}
      {/*    <View>*/}
      {/*      <Table*/}
      {/*        rows={groupRows}*/}
      {/*        columns={columns}*/}
      {/*        isCompactMode={isCompactMode}*/}
      {/*        tableWidth={tableWidth}*/}
      {/*        getCellPixelWidthNEW={getCellPixelWidthNEW}*/}
      {/*      />*/}
      {/*    </View>*/}
      {/*  )*/}
      {/*) : (*/}
      {/*  // For web or non-compact mode, use the animation*/}
      {/*  // <Animated.View style={animatedStyle}>*/}
      {/*  //   <Table rows={groupRows} columns={columns} isCompactMode={isCompactMode} tableWidth={tableWidth} />*/}
      {/*  // </Animated.View>*/}
      {/*  <></>*/}
      {/*)}*/}
    </>
  )
}

export const GroupedTable = <T,>({
  columns,
  rows,
  sortFunction,
  sortGroupsByTitle,
  isCompactMode,
  tableWidth,
  getCellPixelWidthNEW,
}: {
  columns: SeaTableColumn<T>[]
  rows: SeaTableRow<T>[]
  sortFunction?: (a: T, b: T) => number
  sortGroupsByTitle?: (a: string, b: string) => number
  isCompactMode: boolean
  tableWidth: number
  getCellPixelWidthNEW: (col: SeaTableColumn<T>) => number
}) => {
  // --- Group rows into categories
  const { categories, groups } = useMemo(() => {
    const groupMap: Record<string, SeaTableRow<T>[]> = {}
    for (const row of rows) {
      const group = row.group ? row.group(row.data) : ''
      if (!groupMap[group]) groupMap[group] = []
      groupMap[group].push(row)
    }

    const sortedCategories = Object.keys(groupMap).sort(sortGroupsByTitle ?? (() => 0))

    for (const key of sortedCategories) {
      if (sortFunction) {
        groupMap[key].sort((a, b) => sortFunction(a.data, b.data))
      }
    }

    return { categories: sortedCategories, groups: groupMap }
  }, [rows, sortFunction, sortGroupsByTitle])

  // --- Expand/Collapse reducer
  const [state, dispatch] = useReducer(reducer, {})

  // Expand all on focus
  useFocusEffect(
    useCallback(() => {
      dispatch({ type: EXPAND_ALL_ACTION, category: '', categories })
    }, [categories])
  )

  // --- Convert into SectionList sections
  const sections = useMemo(
    () =>
      categories.map(category => ({
        title: category,
        data: state[category] ? groups[category] : [], // collapsed → empty
      })),
    [categories, groups, state]
  )

  return (
    <>
      {!isCompactMode && (
        <Header
          columns={columns}
          tableWidth={tableWidth}
          onCollapseAll={() => dispatch({ type: COLLAPSE_ALL_ACTION, category: '' })}
          onExpandAll={() => dispatch({ type: EXPAND_ALL_ACTION, category: '', categories })}
          getCellPixelWidthNEW={getCellPixelWidthNEW}
        />
      )}

      <SectionList
        sections={sections}
        keyExtractor={(item, index) => `row-${index}`}
        renderSectionHeader={({ section }) => (
          <GroupedSection
            category={section.title}
            rowCount={groups[section.title].length}
            isOpen={state[section.title]}
            isFirstGroup={section.title === categories[0]}
            onToggle={() => dispatch({ type: OPEN_GROUP_ACTION, category: section.title })}
          />
        )}
        renderItem={({ item }) => (
          <MemoizedRow
            row={item}
            columns={columns}
            isCompactMode={isCompactMode}
            tableWidth={tableWidth}
            getCellPixelWidthNEW={getCellPixelWidthNEW}
          />
        )}
        stickySectionHeadersEnabled={false}
        maxToRenderPerBatch={20}
        windowSize={10}
        removeClippedSubviews={true}
      />
    </>
  )
}

interface HeaderProps<T> {
  columns: SeaTableColumn<T>[]
  tableWidth: number
  onCollapseAll?: () => void
  onExpandAll?: () => void
}

const Header = <T,>({ columns, tableWidth, onCollapseAll, onExpandAll, getCellPixelWidthNEW }: HeaderProps<T>) => (
  <View style={styles.header}>
    <View style={styles.columns}>
      {columns
        .filter(col => !col.isHidden)
        .map(col => (
          <Pressable
            key={col.label}
            onPress={() => col.onPress && col.onPress()}
            style={[
              styles.headerColumn,
              {
                // width: getCellPixelWidth(
                //   columns,
                //   // Remove the width of the expand & collapse buttons
                //   tableWidth,
                //   col
                // ),
                width: getCellPixelWidthNEW(col),
              },
              {
                paddingHorizontal: 4,
              },
            ]}>
            <Text numberOfLines={1} ellipsizeMode={'tail'} style={styles.headerText}>
              {col.label}
            </Text>
          </Pressable>
        ))}

      {onExpandAll && onCollapseAll && (
        <SeaStack style={styles.buttons} direction={'row'}>
          <Pressable onPress={onExpandAll}>
            <SeaIcon icon={'unfold_more'} color={colors.grey} />
          </Pressable>
          <Pressable onPress={onCollapseAll}>
            <SeaIcon icon={'unfold_less'} color={colors.grey} />
          </Pressable>
        </SeaStack>
      )}
    </View>
  </View>
)

export interface RowProps<T> {
  row: SeaTableRow<T>
  columns: SeaTableColumn<T>[]
  style?: StyleProp<ViewStyle>
  tableWidth: number
  isDraft?: boolean
}

const Row = <T,>({ row, columns, style, tableWidth, isDraft = false, getCellPixelWidthNEW }: RowProps<T>) => {
  return (
    <TouchableOpacity disabled={!row.onPress} onPress={() => row.onPress?.(row.data)}>
      <View style={[styles.row, style, isDraft ? { paddingLeft: 7 } : {}]}>
        {isDraft && (
          <View style={[styles.draftContainer]}>
            <Text style={styles.draftText}>DRAFT</Text>
          </View>
        )}
        {columns
          .filter(col => !col.isHidden)
          .map((col, i) => (
            <View
              key={i}
              style={[
                styles.cell,
                {
                  // width: getCellPixelWidth(columns, tableWidth, col),
                  width: getCellPixelWidthNEW(col),
                  // width: 100,
                },
                isDraft ? { opacity: 0.5 } : {},
              ]}>
              <View
                style={{
                  flex: 1,
                  width: '100%',
                }}>
                {col.render ? (
                  <View style={{ flex: 1, justifyContent: 'center' }}>{col.render(row.data)}</View>
                ) : (
                  <SeaStack direction={'row'} gap={6} style={{ flex: 1, width: '100%' }}>
                    {col.icon?.(row.data)}
                    <SeaTypography
                      variant={'value'}
                      textStyle={styles.cellText}
                      numberOfLines={2}
                      ellipsizeMode={'tail'}
                      containerStyle={{ flex: 1, width: '100%' }}>
                      {col.value?.(row.data)}
                    </SeaTypography>
                  </SeaStack>
                )}
              </View>
            </View>
          ))}
      </View>
    </TouchableOpacity>
  )
}
const MemoizedRow = React.memo(Row)

const extractCompactColumns = <T,>(
  columns: SeaTableColumn<T>[]
): {
  thumbnailCol: SeaTableColumn<T> | undefined
  titleCol: SeaTableColumn<T> | undefined
  subTitleCol: SeaTableColumn<T> | undefined
  topRightCol: SeaTableColumn<T> | undefined
  bottomRightCol: SeaTableColumn<T> | undefined
  remainingColumns: SeaTableColumn<T>[]
} => {
  let thumbnailCol = undefined
  let titleCol = undefined
  let subTitleCol = undefined
  let topRightCol = undefined
  let bottomRightCol = undefined
  const remainingColumns: SeaTableColumn<T>[] = []

  columns.forEach(col => {
    let isSpecialColumn = false
    if (col.compactModeOptions?.isThumbnail) {
      isSpecialColumn = true
      thumbnailCol = col
    }

    if (col.compactModeOptions?.rowPosition) {
      switch (col.compactModeOptions?.rowPosition) {
        case CompactRowPosition.Title:
          titleCol = col
          isSpecialColumn = true
          break
        case CompactRowPosition.SubTitle:
          subTitleCol = col
          isSpecialColumn = true
          break
        case CompactRowPosition.TopRightCorner:
          topRightCol = col
          isSpecialColumn = true
          break
        case CompactRowPosition.BottomRightCorner:
          bottomRightCol = col
          isSpecialColumn = true
          break
        default:
          break
      }
    }

    // If not a special column, add to remaining columns
    if (!isSpecialColumn) {
      remainingColumns.push(col)
    }
  })

  return {
    thumbnailCol,
    titleCol,
    subTitleCol,
    topRightCol,
    bottomRightCol,
    remainingColumns,
  }
}

const renderCompactLabel = <T,>(col: SeaTableColumn<T>) => {
  const compactLabelOptions = col.compactModeOptions?.label
  if (compactLabelOptions?.show) {
    return (
      <Pressable onPress={() => col.onPress && col.onPress()} style={styles.compactRowPressable}>
        <SeaTypography variant={'value'} containerStyle={styles.compactRowLabel} numberOfLines={1}>
          {compactLabelOptions.name ?? col.label}
        </SeaTypography>
      </Pressable>
    )
  }
  return undefined
}

const renderCompactColumn = <T,>(
  col: SeaTableColumn<T>,
  row: SeaTableRow<T>,
  forceHideLabel?: boolean,
  isTitle?: boolean
): ReactNode => {
  if (col.render) {
    return (
      <View style={styles.compactRowRow}>
        {!forceHideLabel && renderCompactLabel(col)}
        {col.render(row.data, true)}
      </View>
    )
  }

  if (col.value) {
    return (
      <View style={styles.compactRowRow}>
        {!forceHideLabel && renderCompactLabel(col)}
        <View
          style={{
            flex: col.compactModeOptions?.label?.valueWidth ?? CompactValueWidth.Small,
            flexDirection: 'row',
            gap: 2,
          }}>
          {col.icon?.(row.data, true)}
          <SeaTypography
            variant={'value'}
            numberOfLines={1}
            ellipsizeMode={'tail'}
            containerStyle={{ marginBottom: 0, flex: 1 }}
            textStyle={isTitle ? { fontWeight: 700 } : undefined}>
            {col.value?.(row.data, true)}
          </SeaTypography>
        </View>
      </View>
    )
  }
  return undefined
}

const CompactRow = <T,>({ row, columns, style, isDraft = false }: RowProps<T>) => {
  const { thumbnailCol, titleCol, subTitleCol, topRightCol, bottomRightCol, remainingColumns } =
    extractCompactColumns(columns)

  const isTitleRow = titleCol ?? topRightCol
  const isSubtitleRow = subTitleCol

  return (
    <TouchableOpacity disabled={!row.onPress} onPress={() => row.onPress?.(row.data)}>
      <SeaStack direction={'row'} style={[styles.compactRow, style]}>
        {/** Thumbnail - Image */}
        {(thumbnailCol || isDraft) && (
          <View style={{ width: 50, marginRight: 6, alignItems: 'flex-start' }}>
            {isDraft ? (
              <>
                <View style={[styles.draftContainerCompact]}>
                  <Text style={styles.draftTextCompact}>DRAFT</Text>
                </View>
              </>
            ) : (
              <>{thumbnailCol?.render?.(row.data)}</>
            )}
          </View>
        )}

        <SeaStack
          direction={'column'}
          align={'start'}
          style={[{ flex: 1, width: '100%' }, isDraft ? { opacity: 0.5 } : {}]}>
          {/** Content - Title & Subtitle Rows */}
          {(isTitleRow ?? isSubtitleRow) && (
            <SeaStack direction={'column'} align={'start'} style={{ flex: 1, paddingBottom: 2, width: '100%' }} gap={4}>
              {/** Title Row */}
              {isTitleRow && (
                <SeaStack style={{ flex: 1, width: '100%' }} align={'center'} justify={'between'} gap={10}>
                  <View style={styles.compactRowTitleColumn}>
                    {titleCol && renderCompactColumn(titleCol, row, false, true)}
                  </View>
                  {topRightCol && (topRightCol?.value || topRightCol?.render) && (
                    <View style={styles.compactRowTopRightCol}>{renderCompactColumn(topRightCol, row, true)}</View>
                  )}
                </SeaStack>
              )}
              {isSubtitleRow && (
                <View style={styles.compactRowSubTitleColumn}>
                  {renderCompactColumn(subTitleCol, row, false, true)}
                </View>
              )}
            </SeaStack>
          )}

          {/** Content - Remaining Rows */}
          {remainingColumns
            .filter(col => !col.isHidden && !col.compactModeOptions?.hideRow && !col.compactModeOptions?.isThumbnail)
            .map((col, i) => (
              <View key={i} style={[styles.compactRowContentColumn, isDraft ? { opacity: 0.5 } : {}]}>
                {renderCompactColumn(col, row)}
              </View>
            ))}
        </SeaStack>

        {bottomRightCol && (
          <View style={[styles.compactRowBottomRightCol, isDraft ? { opacity: 0.5 } : {}]}>
            {renderCompactColumn(bottomRightCol, row, true)}
          </View>
        )}
      </SeaStack>
    </TouchableOpacity>
  )
}
const MemoizedCompactRow = React.memo(CompactRow)

const getCellPixelWidth = <T,>(columns: SeaTableColumn<T>[], tableWidth: number, column: SeaTableColumn<T>): number => {
  // const visibleColumns = columns.filter(c => !c.isHidden)
  //
  // // Total fixed width from columns with explicit pixel widths
  // const totalFixedWidth = visibleColumns.reduce((sum, c) => {
  //   return sum + (c.width ?? 0)
  // }, 0)
  //
  // // Remaining width available for flexible columns
  // const remainingWidth = tableWidth - totalFixedWidth
  //
  // // All flexible columns (no fixed pixel width)
  // const flexibleColumns = visibleColumns.filter(c => c.width == null)
  //
  // // Sum of widthPercentage values or default to equal distribution
  // const totalPercentage = flexibleColumns.reduce((sum, c) => {
  //   return sum + (c.widthPercentage ?? 1 / flexibleColumns.length)
  // }, 0)
  // If the column has fixed width, return that
  // if (col?.width != null) return col.width
  //
  // // If flexible, calculate proportional width
  // const colPercentage = col.widthPercentage ?? 1 / flexibleColumns.length
  // const effectivePercentage = colPercentage / totalPercentage
  //
  // return remainingWidth * effectivePercentage
}

const getColorForStatus = (status: SeaStatusType) => {
  switch (status) {
    case SeaStatusType.Ok:
    case SeaStatusType.Attention:
      return theme.colors.white
    case SeaStatusType.Warning:
      return theme.colors.status.warnPrimary
    case SeaStatusType.Error:
      return theme.colors.status.error
    case SeaStatusType.Critical:
      return theme.colors.status.critical
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
  },
  header: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingLeft: 25,
    justifyContent: 'space-between',
  },
  columns: {
    flexDirection: 'row',
    // height: 36,
    marginBottom: 4,
  },
  buttons: {},
  headerColumn: {
    paddingVertical: 4,
    justifyContent: 'center',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
    flexDirection: 'row',
    paddingHorizontal: 5,
  },
  headerText: {
    textTransform: 'uppercase',
    fontFamily: fontFamily.BODY_FONT,
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors.text.body,
    flex: 1,
    flexShrink: 1,
  },
  headerRow: {
    borderRadius: 8,
    backgroundColor: theme.colors.lightGrey,
    marginBottom: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerRowTitle: {
    fontSize: 12,
    color: theme.colors.text.primary,
    fontWeight: 'bold',
    fontFamily: fontFamily.BODY_FONT,
    letterSpacing: 0.5,
    textTransform: 'uppercase',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: ROW_HEIGHT,
    marginBottom: ROW_BOTTOM_MARGIN,
    borderRadius: 8,
    paddingHorizontal: 10,
  },
  cell: {
    justifyContent: 'center',
    alignItems: 'flex-start',
    paddingHorizontal: 10,
  },
  cellText: {
    textAlign: 'left',
  },
  compactRow: {
    marginBottom: ROW_BOTTOM_MARGIN,
    paddingHorizontal: 8,
    paddingVertical: 8,
    borderRadius: 8,
    flex: 1,
  },
  compactRowRow: {
    marginBottom: 0,
    flexDirection: 'row',
    gap: 4,
  },
  compactRowPressable: { flex: 1 },
  compactRowLabel: {
    marginBottom: 0,
  },
  compactRowValue: { flex: 1 },

  compactRowContentColumn: {
    flex: 1,
    paddingBottom: 2,
    width: '100%',
  },
  compactRowTitleColumn: {
    flex: 4.5,
    width: '100%',
  },
  compactRowSubTitleColumn: {
    flex: 1,
    width: '100%',
  },
  compactRowTopRightCol: {
    flex: 1,
    alignItems: 'flex-end',
  },
  compactRowBottomRightCol: {
    flex: 1,
    position: 'absolute',
    right: 5,
    bottom: 5,
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
  },

  /** Styling for Draft Record */
  draftContainer: {
    transform: [{ rotate: '-90deg' }],
    width: 10,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  draftText: {
    color: theme.colors.text.primary,
    fontWeight: 'bold',
    fontSize: 11,
    letterSpacing: 1,
  },
  draftContainerCompact: {
    transform: [{ rotate: '-45deg' }],
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  draftTextCompact: {
    color: theme.colors.text.primary,
    fontWeight: 'bold',
    fontSize: 12,
    letterSpacing: 1,
  },
})
