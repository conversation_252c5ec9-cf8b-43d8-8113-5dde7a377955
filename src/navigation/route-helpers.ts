import { PARENT_ROUTES, Routes, ROUTES_CONFIG } from './constants'

/**
 * Creates a mapping of route names to their parent route names
 */
export const createRouteToParentMapping = (): Record<string, string> => {
  const mapping: Record<string, string> = {}

  Object.values(ROUTES_CONFIG).forEach(route => {
    if (route.children) {
      Object.keys(route.children).forEach(childKey => {
        mapping[childKey] = route.name ?? ''
      })
    }
  })

  return mapping
}

/**
 * Find the parent route for a given route name or path
 */
export const findParentRoute = (currentPath: string): string => {
  const routeMapping = createRouteToParentMapping()

  // First, try to find direct matches in the mapping
  for (const [childRoute, parentRoute] of Object.entries(routeMapping)) {
    if (currentPath.includes(childRoute)) {
      return parentRoute
    }
  }

  // If no child match found, check if current path matches any parent route directly
  for (const parentRoute of PARENT_ROUTES) {
    if (currentPath.includes(parentRoute)) {
      return parentRoute
    }
  }

  return ''
}

/**
 * Check if a route is currently active based on the current path
 */
export const isRouteActiveHelper = (routeName: string, currentPath: string): boolean => {
  return currentPath.includes(routeName)
}
