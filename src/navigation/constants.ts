import { IconVariant } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { MaterialIconName } from '@src/types/MaterialIcons'

export type RouteConfig = {
  name: string
  title: string
  path: string
  icon?: { name: MaterialIconName; variant?: IconVariant; size?: number }
  children?: Partial<RoutesConfig>
}

export type RoutesConfig = Record<Routes, Partial<RouteConfig>>

/**
 * List of all the routes
 */
export enum Routes {
  INDEX = 'index',
  LOGIN = 'login',
  LOGOUT = 'logout',
  FORGOT_PASSWORD = 'forgot-password',
  RESET_PASSWORD = 'reset-password',
  NETWORK = 'network',
  HOME = '(home)',
  AUTH = '(auth)',
  ADMIN = '(admin)',
  FLEET_DASHBOARD = 'fleet-dashboard',
  FLEET_DASHBOARD_SETTINGS = 'fleet-dashboard-settings',
  VESSEL_DASHBOARD = 'vessel-dashboard',
  VESSEL_DETAILS = 'details',
  LOGBOOK = 'logbook',

  //Parent routes
  VESSEL = 'vessel',
  MAINTENANCE = 'maintenance',
  SAFETY = 'safety',
  VESSEL_DOCUMENT_REGISTER = 'vessel-document-register',
  CREW = 'crew',
  HEALTH_SAFETY = 'healthSafety',
  REPORTING = 'reporting',

  // Safety Section
  SAFETY_DASHBOARD = 'safety-dashboard',
  SAFETY_EQUIPMENT_CHECKS = 'safetyEquipmentChecks',
  SAFETY_EQUIPMENT_CHECKS_LIST = 'safetyEquipmentChecksList',
  SAFETY_EQUIPMENT_CHECKS_VIEW = 'safetyEquipmentChecksView',
  SAFETY_EQUIPMENT_CHECKS_VIEW_COMPLETED = 'safetyEquipmentChecksViewCompleted',
  SAFETY_EQUIPMENT_EXPIRIES = 'safetyEquipmentExpiries',

  DRILLS = 'drills',
  DRILLS_VIEW_USER_DRILLS = 'drillsViewUserDrills',
  DRILLS_VIEW_USER_DRILL = 'drillsViewUserDrill',
  DRILLS_VIEW_DRILL = 'drillsViewDrill',
  DRILLS_VIEW_DRILL_REPORT = 'drillsViewDrillReport',
  DRILLS_VIEW_DRILLS_HISTORY = 'drillsViewDrillsHistory',

  SAFETY_EQUIPMENT_EXPIRIES_VIEW = 'safetyEquipmentExpiriesList',

  // Maintenance Section
  MAINTENANCE_DASHBOARD = 'maintenance-dashboard',

  MAINTENANCE_SCHEDULE = 'maintenanceSchedule',
  MAINTENANCE_SCHEDULE_VIEW = 'maintenanceScheduleView',

  JOBLIST = 'jobList',
  JOBLIST_VIEW = 'jobListView',

  SPARE_PARTS_LIST = 'sparePartsList',
  SPARE_PARTS_LIST_VIEW = 'sparePartsListView',

  EQUIPMENT_LIST = 'equipmentList',
  EQUIPMENT_LIST_VIEW = 'equipmentListView',

  EQUIPMENT_MANUALS = 'equipmentManuals',
  EQUIPMENT_MANUALS_VIEW = 'equipmentManualsView',

  MAINTENANCE_HISTORY = 'maintenanceHistory',
  MAINTENANCE_HISTORY_VIEW = 'maintenanceHistoryView',
  // Vessel Document Register
  VESSEL_DOCUMENT_REGISTER_DASHBOARD = 'vessel-document-register-dashboard',
  VESSEL_CERTIFICATES = 'vesselCertificates',
  VESSEL_CERTIFICATES_VIEW = 'vesselCertificatesView',
  VESSEL_DOCUMENTS = 'vesselDocuments',
  VESSEL_DOCUMENTS_VIEW = 'vesselDocumentsView',
  SURVEY_DOCUMENTS = 'surveyDocuments',
  SURVEY_DOCUMENTS_VIEW = 'surveyDocumentsView',
  STANDARD_OPERATING_PROCEDURES = 'standardOperatingProcedures',
  STANDARD_OPERATING_PROCEDURES_VIEW = 'standardOperatingProceduresView',

  // Health & Safety
  HEALTH_SAFETY_DASHBOARD = 'health-safety-dashboard',
  INCIDENT_REPORT = 'incidentReport',
  CORRECTIVE_ACTION = 'correctiveAction',
  RISK_ASSESSMENT = 'riskAssessment',
  HEALTH_SAFETY_MEETING = 'healthSafetyMeeting',
  DANGEROUS_GOODS_REGISTER = 'dangerousGoodsRegister',

  CORRECTIVE_ACTION_LIST = 'correctiveActionList',
  CORRECTIVE_ACTION_VIEW = 'correctiveActionView',
  CORRECTIVE_ACTION_EDIT = 'correctiveActionEdit',

  INCIDENT_REPORT_LIST = 'incidentReportList',
  INCIDENT_REPORT_VIEW = 'incidentReportView',
  INCIDENT_REPORT_EDIT = 'incidentReportEdit',

  RISK_ASSESSMENT_LIST = 'riskAssessmentList',
  RISK_ASSESSMENT_VIEW = 'riskAssessmentView',
  RISK_ASSESSMENT_EDIT = 'riskAssessmentEdit',

  HEALTH_SAFETY_MEETING_LIST = 'healthSafetyMeetingList',
  HEALTH_SAFETY_MEETING_VIEW = 'healthSafetyMeetingView',
  HEALTH_SAFETY_MEETING_EDIT = 'healthSafetyMeetingEdit',

  DANGEROUS_GOODS_REGISTER_LIST = 'dangerousGoodsRegisterList',
  DANGEROUS_GOODS_REGISTER_VIEW = 'dangerousGoodsRegisterView',
  DANGEROUS_GOODS_REGISTER_EDIT = 'dangerousGoodsRegisterEdit',

  // Company Document Register
  COMPANY_DOCUMENT_REGISTER_DASHBOARD = 'company-document-register-dashboard',
  DOCUMENT_REGISTER = 'documentRegister',
  COMPANY_PLAN = 'companyPlan',
  COMPANY_DOCUMENTS = 'companyDocuments',
  COMPANY_DOCUMENTS_VIEW = 'companyDocumentsView',
  CUSTOM_FORMS = 'customForms',
  CUSTOM_FORMS_VIEW = 'customFormsView',
  CUSTOM_FORMS_BUILDER = 'customFormsBuilder',
  CUSTOM_FORM_BROWSE_TEMPLATES = 'customFormBrowseTemplates',

  // Crew
  CREW_DASHBOARD = 'crew-dashboard',
  CREW_PARTICULARS = 'crewParticulars',
  CREW_PARTICULARS_EDIT = 'crewParticularsEdit',

  CREW_PARTICULARS_VIEW = 'crewParticularsView',
  CREW_PARTICULARS_VIEW_PROFILE = 'crewParticularsViewProfile',
  CREW_PARTICULARS_VIEW_FORMS = 'crewParticularsViewForms',
  CREW_PARTICULARS_VIEW_SEATIME = 'crewParticularsViewSeaTime',
  CREW_PARTICULARS_VIEW_CERTIFICATES = 'crewParticularsViewCertificates',
  CREW_PARTICULARS_VIEW_DRILLS = 'crewParticularsViewDrills',
  CREW_PARTICULARS_VIEW_TRAINING = 'crewParticularsViewTraining',

  CREW_PARTICULARS_ARCHIVED = 'crewParticularsArchived',
  CREW_PARTICULARS_ARCHIVED_VIEW_PROFILE = 'crewParticularsArchivedViewProfile',

  CREW_CERTIFICATES = 'crewCertificates',
  CREW_CERTIFICATES_VIEW = 'viewCrewCertificates',

  CREW_CONTACTS = 'contacts',
  CREW_CONTACTS_VIEW = 'viewContacts',

  CREW_TRAINING = 'crewTraining',
  CREW_TRAINING_VIEW = 'viewCrewTraining',
  CREW_TRAINING_VIEW_USER_TRAINING = 'crewTrainingVewUserTraining',
  CREW_TRAINING_VIEW_USER = 'crewTrainingViewUser',
  CREW_TRAINING_REPORT = 'crewTrainingReport',

  // Reporting
  REPORTING_DASHBOARD = 'reporting-dashboard',
  REPORTING_LOGBOOK = 'reportingLogbook',
  REPORTING_SAFETY = 'reportingSafety',
  REPORTING_MAINTENANCE = 'reportingMaintenance',
  REPORTING_HEALTH_SAFETY = 'reportingHealthSafety',
  REPORTING_COMPANY_DOCUMENT = 'reportingCompanyDocument',
  REPORTING_VESSEL_DOCUMENT = 'reportingVesselDocument',
  REPORTING_CREW = 'reportingCrew',

  // Reporting Safety
  REPORTING_SAFETY_EQUIPMENT_CHECKS = 'reportingSafetyEquipmentChecks',
  REPORTING_SAFETY_EQUIPMENT_CHECKS_VIEW = 'reportingSafetyEquipmentChecksView',
  REPORTING_COMPLETED_SAFETY_CHECKS = 'reportingCompletedSafetyChecks',
  REPORTING_COMPLETED_SAFETY_CHECKS_VIEW = 'reportingCompletedSafetyChecksView',
  REPORTING_SAFETY_EQUIPMENT_EXPIRIES = 'reportingSafetyEquipmentExpiries',
  REPORTING_SAFETY_EQUIPMENT_EXPIRIES_VIEW = 'reportingSafetyEquipmentExpiriesView',

  // Reporting Maintenance
  REPORTING_MAINTENANCE_SCHEDULE = 'reportingMaintenanceSchedule',
  REPORTING_MAINTENANCE_SCHEDULE_VIEW = 'reportingMaintenanceScheduleView',
  REPORTING_MAINTENANCE_JOB = 'reportingMaintenanceJob',
  REPORTING_MAINTENANCE_JOB_VIEW = 'reportingMaintenanceJobView',
  REPORTING_COMPLETED_MAINTENANCE_TASKS = 'reportingCompletedMaintenanceTasks',
  REPORTING_COMPLETED_MAINTENANCE_TASKS_VIEW = 'reportingCompletedMaintenanceTasksView',

  // Reporting Health & Safety
  REPORTING_HEALTH_SAFETY_INCIDENT = 'reportingHealthSafetyIncident',
  REPORTING_HEALTH_SAFETY_INCIDENT_TYPE = 'reportingHealthSafetyIncidentType',
  REPORTING_HEALTH_SAFETY_INCIDENT_CATEGORY = 'reportingHealthSafetyIncidentCategory',
  REPORTING_HEALTH_SAFETY_INCIDENT_CONTRIBUTING_FACTOR = 'reportingHealthSafetyIncidentContributingFactor',
  REPORTING_HEALTH_SAFETY_INCIDENT_INJURY_TYPE = 'reportingHealthSafetyIncidentInjuryType',
  REPORTING_HEALTH_SAFETY_INCIDENT_INJURY_LOCATION = 'reportingHealthSafetyIncidentInjuryLocation',
  REPORTING_HEALTH_SAFETY_INCIDENT_NOTIFIED_TO_AUTHORITY = 'reportingHealthSafetyIncidentNotifiedToAuthority',
  REPORTING_HEALTH_SAFETY_INCIDENT_PERSONNEL_INVOLVED = 'reportingHealthSafetyIncidentPersonnelInvolved',
  REPORTING_HEALTH_SAFETY_INCIDENT_INJURY_OUTCOME = 'reportingHealthSafetyIncidentInjuryOutcome',

  // Reporting Vessel Document Register
  REPORTING_VESSEL_DOCUMENT_VESSEL_CERTIFICATE = 'reportingVesselDocumentVesselCertificate',

  // Crew Reporting
  REPORTING_CREW_CERTIFICATES = 'reportingCrewCertificates',

  //Other routes
  ACTION_LOG = 'actionLog',
  SUPPORT = 'support',
  PROFILE = 'profile',
  VOYAGE = 'voyage',
  VOYAGE_HISTORY = 'voyageHistory',
  VOYAGE_HISTORY_VIEW = 'voyageHistoryView',

  VESSEL_STATUS = 'status',
  ENGINE_HOURS = 'engineHours',
  FUEL = 'fuel',
  SURVEY_STATUS = 'surveyStatus',
}

export const PARENT_ROUTES = [
  Routes.FLEET_DASHBOARD,
  Routes.VESSEL_DASHBOARD,
  Routes.LOGBOOK,
  Routes.VOYAGE_HISTORY,
  Routes.SAFETY_DASHBOARD,
  Routes.MAINTENANCE_DASHBOARD,
  Routes.VESSEL_DOCUMENT_REGISTER_DASHBOARD,
  Routes.HEALTH_SAFETY_DASHBOARD,
  Routes.COMPANY_DOCUMENT_REGISTER_DASHBOARD,
  Routes.CREW_DASHBOARD,
]

/**
 * List of all the routes and their configurations
 */
export const ROUTES_CONFIG: RoutesConfig = {
  // Main Stack Navigation
  [Routes.INDEX]: {
    name: Routes.INDEX,
    title: Routes.INDEX,
    path: '/',
  },
  [Routes.AUTH]: {
    name: Routes.AUTH,
    title: Routes.AUTH,
    path: `/${Routes.AUTH}`,
  },
  [Routes.HOME]: {
    name: Routes.HOME,
    title: Routes.HOME,
    path: `/${Routes.HOME}`,
  },

  [Routes.VESSEL]: {
    name: Routes.VESSEL,
    title: 'Vessel',
  },
  [Routes.MAINTENANCE]: {
    name: Routes.MAINTENANCE,
    title: 'Maintenance',
  },
  [Routes.SAFETY]: {
    name: Routes.SAFETY,
    title: 'Safety',
  },
  [Routes.VESSEL_DOCUMENT_REGISTER]: {
    name: Routes.VESSEL_DOCUMENT_REGISTER,
    title: 'Vessel Document Register',
  },
  [Routes.CREW]: {
    name: Routes.CREW,
    title: 'Crew',
  },
  [Routes.HEALTH_SAFETY]: {
    name: Routes.HEALTH_SAFETY,
    title: 'Health & Safety',
  },

  // Public routes - general
  [Routes.NETWORK]: {
    name: Routes.NETWORK,
    title: Routes.NETWORK,
    path: `/(public)/${Routes.NETWORK}`,
  },

  // ADMIN - Navigation
  [Routes.LOGIN]: {
    name: Routes.LOGIN,
    title: Routes.LOGIN,
    path: `/${Routes.AUTH}/${Routes.LOGIN}`,
  },
  [Routes.LOGOUT]: {
    name: Routes.LOGOUT,
    title: 'Log out',
    path: `/${Routes.AUTH}/${Routes.LOGOUT}`,
    icon: { name: 'logout' },
  },
  [Routes.FORGOT_PASSWORD]: {
    name: Routes.FORGOT_PASSWORD,
    title: 'Forgot Password',
    path: `/${Routes.AUTH}/${Routes.FORGOT_PASSWORD}`,
  },
  [Routes.RESET_PASSWORD]: {
    name: Routes.RESET_PASSWORD,
    title: 'Reset Password',
    path: `/${Routes.AUTH}/${Routes.RESET_PASSWORD}`,
  },

  // AUTH - Navigation
  [Routes.ADMIN]: {
    name: Routes.ADMIN,
    title: 'Admin Accounts',
    path: `/${Routes.AUTH}/${Routes.ADMIN}`,
  },

  // HOME - Drawer Navigation
  [Routes.FLEET_DASHBOARD]: {
    name: Routes.FLEET_DASHBOARD,
    title: 'Fleet Dashboard',
    path: `/${Routes.HOME}/${Routes.FLEET_DASHBOARD}`,
    icon: { name: 'anchor' },
  },
  [Routes.FLEET_DASHBOARD_SETTINGS]: {
    name: Routes.FLEET_DASHBOARD_SETTINGS,
    title: 'Fleet Dashboard Settings',
    path: `/${Routes.HOME}/${Routes.FLEET_DASHBOARD}/settings`,
  },

  [Routes.VESSEL_DASHBOARD]: {
    name: Routes.VESSEL_DASHBOARD,
    title: 'Dashboard',
    path: `/${Routes.HOME}/vessel/${Routes.VESSEL_DASHBOARD}`,
    icon: { name: 'directions_boat_filled' },
  },
  [Routes.VESSEL_DETAILS]: {
    name: Routes.VESSEL_DETAILS,
    title: 'Vessel Details',
    path: `/${Routes.HOME}/vessel/${Routes.VESSEL_DETAILS}`,
  },

  [Routes.LOGBOOK]: {
    name: Routes.LOGBOOK,
    title: 'Logbook',
    path: `/${Routes.HOME}/vessel/${Routes.LOGBOOK}`,
    icon: { name: 'menu_book' },
  },

  // Safety Section
  [Routes.SAFETY_DASHBOARD]: {
    name: Routes.SAFETY_DASHBOARD,
    title: 'Safety',
    path: `/${Routes.HOME}/vessel/safety/${Routes.SAFETY_DASHBOARD}`,
    icon: { name: 'support' },
    children: {
      [Routes.SAFETY_EQUIPMENT_CHECKS]: {},
      [Routes.SAFETY_EQUIPMENT_EXPIRIES]: {},
      [Routes.DRILLS]: {},
    },
  },
  [Routes.SAFETY_EQUIPMENT_CHECKS]: {
    name: Routes.SAFETY_EQUIPMENT_CHECKS,
    title: 'Safety Checks',
    path: `/vessel/safety/${Routes.SAFETY_EQUIPMENT_CHECKS}`,
  },
  [Routes.SAFETY_EQUIPMENT_CHECKS_VIEW]: {
    name: Routes.SAFETY_EQUIPMENT_CHECKS_VIEW,
    title: 'Safety Check - View',
    path: `/vessel/safety/${Routes.SAFETY_EQUIPMENT_CHECKS}/view`,
  },
  [Routes.SAFETY_EQUIPMENT_CHECKS_VIEW_COMPLETED]: {
    name: Routes.SAFETY_EQUIPMENT_CHECKS_VIEW_COMPLETED,
    title: 'Completed Safety Check - View',
    path: `/vessel/safety/${Routes.SAFETY_EQUIPMENT_CHECKS}/viewCompleted`,
  },
  [Routes.SAFETY_EQUIPMENT_CHECKS_LIST]: {
    name: Routes.SAFETY_EQUIPMENT_CHECKS_LIST,
    title: 'Safety Checks',
    path: `/${Routes.HOME}/vessel/safety/${Routes.SAFETY_EQUIPMENT_CHECKS}/list`,
  },

  [Routes.SAFETY_EQUIPMENT_EXPIRIES]: {
    name: Routes.SAFETY_EQUIPMENT_EXPIRIES,
    title: 'Safety Equipment Expiries',
    path: `/vessel/safety/${Routes.SAFETY_EQUIPMENT_EXPIRIES}`,
  },
  [Routes.SAFETY_EQUIPMENT_EXPIRIES_VIEW]: {
    name: Routes.SAFETY_EQUIPMENT_EXPIRIES_VIEW,
    title: 'Safety Equipment Expiries - View',
    path: `/vessel/safety/${Routes.SAFETY_EQUIPMENT_EXPIRIES}/view`,
  },
  [Routes.DRILLS]: {
    name: Routes.DRILLS,
    title: 'Drills',
    path: `/vessel/safety/${Routes.DRILLS}`,
  },
  [Routes.DRILLS_VIEW_DRILLS_HISTORY]: {
    name: Routes.DRILLS_VIEW_DRILLS_HISTORY,
    title: 'Drills History',
    path: `/vessel/safety/${Routes.DRILLS}/history`,
  },
  [Routes.DRILLS_VIEW_USER_DRILLS]: {
    name: Routes.DRILLS_VIEW_USER_DRILLS,
    title: 'User Drills',
    path: `/vessel/safety/${Routes.DRILLS}/user`,
  },
  [Routes.DRILLS_VIEW_USER_DRILL]: {
    name: Routes.DRILLS_VIEW_USER_DRILL,
    title: 'User Drills',
    path: `/vessel/safety/${Routes.DRILLS}/user-drill`,
  },
  [Routes.DRILLS_VIEW_DRILL]: {
    name: Routes.DRILLS_VIEW_DRILL,
    title: 'User Drills',
    path: `/vessel/safety/${Routes.DRILLS}/drill`,
  },
  [Routes.DRILLS_VIEW_DRILL_REPORT]: {
    name: Routes.DRILLS_VIEW_DRILL_REPORT,
    title: 'User Drills',
    path: `/vessel/safety/${Routes.DRILLS}/drill-report`,
  },

  // Maintenance Section
  [Routes.MAINTENANCE_DASHBOARD]: {
    name: Routes.MAINTENANCE_DASHBOARD,
    title: 'Maintenance',
    path: `/vessel/maintenance/${Routes.MAINTENANCE_DASHBOARD}`,
    icon: { name: 'build' },
    children: {
      [Routes.MAINTENANCE_SCHEDULE]: {
        // name: Routes.MAINTENANCE_SCHEDULE,
        // title: "Usage: If necessary REPLACE WITH CUSTOM NAME FROM THIS ROUTE",
      },
      [Routes.JOBLIST]: {},
      [Routes.SPARE_PARTS_LIST]: {},
      [Routes.EQUIPMENT_LIST]: {},
      [Routes.EQUIPMENT_MANUALS]: {},
      [Routes.MAINTENANCE_HISTORY]: {},
    },
  },
  [Routes.MAINTENANCE_SCHEDULE]: {
    name: Routes.MAINTENANCE_SCHEDULE,
    title: 'Maintenance Schedule',
    path: `/vessel/maintenance/${Routes.MAINTENANCE_SCHEDULE}`,
  },
  [Routes.MAINTENANCE_SCHEDULE_VIEW]: {
    name: Routes.MAINTENANCE_SCHEDULE_VIEW,
    title: 'Maintenance Schedule - View',
    path: `/vessel/maintenance/${Routes.MAINTENANCE_SCHEDULE}/view`,
  },

  [Routes.JOBLIST]: {
    name: Routes.JOBLIST,
    title: 'Job List',
    path: `/vessel/maintenance/${Routes.JOBLIST}`,
  },
  [Routes.JOBLIST_VIEW]: {
    name: Routes.JOBLIST_VIEW,
    title: 'Job List - View',
    path: `/vessel/maintenance/${Routes.JOBLIST}/view`,
  },

  [Routes.SPARE_PARTS_LIST]: {
    name: Routes.SPARE_PARTS_LIST,
    title: 'Spare Parts List',
    path: `/vessel/maintenance/${Routes.SPARE_PARTS_LIST}`,
  },
  [Routes.SPARE_PARTS_LIST_VIEW]: {
    name: Routes.SPARE_PARTS_LIST_VIEW,
    title: 'Spare Parts List - View',
    path: `/vessel/maintenance/${Routes.SPARE_PARTS_LIST}/view`,
  },

  [Routes.EQUIPMENT_LIST]: {
    name: Routes.EQUIPMENT_LIST,
    title: 'Equipment List',
    path: `/vessel/maintenance/${Routes.EQUIPMENT_LIST}`,
  },
  [Routes.EQUIPMENT_LIST_VIEW]: {
    name: Routes.EQUIPMENT_LIST_VIEW,
    title: 'Equipment List',
    path: `/vessel/maintenance/${Routes.EQUIPMENT_LIST}/view`,
  },

  [Routes.EQUIPMENT_MANUALS]: {
    name: Routes.EQUIPMENT_MANUALS,
    title: 'Equipment Manuals',
    path: `/vessel/maintenance/${Routes.EQUIPMENT_MANUALS}`,
  },
  [Routes.EQUIPMENT_MANUALS_VIEW]: {
    name: Routes.EQUIPMENT_MANUALS_VIEW,
    title: 'Equipment Manuals - View',
    path: `/vessel/maintenance/${Routes.EQUIPMENT_MANUALS}/view`,
  },
  [Routes.MAINTENANCE_HISTORY]: {
    name: Routes.MAINTENANCE_HISTORY,
    title: 'Maintenance History',
    path: `/vessel/maintenance/${Routes.MAINTENANCE_HISTORY}`,
  },
  [Routes.MAINTENANCE_HISTORY_VIEW]: {
    name: Routes.MAINTENANCE_HISTORY_VIEW,
    title: 'Maintenance History - View',
    path: `/vessel/maintenance/${Routes.MAINTENANCE_HISTORY}/view`,
  },

  // Vessel Document Register Section
  [Routes.VESSEL_DOCUMENT_REGISTER_DASHBOARD]: {
    name: Routes.VESSEL_DOCUMENT_REGISTER_DASHBOARD,
    title: 'Vessel Document Register',
    path: `/vessel/vessel-document-register/${Routes.VESSEL_DOCUMENT_REGISTER_DASHBOARD}`,
    icon: { name: 'description' },
    children: {
      [Routes.VESSEL_CERTIFICATES]: {},
      [Routes.VESSEL_DOCUMENTS]: {},
      [Routes.SURVEY_DOCUMENTS]: {},
      [Routes.STANDARD_OPERATING_PROCEDURES]: {},
    },
  },
  [Routes.VESSEL_CERTIFICATES]: {
    name: Routes.VESSEL_CERTIFICATES,
    title: 'Vessel Certificates',
    path: `/vessel/vessel-document-register/${Routes.VESSEL_CERTIFICATES}`,
  },
  [Routes.VESSEL_CERTIFICATES_VIEW]: {
    name: Routes.VESSEL_CERTIFICATES_VIEW,
    title: 'Vessel Certificates - View',
    path: `/vessel/vessel-document-register/${Routes.VESSEL_CERTIFICATES}/view`,
  },
  [Routes.VESSEL_DOCUMENTS]: {
    name: Routes.VESSEL_DOCUMENTS,
    title: 'Vessel Documents',
    path: `/vessel/vessel-document-register/${Routes.VESSEL_DOCUMENTS}`,
  },
  [Routes.VESSEL_DOCUMENTS_VIEW]: {
    name: Routes.VESSEL_DOCUMENTS,
    title: 'Vessel Documents - View',
    path: `/vessel/vessel-document-register/${Routes.VESSEL_DOCUMENTS}/view`,
  },
  [Routes.SURVEY_DOCUMENTS]: {
    name: Routes.SURVEY_DOCUMENTS,
    title: 'Survey Documents',
    path: `/vessel/vessel-document-register/${Routes.SURVEY_DOCUMENTS}`,
  },
  [Routes.SURVEY_DOCUMENTS_VIEW]: {
    name: Routes.SURVEY_DOCUMENTS_VIEW,
    title: 'Survey Documents',
    path: `/vessel/vessel-document-register/${Routes.SURVEY_DOCUMENTS}/view`,
  },
  [Routes.STANDARD_OPERATING_PROCEDURES]: {
    name: Routes.STANDARD_OPERATING_PROCEDURES,
    title: 'Standard Operating Procedures',
    path: `/vessel/vessel-document-register/${Routes.STANDARD_OPERATING_PROCEDURES}`,
  },
  [Routes.STANDARD_OPERATING_PROCEDURES_VIEW]: {
    name: Routes.STANDARD_OPERATING_PROCEDURES_VIEW,
    title: 'Standard Operating Procedures - View',
    path: `/vessel/vessel-document-register/${Routes.STANDARD_OPERATING_PROCEDURES}/view`,
  },

  // Health and Safety Section
  [Routes.HEALTH_SAFETY_DASHBOARD]: {
    name: Routes.HEALTH_SAFETY_DASHBOARD,
    title: 'Health & Safety',
    path: `/healthSafety/${Routes.HEALTH_SAFETY_DASHBOARD}`,
    icon: { name: 'health_and_safety' },
    children: {
      [Routes.INCIDENT_REPORT]: {},
      [Routes.CORRECTIVE_ACTION]: {},
      [Routes.RISK_ASSESSMENT]: {},
      [Routes.HEALTH_SAFETY_MEETING]: {},
      [Routes.DANGEROUS_GOODS_REGISTER]: {},
    },
  },
  [Routes.INCIDENT_REPORT]: {
    name: Routes.INCIDENT_REPORT,
    title: 'Incident / Event Reports',
    path: `/healthSafety/${Routes.INCIDENT_REPORT}`,
    children: {
      [Routes.INCIDENT_REPORT_LIST]: {},
      [Routes.INCIDENT_REPORT_VIEW]: {},
      [Routes.INCIDENT_REPORT_EDIT]: {},
    },
  },
  [Routes.INCIDENT_REPORT_LIST]: {
    name: Routes.INCIDENT_REPORT_LIST,
    title: 'Incident / Event Reports',
    path: `/healthSafety/${Routes.INCIDENT_REPORT}`,
  },
  [Routes.INCIDENT_REPORT_VIEW]: {
    name: Routes.INCIDENT_REPORT_VIEW,
    title: 'Incident / Event Reports - View',
    path: `/healthSafety/${Routes.INCIDENT_REPORT}/view`,
  },
  [Routes.INCIDENT_REPORT_EDIT]: {
    name: Routes.INCIDENT_REPORT_EDIT,
    title: 'Incident / Event Reports - Edit',
    path: `/healthSafety/${Routes.INCIDENT_REPORT}/edit`,
  },

  [Routes.CORRECTIVE_ACTION]: {
    name: Routes.CORRECTIVE_ACTION,
    title: 'Corrective Actions',
    path: `/healthSafety/${Routes.CORRECTIVE_ACTION}`,
    children: {
      [Routes.CORRECTIVE_ACTION_LIST]: {},
      [Routes.CORRECTIVE_ACTION_VIEW]: {},
      [Routes.CORRECTIVE_ACTION_EDIT]: {},
    },
  },
  [Routes.CORRECTIVE_ACTION_LIST]: {
    name: Routes.CORRECTIVE_ACTION_LIST,
    title: 'Corrective Actions',
    path: `/healthSafety/${Routes.CORRECTIVE_ACTION}`,
  },
  [Routes.CORRECTIVE_ACTION_VIEW]: {
    name: Routes.CORRECTIVE_ACTION_VIEW,
    title: 'Corrective Actions - View',
    path: `/healthSafety/${Routes.CORRECTIVE_ACTION}/view`,
  },
  [Routes.CORRECTIVE_ACTION_EDIT]: {
    name: Routes.CORRECTIVE_ACTION_EDIT,
    title: 'Corrective Actions - Edit',
    path: `/healthSafety/${Routes.CORRECTIVE_ACTION}/edit`,
  },

  [Routes.RISK_ASSESSMENT]: {
    name: Routes.RISK_ASSESSMENT,
    title: 'Risk Assessments',
    path: `/healthSafety/${Routes.RISK_ASSESSMENT}`,
    children: {
      [Routes.RISK_ASSESSMENT_LIST]: {},
      [Routes.RISK_ASSESSMENT_VIEW]: {},
      [Routes.RISK_ASSESSMENT_EDIT]: {},
    },
  },
  [Routes.RISK_ASSESSMENT_LIST]: {
    name: Routes.RISK_ASSESSMENT_LIST,
    title: 'Risk Assessments',
    path: `/healthSafety/${Routes.RISK_ASSESSMENT}`,
  },
  [Routes.RISK_ASSESSMENT_VIEW]: {
    name: Routes.RISK_ASSESSMENT_VIEW,
    title: 'Risk Assessments - View',
    path: `/healthSafety/${Routes.RISK_ASSESSMENT}/view`,
  },
  [Routes.RISK_ASSESSMENT_EDIT]: {
    name: Routes.RISK_ASSESSMENT_EDIT,
    title: 'Risk Assessments - Edit',
    path: `/healthSafety/${Routes.RISK_ASSESSMENT}/edit`,
  },

  [Routes.HEALTH_SAFETY_MEETING]: {
    name: Routes.HEALTH_SAFETY_MEETING,
    title: 'Health & Safety Meetings',
    path: `/healthSafety/${Routes.HEALTH_SAFETY_MEETING}`,
    children: {
      [Routes.HEALTH_SAFETY_MEETING_LIST]: {},
      [Routes.HEALTH_SAFETY_MEETING_VIEW]: {},
      [Routes.HEALTH_SAFETY_MEETING_EDIT]: {},
    },
  },
  [Routes.HEALTH_SAFETY_MEETING_LIST]: {
    name: Routes.HEALTH_SAFETY_MEETING_LIST,
    title: 'Health & Safety Meetings',
    path: `/healthSafety/${Routes.HEALTH_SAFETY_MEETING}`,
  },
  [Routes.HEALTH_SAFETY_MEETING_VIEW]: {
    name: Routes.HEALTH_SAFETY_MEETING_VIEW,
    title: 'Health & Safety Meetings - View',
    path: `/healthSafety/${Routes.HEALTH_SAFETY_MEETING}/view`,
  },
  [Routes.HEALTH_SAFETY_MEETING_EDIT]: {
    name: Routes.HEALTH_SAFETY_MEETING_EDIT,
    title: 'Health & Safety Meetings - Edit',
    path: `/healthSafety/${Routes.HEALTH_SAFETY_MEETING}/edit`,
  },

  [Routes.DANGEROUS_GOODS_REGISTER]: {
    name: Routes.DANGEROUS_GOODS_REGISTER,
    title: 'Dangerous Goods Register',
    path: `/healthSafety/${Routes.DANGEROUS_GOODS_REGISTER}`,
    children: {
      [Routes.DANGEROUS_GOODS_REGISTER_LIST]: {},
      [Routes.DANGEROUS_GOODS_REGISTER_VIEW]: {},
      [Routes.DANGEROUS_GOODS_REGISTER_EDIT]: {},
    },
  },
  [Routes.DANGEROUS_GOODS_REGISTER_LIST]: {
    name: Routes.DANGEROUS_GOODS_REGISTER_LIST,
    title: 'Dangerous Goods Register',
    path: `/healthSafety/${Routes.DANGEROUS_GOODS_REGISTER}`,
  },
  [Routes.DANGEROUS_GOODS_REGISTER_VIEW]: {
    name: Routes.DANGEROUS_GOODS_REGISTER_VIEW,
    title: 'Dangerous Goods Register - View',
    path: `/healthSafety/${Routes.DANGEROUS_GOODS_REGISTER}/view`,
  },
  [Routes.DANGEROUS_GOODS_REGISTER_EDIT]: {
    name: Routes.DANGEROUS_GOODS_REGISTER_EDIT,
    title: 'Dangerous Goods Register - Edit',
    path: `/healthSafety/${Routes.DANGEROUS_GOODS_REGISTER}/edit`,
  },

  // Company Document Register Section
  [Routes.DOCUMENT_REGISTER]: {
    // Parent Group Route
    name: Routes.DOCUMENT_REGISTER,
    title: 'Company Document Register',
  },
  [Routes.COMPANY_DOCUMENT_REGISTER_DASHBOARD]: {
    name: Routes.COMPANY_DOCUMENT_REGISTER_DASHBOARD,
    title: 'Company Document Register',
    path: `/documentRegister/${Routes.COMPANY_DOCUMENT_REGISTER_DASHBOARD}`,
    icon: { name: 'file_copy' },
    children: {
      [Routes.COMPANY_PLAN]: {},
      [Routes.COMPANY_DOCUMENTS]: {},
      [Routes.CUSTOM_FORMS]: {},
    },
  },
  [Routes.COMPANY_PLAN]: {
    name: Routes.COMPANY_PLAN,
    title: 'Company Plan',
    path: `/documentRegister/${Routes.COMPANY_PLAN}`,
  },
  [Routes.COMPANY_DOCUMENTS]: {
    name: Routes.COMPANY_DOCUMENTS,
    title: 'Company Documents',
    path: `/documentRegister/${Routes.COMPANY_DOCUMENTS}`,
  },
  [Routes.COMPANY_DOCUMENTS_VIEW]: {
    name: Routes.COMPANY_DOCUMENTS_VIEW,
    title: 'Company Documents - View',
    path: `/documentRegister/${Routes.COMPANY_DOCUMENTS}/view`,
  },
  [Routes.CUSTOM_FORMS]: {
    name: Routes.CUSTOM_FORMS,
    title: 'Forms / Checklists',
    path: `/documentRegister/${Routes.CUSTOM_FORMS}`,
  },
  [Routes.CUSTOM_FORMS_VIEW]: {
    name: Routes.CUSTOM_FORMS_VIEW,
    title: 'Forms / Checklists - View',
    path: `/documentRegister/${Routes.CUSTOM_FORMS}/view`,
  },
  [Routes.CUSTOM_FORMS_BUILDER]: {
    name: Routes.CUSTOM_FORMS_BUILDER,
    title: 'Forms / Checklists - History',
    path: `/documentRegister/${Routes.CUSTOM_FORMS}/formBuilder`,
  },
  [Routes.CUSTOM_FORM_BROWSE_TEMPLATES]: {
    name: Routes.CUSTOM_FORM_BROWSE_TEMPLATES,
    title: 'Forms / Checklists - Browse Templates',
    path: `/documentRegister/${Routes.CUSTOM_FORMS}/browseTemplates`,
  },

  // Crew Section
  [Routes.CREW_DASHBOARD]: {
    name: Routes.CREW_DASHBOARD,
    title: 'Crew',
    path: `/crew/${Routes.CREW_DASHBOARD}`,
    icon: { name: 'groups' },
    children: {
      [Routes.CREW_PARTICULARS]: {},
      [Routes.CREW_CERTIFICATES]: {},
      [Routes.CREW_TRAINING]: {},
      [Routes.CREW_CONTACTS]: {},
    },
  },
  [Routes.CREW_PARTICULARS]: {
    name: Routes.CREW_PARTICULARS,
    title: 'Crew Particulars',
    path: `/crew/${Routes.CREW_PARTICULARS}`,
    children: {
      [Routes.CREW_PARTICULARS_VIEW]: {},
      [Routes.CREW_PARTICULARS_EDIT]: {},
    },
  },
  [Routes.CREW_PARTICULARS_EDIT]: {
    name: Routes.CREW_PARTICULARS_EDIT,
    title: 'Crew Particulars - Edit',
    path: `/crew/${Routes.CREW_PARTICULARS}/edit`,
  },

  [Routes.CREW_PARTICULARS_VIEW]: {
    name: Routes.CREW_PARTICULARS_VIEW,
    title: 'Crew Particulars - View',
    path: `/crew/${Routes.CREW_PARTICULARS}/view`,
    children: {
      [Routes.CREW_PARTICULARS_VIEW_PROFILE]: {},
      [Routes.CREW_PARTICULARS_VIEW_FORMS]: {},
      [Routes.CREW_PARTICULARS_VIEW_SEATIME]: {},
      [Routes.CREW_PARTICULARS_VIEW_CERTIFICATES]: {},
      [Routes.CREW_PARTICULARS_VIEW_DRILLS]: {},
      [Routes.CREW_PARTICULARS_VIEW_TRAINING]: {},
    },
  },
  [Routes.CREW_PARTICULARS_VIEW_PROFILE]: {
    name: Routes.CREW_PARTICULARS_VIEW_PROFILE,
    title: 'Profile',
    path: `/crew/${Routes.CREW_PARTICULARS}/view/profile`,
  },
  [Routes.CREW_PARTICULARS_VIEW_FORMS]: {
    name: Routes.CREW_PARTICULARS_VIEW_FORMS,
    title: 'Forms / Documents',
    path: `/crew/${Routes.CREW_PARTICULARS}/view/forms`,
  },
  [Routes.CREW_PARTICULARS_VIEW_SEATIME]: {
    name: Routes.CREW_PARTICULARS_VIEW_SEATIME,
    title: 'Sea Time',
    path: `/crew/${Routes.CREW_PARTICULARS}/view/seaTime`,
  },
  [Routes.CREW_PARTICULARS_VIEW_CERTIFICATES]: {
    name: Routes.CREW_PARTICULARS_VIEW_CERTIFICATES,
    title: 'Certificates',
    path: `/crew/${Routes.CREW_PARTICULARS}/view/certificates`,
  },
  [Routes.CREW_PARTICULARS_VIEW_DRILLS]: {
    name: Routes.CREW_PARTICULARS_VIEW_DRILLS,
    title: 'Drills',
    path: `/crew/${Routes.CREW_PARTICULARS}/view/drills`,
  },
  [Routes.CREW_PARTICULARS_VIEW_TRAINING]: {
    name: Routes.CREW_PARTICULARS_VIEW_TRAINING,
    title: 'Training',
    path: `/crew/${Routes.CREW_PARTICULARS}/view/training`,
  },
  [Routes.CREW_PARTICULARS_ARCHIVED]: {
    name: Routes.CREW_PARTICULARS_ARCHIVED,
    title: 'Archived Users',
    path: `/crew/${Routes.CREW_PARTICULARS}/archived`,
  },
  [Routes.CREW_PARTICULARS_ARCHIVED_VIEW_PROFILE]: {
    name: Routes.CREW_PARTICULARS_ARCHIVED_VIEW_PROFILE,
    title: 'Archived Profile',
    path: `/crew/${Routes.CREW_PARTICULARS}/archived/view/profile`,
  },

  [Routes.CREW_CERTIFICATES]: {
    name: Routes.CREW_CERTIFICATES,
    title: 'Crew Certificates',
    path: `/crew/${Routes.CREW_CERTIFICATES}`,
  },

  [Routes.CREW_CERTIFICATES_VIEW]: {
    name: Routes.CREW_CERTIFICATES_VIEW,
    title: 'View Crew Certificates',
    path: `/crew/${Routes.CREW_CERTIFICATES}/view`,
  },

  [Routes.CREW_CONTACTS]: {
    name: Routes.CREW_CONTACTS,
    title: 'Contacts/Suppliers',
    path: `/crew/${Routes.CREW_CONTACTS}`,
  },

  [Routes.CREW_CONTACTS_VIEW]: {
    name: Routes.CREW_CONTACTS_VIEW,
    title: 'View Contact',
    path: `/crew/${Routes.CREW_CONTACTS}/view`,
  },

  [Routes.CREW_TRAINING]: {
    name: Routes.CREW_TRAINING,
    title: 'Crew Training',
    path: `/crew/${Routes.CREW_TRAINING}`,
  },

  [Routes.CREW_TRAINING_VIEW]: {
    name: Routes.CREW_TRAINING_VIEW,
    title: 'Crew Training - View',
    path: `/crew/${Routes.CREW_TRAINING}/training`,
  },

  [Routes.CREW_TRAINING_VIEW_USER]: {
    name: Routes.CREW_TRAINING_VIEW_USER,
    title: 'Crew Training - User',
    path: `/crew/${Routes.CREW_TRAINING}/user`,
  },

  [Routes.CREW_TRAINING_VIEW_USER_TRAINING]: {
    name: Routes.CREW_TRAINING_VIEW_USER_TRAINING,
    title: 'Crew Training - Training',
    path: `/crew/${Routes.CREW_TRAINING}/user-training`,
  },

  [Routes.CREW_TRAINING_REPORT]: {
    name: Routes.CREW_TRAINING_REPORT,
    title: 'Crew Training - Report',
    path: `/crew/${Routes.CREW_TRAINING}/training-report`,
  },

  // Reporting
  [Routes.REPORTING]: {
    name: Routes.REPORTING,
    title: 'Reporting',
  },
  [Routes.REPORTING_DASHBOARD]: {
    name: Routes.REPORTING_DASHBOARD,
    title: 'Reporting',
    path: `/reporting/${Routes.REPORTING_DASHBOARD}`,
    icon: { name: 'bar_chart_4_bars' },
    children: {
      [Routes.REPORTING_LOGBOOK]: {},
      [Routes.REPORTING_SAFETY]: {},
      [Routes.REPORTING_MAINTENANCE]: {},
      [Routes.REPORTING_HEALTH_SAFETY]: {},
      [Routes.REPORTING_COMPANY_DOCUMENT]: {},
      [Routes.REPORTING_VESSEL_DOCUMENT]: {},
      [Routes.REPORTING_CREW]: {},
    },
  },
  [Routes.REPORTING_LOGBOOK]: {
    name: Routes.REPORTING_LOGBOOK,
    title: 'Logbook',
    path: `/reporting/${Routes.REPORTING_LOGBOOK}`,
  },

  [Routes.REPORTING_SAFETY]: {
    name: Routes.REPORTING_SAFETY,
    title: 'Safety',
    path: `/reporting/${Routes.REPORTING_SAFETY}`,
    children: {
      [Routes.REPORTING_SAFETY_EQUIPMENT_CHECKS]: {},
      [Routes.REPORTING_SAFETY_EQUIPMENT_CHECKS_VIEW]: {},
      [Routes.REPORTING_COMPLETED_SAFETY_CHECKS]: {},
      [Routes.REPORTING_COMPLETED_SAFETY_CHECKS_VIEW]: {},
      [Routes.REPORTING_SAFETY_EQUIPMENT_EXPIRIES]: {},
      [Routes.REPORTING_SAFETY_EQUIPMENT_EXPIRIES_VIEW]: {},
    },
  },
  [Routes.REPORTING_SAFETY_EQUIPMENT_CHECKS]: {
    name: Routes.REPORTING_SAFETY_EQUIPMENT_CHECKS,
    title: 'Safety Checks',
    path: `/reporting/${Routes.REPORTING_SAFETY}/safetyEquipmentChecks`,
  },
  [Routes.REPORTING_SAFETY_EQUIPMENT_CHECKS_VIEW]: {
    name: Routes.REPORTING_SAFETY_EQUIPMENT_CHECKS_VIEW,
    title: 'Safety Checks - View',
    path: `/reporting/${Routes.REPORTING_SAFETY}/safetyEquipmentChecks/view`,
  },
  [Routes.REPORTING_COMPLETED_SAFETY_CHECKS]: {
    name: Routes.REPORTING_COMPLETED_SAFETY_CHECKS,
    title: 'Completed Safety Checks',
    path: `/reporting/${Routes.REPORTING_SAFETY}/completedSafetyChecks`,
  },
  [Routes.REPORTING_COMPLETED_SAFETY_CHECKS_VIEW]: {
    name: Routes.REPORTING_COMPLETED_SAFETY_CHECKS_VIEW,
    title: 'Completed Safety Checks - View',
    path: `/reporting/${Routes.REPORTING_SAFETY}/completedSafetyChecks/view`,
  },
  [Routes.REPORTING_SAFETY_EQUIPMENT_EXPIRIES]: {
    name: Routes.REPORTING_SAFETY_EQUIPMENT_EXPIRIES,
    title: 'Safety Equipment Expiries',
    path: `/reporting/${Routes.REPORTING_SAFETY}/safetyEquipmentExpiries`,
  },
  [Routes.REPORTING_SAFETY_EQUIPMENT_EXPIRIES_VIEW]: {
    name: Routes.REPORTING_SAFETY_EQUIPMENT_EXPIRIES_VIEW,
    title: 'Safety Equipment Expiries - View',
    path: `/reporting/${Routes.REPORTING_SAFETY}/safetyEquipmentExpiries/view`,
  },

  [Routes.REPORTING_MAINTENANCE]: {
    name: Routes.REPORTING_MAINTENANCE,
    title: 'Maintenance',
    path: `/reporting/${Routes.REPORTING_MAINTENANCE}`,
    children: {
      [Routes.REPORTING_MAINTENANCE_SCHEDULE]: {},
      [Routes.REPORTING_MAINTENANCE_SCHEDULE_VIEW]: {},
      [Routes.REPORTING_MAINTENANCE_JOB]: {},
      [Routes.REPORTING_MAINTENANCE_JOB_VIEW]: {},
      [Routes.REPORTING_COMPLETED_MAINTENANCE_TASKS]: {},
      [Routes.REPORTING_COMPLETED_MAINTENANCE_TASKS_VIEW]: {},
    },
  },
  [Routes.REPORTING_MAINTENANCE_SCHEDULE]: {
    name: Routes.REPORTING_MAINTENANCE_SCHEDULE,
    title: 'Maintenance Schedule',
    path: `/reporting/${Routes.REPORTING_MAINTENANCE}/maintenanceSchedule`,
  },
  [Routes.REPORTING_MAINTENANCE_SCHEDULE_VIEW]: {
    name: Routes.REPORTING_MAINTENANCE_SCHEDULE_VIEW,
    title: 'Maintenance Schedule - View',
    path: `/reporting/${Routes.REPORTING_MAINTENANCE}/maintenanceSchedule/view`,
  },
  [Routes.REPORTING_MAINTENANCE_JOB]: {
    name: Routes.REPORTING_MAINTENANCE_JOB,
    title: 'Job Tasks',
    path: `/reporting/${Routes.REPORTING_MAINTENANCE}/job`,
  },
  [Routes.REPORTING_MAINTENANCE_JOB_VIEW]: {
    name: Routes.REPORTING_MAINTENANCE_JOB_VIEW,
    title: 'Job - View',
    path: `/reporting/${Routes.REPORTING_MAINTENANCE}/job/view`,
  },
  [Routes.REPORTING_COMPLETED_MAINTENANCE_TASKS]: {
    name: Routes.REPORTING_COMPLETED_MAINTENANCE_TASKS,
    title: 'Completed Maintenance Tasks',
    path: `/reporting/${Routes.REPORTING_MAINTENANCE}/completedMaintenanceTask`,
  },
  [Routes.REPORTING_COMPLETED_MAINTENANCE_TASKS_VIEW]: {
    name: Routes.REPORTING_COMPLETED_MAINTENANCE_TASKS_VIEW,
    title: 'Completed Maintenance Tasks - View',
    path: `/reporting/${Routes.REPORTING_MAINTENANCE}/completedMaintenanceTask/view`,
  },

  // Reporting Health & Safety
  [Routes.REPORTING_HEALTH_SAFETY]: {
    name: Routes.REPORTING_HEALTH_SAFETY,
    title: 'Health & Safety',
    path: `/reporting/${Routes.REPORTING_HEALTH_SAFETY}`,
    children: {
      [Routes.REPORTING_HEALTH_SAFETY_INCIDENT]: {},
      [Routes.REPORTING_HEALTH_SAFETY_INCIDENT_TYPE]: {},
      [Routes.REPORTING_HEALTH_SAFETY_INCIDENT_CATEGORY]: {},
      [Routes.REPORTING_HEALTH_SAFETY_INCIDENT_CONTRIBUTING_FACTOR]: {},
      [Routes.REPORTING_HEALTH_SAFETY_INCIDENT_INJURY_TYPE]: {},
      [Routes.REPORTING_HEALTH_SAFETY_INCIDENT_INJURY_LOCATION]: {},
      [Routes.REPORTING_HEALTH_SAFETY_INCIDENT_NOTIFIED_TO_AUTHORITY]: {},
      [Routes.REPORTING_HEALTH_SAFETY_INCIDENT_PERSONNEL_INVOLVED]: {},
      [Routes.REPORTING_HEALTH_SAFETY_INCIDENT_INJURY_OUTCOME]: {},
    },
  },
  [Routes.REPORTING_HEALTH_SAFETY_INCIDENT]: {
    name: Routes.REPORTING_HEALTH_SAFETY_INCIDENT,
    title: 'Incident / Event Reports',
    path: `/reporting/${Routes.REPORTING_HEALTH_SAFETY}/incident`,
  },
  [Routes.REPORTING_HEALTH_SAFETY_INCIDENT_TYPE]: {
    name: Routes.REPORTING_HEALTH_SAFETY_INCIDENT_TYPE,
    title: 'Incident / Event Types',
    path: `/reporting/${Routes.REPORTING_HEALTH_SAFETY}/incidentType`,
  },
  [Routes.REPORTING_HEALTH_SAFETY_INCIDENT_CATEGORY]: {
    name: Routes.REPORTING_HEALTH_SAFETY_INCIDENT_CATEGORY,
    title: 'Incident / Event Categories',
    path: `/reporting/${Routes.REPORTING_HEALTH_SAFETY}/incidentCategory`,
  },
  [Routes.REPORTING_HEALTH_SAFETY_INCIDENT_CONTRIBUTING_FACTOR]: {
    name: Routes.REPORTING_HEALTH_SAFETY_INCIDENT_CONTRIBUTING_FACTOR,
    title: 'Incident / Event Contributing Factors',
    path: `/reporting/${Routes.REPORTING_HEALTH_SAFETY}/incidentContributingFactor`,
  },
  [Routes.REPORTING_HEALTH_SAFETY_INCIDENT_INJURY_TYPE]: {
    name: Routes.REPORTING_HEALTH_SAFETY_INCIDENT_INJURY_TYPE,
    title: 'Incident / Event Injury Types',
    path: `/reporting/${Routes.REPORTING_HEALTH_SAFETY}/incidentInjuryType`,
  },
  [Routes.REPORTING_HEALTH_SAFETY_INCIDENT_INJURY_LOCATION]: {
    name: Routes.REPORTING_HEALTH_SAFETY_INCIDENT_INJURY_LOCATION,
    title: 'Incident / Event Injury Locations',
    path: `/reporting/${Routes.REPORTING_HEALTH_SAFETY}/incidentInjuryLocation`,
  },
  [Routes.REPORTING_HEALTH_SAFETY_INCIDENT_NOTIFIED_TO_AUTHORITY]: {
    name: Routes.REPORTING_HEALTH_SAFETY_INCIDENT_NOTIFIED_TO_AUTHORITY,
    title: 'Incident / Event Notified to Authority',
    path: `/reporting/${Routes.REPORTING_HEALTH_SAFETY}/incidentNotifiedToAuthority`,
  },
  [Routes.REPORTING_HEALTH_SAFETY_INCIDENT_PERSONNEL_INVOLVED]: {
    name: Routes.REPORTING_HEALTH_SAFETY_INCIDENT_PERSONNEL_INVOLVED,
    title: 'Incident / Event Personnel Involved',
    path: `/reporting/${Routes.REPORTING_HEALTH_SAFETY}/incidentPersonnelInvolved`,
  },
  [Routes.REPORTING_HEALTH_SAFETY_INCIDENT_INJURY_OUTCOME]: {
    name: Routes.REPORTING_HEALTH_SAFETY_INCIDENT_INJURY_OUTCOME,
    title: 'Incident / Event Injury Outcome',
    path: `/reporting/${Routes.REPORTING_HEALTH_SAFETY}/incidentInjuryOutcome`,
  },

  // Reporting Company Document Register
  [Routes.REPORTING_COMPANY_DOCUMENT]: {
    name: Routes.REPORTING_COMPANY_DOCUMENT,
    title: 'Company Documents',
    path: `/reporting/${Routes.REPORTING_COMPANY_DOCUMENT}`,
  },

  [Routes.REPORTING_CREW]: {
    name: Routes.REPORTING_CREW,
    title: 'Crew',
    path: `/reporting/${Routes.REPORTING_CREW}`,
    children: {
      [Routes.REPORTING_CREW_CERTIFICATES]: {},
    },
  },
  [Routes.REPORTING_CREW_CERTIFICATES]: {
    name: Routes.REPORTING_CREW_CERTIFICATES,
    title: 'Crew Certificates',
    path: `/reporting/${Routes.REPORTING_CREW}/crewCertificate`,
  },

  [Routes.REPORTING_VESSEL_DOCUMENT]: {
    name: Routes.REPORTING_VESSEL_DOCUMENT,
    title: 'Vessel Documents',
    path: `/reporting/${Routes.REPORTING_VESSEL_DOCUMENT}`,
    children: {
      [Routes.REPORTING_VESSEL_DOCUMENT_VESSEL_CERTIFICATE]: {},
    },
  },
  [Routes.REPORTING_VESSEL_DOCUMENT_VESSEL_CERTIFICATE]: {
    name: Routes.REPORTING_VESSEL_DOCUMENT_VESSEL_CERTIFICATE,
    title: 'Vessel Certificates by Vessel',
    path: `/reporting/${Routes.REPORTING_VESSEL_DOCUMENT}/vesselCertificate`,
  },

  // Other routes
  [Routes.ACTION_LOG]: {
    name: Routes.ACTION_LOG,
    title: 'Action Log',
    path: `/${Routes.ACTION_LOG}`,
    icon: { name: 'assignment' },
  },
  [Routes.SUPPORT]: {
    name: Routes.SUPPORT,
    title: 'Support',
    path: `/${Routes.SUPPORT}`,
    icon: { name: 'contact_support' },
  },
  [Routes.PROFILE]: {
    name: Routes.PROFILE,
    title: 'Profile',
    path: `/${Routes.PROFILE}`,
  },
  [Routes.VOYAGE]: {
    name: Routes.VOYAGE,
    title: 'Voyage',
    path: `/vessel/${Routes.VOYAGE}`,
  },

  [Routes.VOYAGE_HISTORY]: {
    name: Routes.VOYAGE_HISTORY,
    title: 'Voyage History',
    path: `/${Routes.HOME}/vessel/voyageHistory`,
    icon: { name: 'history' },
  },

  [Routes.VOYAGE_HISTORY_VIEW]: {
    name: Routes.VOYAGE_HISTORY_VIEW,
    title: 'Voyage History - View',
    path: `/vessel/voyageHistory/view`,
  },

  [Routes.VESSEL_STATUS]: {
    name: Routes.VESSEL_STATUS,
    title: 'Vessel Status',
    path: `/vessel/${Routes.VESSEL_STATUS}`,
  },
  [Routes.ENGINE_HOURS]: {
    name: Routes.ENGINE_HOURS,
    title: 'Engine / Equipment Hours',
    path: `/vessel/${Routes.ENGINE_HOURS}`,
  },
  [Routes.FUEL]: {
    name: Routes.FUEL,
    title: 'Fuel',
    path: `/vessel/${Routes.FUEL}`,
  },
  [Routes.SURVEY_STATUS]: {
    name: Routes.SURVEY_STATUS,
    title: 'Survey Status',
    path: `/vessel/${Routes.SURVEY_STATUS}`,
  },
}
