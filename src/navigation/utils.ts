import { NavigationProp } from '@react-navigation/native'
import { RouteConfig, Routes, ROUTES_CONFIG } from '@src/navigation/constants'
import { useNavigation } from 'expo-router'

/**
 * Get the route configuration for the specified route
 * @param routeName - Expo Route name
 * @returns RouteConfig - the route configuration
 */
export const getRouteConfig = (routeName: Routes | string): RouteConfig => {
  if (routeName) {
    const routeConfig = ROUTES_CONFIG[routeName as Routes]
    if (routeConfig) {
      return routeConfig as RouteConfig
    }
  }

  // Return the default route
  return ROUTES_CONFIG[Routes.LOGIN] as RouteConfig
}

/**
 * Get the route path for the specified route name
 * @param routeName - Expo Route name
 * @returns string - the route path
 */
export const getRoutePath = (routeName: Routes | string): string => {
  const routePath = getRouteConfig(routeName)

  return routePath.path
}

/**
 * Get current navigation route name and params
 *
 * @param navigation
 */
export const getCurrentNavigationRoute = (navigation: NavigationProp<Record<string, object | undefined>>) => {
  const findCurrentRoute = (navState: any): any => {
    if (navState.index === undefined || navState.index < 0) {
      return navState
    }
    const nestedState = navState.routes[navState.index]
    if (nestedState.state) {
      return findCurrentRoute(nestedState.state)
    }
    return nestedState
  }

  if (navigation) {
    const navigationState = navigation?.getState()
    return findCurrentRoute(navigationState)
  }
  return undefined
}
