import { View } from 'react-native'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { ScrollablePageLayout } from '@src/layout/ScrollablePageLayout/ScrollablePageLayout'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { SeaPageCard, SeaPageCardTitle } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { CustomFormTemplate } from '@src/shared-state/CompanyDocuments/CustomForms/customFormTemplates'
import { sharedState } from '@src/shared-state/shared-state'
import { ViewCustomFormTemplateModal } from '@src/components/_organisms/CompanyDocumentRegister/CustomForm/ViewCustomFormTemplateModal'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { EditCustomFormDrawer } from '@src/components/_organisms/CompanyDocumentRegister/CustomForm/EditCustomFormDrawer'
import { DrawerMode } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { SeaButton } from '@src/components/_atoms/SeaButton/SeaButton'
import { CustomFormElementType } from '@src/components/_atoms/SeaCustomFormElement/SeaCustomFormElement'
import { useCustomFormTemplateVersions } from '@src/shared-state/CompanyDocuments/CustomForms/useCustomFormTemplateVersions'
import { makeFormElements } from '@src/lib/customForms'
import { BrowseTemplatesTable } from '@src/components/_organisms/CompanyDocumentRegister/CustomForm/BrowseTemplatesTable'

export function BrowseTemplates() {
  const customFormTemplates = sharedState.customFormTemplates.use()

  const { styles } = useStyles(styleSheet)
  const [issVisibleModal, setIsVisibleModal] = useState(false)
  const [isVisibleCreateDrawer, setIsVisibleCreateDrawer] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<CustomFormTemplate | undefined>(undefined)
  const [formElements, setFormElements] = useState<CustomFormElementType[]>([])

  const { isMobileWidth } = useDeviceWidth()
  const templateVersion = useCustomFormTemplateVersions(selectedTemplate)

  const data = useMemo(() => {
    if (!customFormTemplates) return []

    const allOptions = customFormTemplates.categories.reduce((acc, category) => {
      if (!customFormTemplates.byCategory[category]) return acc
      const data = customFormTemplates.byCategory[category]

      acc.push(...data)
      return acc
    }, [] as CustomFormTemplate[])

    return allOptions
  }, [customFormTemplates])

  const handlePress = useCallback((item: CustomFormTemplate) => {
    const selectedItem = { ...item, categoryId: '' }
    setSelectedTemplate(selectedItem)
    setIsVisibleModal(true)
  }, [])

  useEffect(() => {
    console.log('TTT - render BrowseTemplates')
    if (templateVersion && selectedTemplate) {
      setFormElements(makeFormElements(templateVersion, selectedTemplate))
    } else {
      setFormElements([])
    }

    return () => {
      setFormElements([])
    }
  }, [selectedTemplate, templateVersion])

  return (
    <>
      <ScrollablePageLayout>
        <RequirePermissions role="customForms" level={permissionLevels.CREATE} showDenial={true}>
          <SeaPageCard titleComponent={<SeaPageCardTitle title="Forms/Checklists Templates" />} />

          {/* Table View */}
          <View style={styles.tableView}>
            <BrowseTemplatesTable data={data} handlePress={handlePress} />
          </View>
        </RequirePermissions>
      </ScrollablePageLayout>
      {issVisibleModal && (
        <ViewCustomFormTemplateModal
          selectedTemplate={selectedTemplate}
          formElements={formElements}
          visible={issVisibleModal}
          onClose={() => setIsVisibleModal(false)}
          maxWidth={850}
          maxHeight={isMobileWidth ? '85%' : '95%'}
          actions={
            <SeaButton
              label="Use This Template"
              onPress={() => {
                setIsVisibleModal(false)

                // SINCE THERE ARE 2 MODALS, ONE GETS CLOSED AND THE OTHER OPENS
                // WE NEED TO DELAY THE OPENING OF THE DRAWER TO ENSURE THE MODAL CLOSES FIRST
                // OTHERWISE, THE SECOND MODAL DOES NOT OPEN AND THE SELECTED TEMPLATE IS NOT SET
                setTimeout(() => {
                  setIsVisibleCreateDrawer(true)
                }, 100) // Delay to ensure modal closes before opening drawer
              }}
            />
          }
        />
      )}
      {isVisibleCreateDrawer && (
        <EditCustomFormDrawer
          onClose={() => setIsVisibleCreateDrawer(false)}
          visible={isVisibleCreateDrawer}
          mode={DrawerMode.Create}
          selectedTemplate={{ ...selectedTemplate, version: templateVersion }}
        />
      )}
    </>
  )
}

const styleSheet = createStyleSheet(() => ({
  container: {
    flex: 1,
  },
  tableView: {
    marginTop: 16,
  },
}))
