import React, { useCallback, useEffect, useMemo, useState } from 'react'
import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
  SeaTableRow,
} from '@src/components/_atoms/SeaTable/SeaTable'
import { DangerousGood } from '@src/shared-state/HealthSafety/dangerousGoods'
import { StyleSheet, Text, View } from 'react-native'
import { sharedState } from '@src/shared-state/shared-state'
import { logPageView } from '@src/lib/firebase'
import { formatValue, formatVessels } from '@src/lib/util'
import { warnDays } from '@src/lib/datesAndTime'
import { WhenDueStatus } from '@src/components/_molecules/WhenDueStatus/WhenDueStatus'
import { useRouter } from 'expo-router'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { SeaPageCard, SeaPageCardTitle, SubNav } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { SeaAddButton } from '@src/components/_molecules/IconButtons/SeaAddButton'
import { SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { EditDangerousGoodsRegister } from '@src/components/_organisms/HealthSafety/DangerousGoodsRegister/EditDangerousGoodsRegister'
import { SeaFilterRow } from '@src/components/_molecules/SeaFilterRow/SeaFilterRow'
import { VesselFilterDropdown } from '@src/components/_molecules/VesselFilterDropdown/VesselFilterDropdown'
import { useFilteredList } from '@src/hooks/filters/useFilteredList'
import { searchFilter } from '@src/hooks/filters/searchFilter'
import { useDebouncedSearch } from '@src/hooks/useDebouncedSearch'
import { ScrollablePageLayout } from '@src/layout/ScrollablePageLayout/ScrollablePageLayout'
import { SeaTableImage } from '@src/components/_atoms/SeaTable/SeaTableImage'
import {
  SeaTableIconCalendar,
  SeaTableIconLocation,
  SeaTableIconVessel,
} from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'
import { DrawerMode } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { useFocusEffect } from '@react-navigation/native'

interface DangerousGoodsRegisterProps {
  headerSubNavigation?: SubNav[]
}

export const DangerousGoodsRegister = ({ headerSubNavigation }: DangerousGoodsRegisterProps) => {
  // Shared Data
  const dangerousGoods = sharedState.dangerousGoods.use()
  const user = sharedState.user.use()
  const vessels = sharedState.vessels.use()
  const vesselId = sharedState.vesselId.use()

  // Hooks
  const router = useRouter()
  const [addDangerousGoodVisible, setAddDangerousGoodVisible] = useState(false)
  const [filterVesselIds, setFilterVesselIds] = useState<string[]>(user?.vesselIds ?? [])

  // Filter States
  const [isSearchFilterActive, setIsSearchFilterActive] = useState(false)

  // Search
  const { searchValue, debouncedSearchValue, setSearchValue } = useDebouncedSearch(300)
  // TODO: Export
  // const [exportType, setExportType] = useState<ExportType>();

  useFocusEffect(
    useCallback(() => {
      logPageView('HealthSafety/DangerousGoodsRegister')
    }, [])
  )

  useFocusEffect(
    useCallback(() => {
      user?.vesselIds && setFilterVesselIds(user?.vesselIds)
    }, [user?.vesselIds])
  )

  const allItems = useMemo(() => {
    let _dangerousGoods: DangerousGood[] = []

    if (filterVesselIds) {
      const doneDangerousGood = {} as Record<string, true>
      filterVesselIds.forEach(vesselId => {
        dangerousGoods?.byVesselId[vesselId]?.forEach(dangerousGood => {
          if (!doneDangerousGood[dangerousGood.id]) {
            doneDangerousGood[dangerousGood.id] = true
            _dangerousGoods.push(dangerousGood)
          }
        })
      })

      if (filterVesselIds.length > 1) {
        _dangerousGoods.sort((a, b) => {
          return a.name.localeCompare(b.name)
        })
      }
    } else {
      _dangerousGoods = dangerousGoods?.all ?? []
    }
    return _dangerousGoods
  }, [dangerousGoods, filterVesselIds])

  // Build filtered list using the new filtering system
  const { data: filteredDangerousGoods, filterText } = useFilteredList({
    data: allItems,
    filters: [
      searchFilter<DangerousGood>(isSearchFilterActive, debouncedSearchValue, good => [
        good.name ?? '',
        good.quantity ?? '',
        good.location ?? '',
        good.class ?? '',
      ]),
    ],
  })

  // Combined search handler
  const handleSearchFilterChange = (value: string) => {
    setSearchValue(value)

    // Set search as active filter if there's text, otherwise deactivate
    if (value.trim()) {
      setIsSearchFilterActive(true)
    } else {
      setIsSearchFilterActive(false)
    }
  }

  // Primary filters array
  const primaryFilters = [
    <VesselFilterDropdown key="vessel-filter" vesselIds={filterVesselIds} setVesselIds={setFilterVesselIds} />,
  ]

  /**
   * Get the columns for the Table
   *
   * @return SeaTableColumn<DangerousGood>[]
   */
  const tableColumns = useMemo((): SeaTableColumn<DangerousGood>[] => {
    return [
      {
        label: '',
        width: 60,
        render: item => <SeaTableImage files={item?.imageFiles} />,
        compactModeOptions: {
          isThumbnail: true,
        },
      },
      {
        label: 'Chemical',
        value: item => item.name,
        compactModeOptions: {
          rowPosition: CompactRowPosition.Title,
        },
      },
      {
        label: 'Vessels / Facilities',
        icon: () => <SeaTableIconVessel />,
        value: item => formatVessels(item.vesselIds, vessels),
        compactModeOptions: {
          label: {
            show: true,
            valueWidth: CompactValueWidth.Medium,
            name: 'Vessel',
          },
        },
      },
      {
        label: 'Quantity',
        value: item => formatValue(item.quantity),
        compactModeOptions: {
          label: {
            show: true,
            valueWidth: CompactValueWidth.Medium,
          },
        },
      },
      {
        label: 'Location',
        icon: () => <SeaTableIconLocation />,
        value: item => formatValue(item.location),
        compactModeOptions: {
          hideRow: true,
        },
      },
      {
        label: 'Hazardous',
        value: item => (item.isHazardous ? 'Yes' : 'No'),
        compactModeOptions: {
          hideRow: true,
        },
      },
      {
        label: 'DG Class',
        value: item => formatValue(item.class),
        compactModeOptions: {
          hideRow: true,
        },
      },
      {
        label: 'SDS',
        render: item => {
          if (item?.msdsFiles.length > 0) {
            return <SeaTableImage files={item.msdsFiles} />
          }
          return <Text>-</Text>
        },
        compactModeOptions: {
          hideRow: true,
        },
      },
      {
        label: 'Expiry',
        icon: () => <SeaTableIconCalendar />,
        value: item =>
          item.dateExpires && (
            <WhenDueStatus
              whenDue={item.dateExpires}
              warnDaysThreshold={warnDays.safetyEquipmentChecks[0]}
              hasFault={false}
            />
          ),
        compactModeOptions: {
          label: {
            show: true,
            valueWidth: CompactValueWidth.Medium,
          },
        },
      },
    ]
  }, [vessels])

  /**
   * Get the rows for the Table
   *
   * @param items - the filtered list of risks
   * @return SeaTableRow<Riskt>[]
   */
  const tableRows = useMemo(() => {
    return filteredDangerousGoods.map(item => {
      return {
        data: item,
        onPress: (item: DangerousGood) => {
          return router.navigate({
            pathname: getRoutePath(Routes.DANGEROUS_GOODS_REGISTER_VIEW),
            params: {
              dangerousGoodId: item.id,
            },
          })
        },
      }
    })
  }, [router, filteredDangerousGoods])

  const newDangerousGoodData: DangerousGood = {
    vesselIds: vesselId ? [vesselId] : [],
    name: '',
    quantity: '',
    location: '',
    isHazardous: false,
    class: '',
    imageFiles: [],
    msdsFiles: [],
    state: 'active',
    dateExpires: '',
  }

  return (
    <RequirePermissions role="dangerousGoodsRegister" level={permissionLevels.VIEW} showDenial={true}>
      <ScrollablePageLayout>
        <SeaPageCard
          titleComponent={<SeaPageCardTitle title={'Dangerous Goods Register'} />}
          primaryActionButton={
            <RequirePermissions
              key="dangerousGoodsRegister"
              role="dangerousGoodsRegister"
              level={permissionLevels.CREATE}>
              <SeaAddButton
                label={'Add New'}
                variant={SeaButtonVariant.Primary}
                onPress={() => setAddDangerousGoodVisible(true)}
              />
            </RequirePermissions>
          }
          secondaryActionButton={[
            <SeaDownloadButton key={'download'} onPress={() => alert('This functionality is not completed yet')} />,
          ]}
          subNav={headerSubNavigation}
        />

        {/* Filter Row */}
        <SeaFilterRow
          searchValue={searchValue}
          onSearchValueChange={handleSearchFilterChange}
          primaryFilters={primaryFilters}
          secondaryFilters={[]}
          filterText={filterText}
        />

        {/* Table View */}
        <View style={styles.tableView}>
          {/* TODO: Filter Tags */}
          <SeaTable columns={tableColumns} rows={tableRows} />
        </View>
      </ScrollablePageLayout>
      {addDangerousGoodVisible && (
        <EditDangerousGoodsRegister
          dangerousGood={newDangerousGoodData}
          visible={addDangerousGoodVisible}
          onClose={() => setAddDangerousGoodVisible(false)}
          mode={DrawerMode.Create}
        />
      )}
    </RequirePermissions>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  tableView: {
    marginTop: 16,
  },
})
