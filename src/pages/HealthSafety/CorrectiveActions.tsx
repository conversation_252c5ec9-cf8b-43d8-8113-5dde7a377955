import { SeaPageCard, SeaPageCardTitle } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { sharedState } from '@src/shared-state/shared-state'
import { useRouter } from 'expo-router'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { CorrectiveAction } from '@src/shared-state/HealthSafety/correctiveActions'
import { formatValue, makeDateTime } from '@src/lib/util'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { SeaAddButton } from '@src/components/_molecules/IconButtons/SeaAddButton'
import { SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaSettingsButton } from '@src/components/_molecules/IconButtons/SeaSettingsButton'
import { SeaFilterRow } from '@src/components/_molecules/SeaFilterRow/SeaFilterRow'
import { VesselFilterDropdown } from '@src/components/_molecules/VesselFilterDropdown/VesselFilterDropdown'
import { useFilteredList } from '@src/hooks/filters/useFilteredList'
import { searchFilter } from '@src/hooks/filters/searchFilter'
import { useDebouncedSearch } from '@src/hooks/useDebouncedSearch'
import { StyleSheet, View } from 'react-native'
import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
} from '@src/components/_atoms/SeaTable/SeaTable'
import { DrawerMode } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { ScrollablePageLayout } from '@src/layout/ScrollablePageLayout/ScrollablePageLayout'
import { SeaCheckButton } from '@src/components/_molecules/IconButtons/SeaCheckButton'
import { EditCorrectiveActionDrawer } from '@src/components/_organisms/HealthSafety/CorrectiveActions/EditCorrectiveActionDrawer'
import {
  SeaTableIconCalendar,
  SeaTableIconPerson,
  SeaTableIconVessel,
} from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'
import { SeaTableImage } from '@src/components/_atoms/SeaTable/SeaTableImage'
import { formatDateShort } from '@src/lib/datesAndTime'
import { renderVesselsList } from '@src/shared-state/Core/vessels'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { CorrectiveActionSettingsDrawer } from '@src/components/_organisms/HealthSafety/CorrectiveActions/CorrectiveActionSettingsDrawer'
import { SubNav } from '@src/components/_molecules/SeaSubNav/SeaSubNav'
import { SeaDropdownFilter } from '@src/components/_molecules/SeaFilterRow/filters/SeaDropdownFilter'
import { useFocusEffect } from '@react-navigation/native'

interface CorrectiveActionsProps {
  headerSubNavigation?: SubNav[]
}

export const CorrectiveActions = ({ headerSubNavigation }: CorrectiveActionsProps) => {
  const correctiveActions = sharedState.correctiveActions.use()
  const user = sharedState.user.use()
  const vesselId = sharedState.vesselId.use()
  const vessels = sharedState.vessels.use()
  const divisions = sharedState.divisions.use()
  sharedState.contacts.use() // Prepare for modals to access
  sharedState.vesselLocations.use() // Prepare for modals to access
  sharedState.safetyCheckItems.use() // Prepare for modals to access
  sharedState.spareParts.use() // Prepare for modals to access

  // Hooks
  const router = useRouter()
  const [addDrawerVisible, setAddDrawerVisible] = useState(false)
  const [isOpenSettingsDrawer, setIsOpenSettingsDrawer] = useState(false)

  /** TODO: History View */
  const [historyModalVisible, setHistoryModalVisible] = useState(false)
  const [filterVesselIds, setFilterVesselIds] = useState<string[]>(user?.vesselIds ?? [])

  // Filter States
  const [isSearchFilterActive, setIsSearchFilterActive] = useState(false)

  // Search
  const { searchValue, debouncedSearchValue, setSearchValue } = useDebouncedSearch(300)

  useFocusEffect(
    useCallback(() => {
      user?.vesselIds && setFilterVesselIds(user?.vesselIds)
    }, [user?.vesselIds])
  )

  const allItems = useMemo(() => {
    let allCorrectiveActions: CorrectiveAction[] = []

    // Filter by selected Vessels
    if (filterVesselIds) {
      const hasCorrectiveAction = {} as { [id: string]: true }
      filterVesselIds.forEach(vesselId => {
        correctiveActions?.byVesselId[vesselId]?.forEach(correctiveAction => {
          // Only get the active corrective action
          if (correctiveAction.state === 'active') {
            if (!hasCorrectiveAction[correctiveAction.id]) {
              hasCorrectiveAction[correctiveAction.id] = true
              allCorrectiveActions.push(correctiveAction)
            }
          }
        })
      })
      if (filterVesselIds.length > 1) {
        allCorrectiveActions.sort((a, b) => {
          return a.title.localeCompare(b.title)
        })
      }
    } else {
      // Only get the active corrective actions
      allCorrectiveActions = correctiveActions?.array.active ?? []
    }

    return allCorrectiveActions
  }, [correctiveActions, filterVesselIds])

  // Build filtered list using the new filtering system
  const { data: filteredCorrectiveActions, filterText } = useFilteredList({
    data: allItems,
    filters: [
      searchFilter<CorrectiveAction>(isSearchFilterActive, debouncedSearchValue, action => [
        action.title ?? '',
        action.description ?? '',
        action.correctiveActionNum ?? '',
        action.tags?.join(' ') ?? '',
      ]),
    ],
  })

  // Combined search handler
  const handleSearchFilterChange = (value: string) => {
    setSearchValue(value)

    // Set search as active filter if there's text, otherwise deactivate
    if (value.trim()) {
      setIsSearchFilterActive(true)
    } else {
      setIsSearchFilterActive(false)
    }
  }

  // Primary filters array
  const primaryFilters = [
    <VesselFilterDropdown key="vessel-filter" vesselIds={filterVesselIds} setVesselIds={setFilterVesselIds} />,
  ]

  const buildRows = useCallback(
    (items: CorrectiveAction[]) => {
      return items.map(item => {
        return {
          data: item,
          onPress: (item: CorrectiveAction) => {
            return router.navigate({
              pathname: getRoutePath(Routes.CORRECTIVE_ACTION_VIEW),
              params: {
                correctiveActionId: item.id,
              },
            })
          },
        }
      })
    },
    [router]
  )

  const buildColumns = useCallback(() => {
    return [
      {
        label: '',
        width: 60,
        render: item => <SeaTableImage files={item.files} />,
        compactModeOptions: {
          isThumbnail: true,
        },
      },
      {
        label: 'Corrective Action',
        value: item => formatValue(item.title),
        compactModeOptions: {
          rowPosition: CompactRowPosition.Title,
        },
      },
      {
        label: 'Action #',
        value: item => formatValue(item.correctiveActionNum),
        compactModeOptions: {
          label: {
            show: true,
            valueWidth: CompactValueWidth.Medium,
          },
        },
      },
      {
        label: 'Assigned To',
        icon: () => <SeaTableIconPerson />,
        value: item => renderFullNameForUserId(item.assignedTo),
        compactModeOptions: {
          label: {
            show: true,
            valueWidth: CompactValueWidth.Medium,
            name: 'Assigned',
          },
        },
      },
      {
        label: 'Tags',
        value: item => item.tags?.join(', '),
        compactModeOptions: {
          hideRow: true,
        },
      },
      {
        label: 'Date Added',
        icon: () => <SeaTableIconCalendar />,
        value: item => formatDateShort(item.whenAdded),
        compactModeOptions: {
          hideRow: true,
        },
      },
      {
        label: 'Due Date',
        icon: () => <SeaTableIconCalendar />,
        value: item => (item.dateDue ? formatDateShort(item.dateDue) : '-'),
        compactModeOptions: {
          label: {
            show: true,
            valueWidth: CompactValueWidth.Medium,
          },
        },
      },
      {
        label: 'Vessels / Facilities',
        icon: () => <SeaTableIconVessel />,
        value: item => renderVesselsList(item.vesselIds, vessels, divisions),
        compactModeOptions: {},
      },
    ] as SeaTableColumn<CorrectiveAction>[]
  }, [divisions, vessels])

  const newCorrectiveActionData: CorrectiveAction = {
    title: '',
    description: '',
    dateDue: makeDateTime('').toISODate(),
    emailReminder: '',
    assignedTo: '',
    vesselIds: vesselId ? [vesselId] : [],
  }

  return (
    <RequirePermissions
      role="correctiveActions"
      level={permissionLevels.VIEW}
      showDenial={true}
      licenseePermission="hasCorrectiveActions">
      <ScrollablePageLayout>
        <SeaPageCard
          titleComponent={<SeaPageCardTitle title={'Corrective Actions List'} />}
          primaryActionButton={
            <RequirePermissions key="correctiveActions" role="correctiveActions" level={permissionLevels.CREATE}>
              <SeaAddButton
                label={'Add New'}
                variant={SeaButtonVariant.Primary}
                onPress={() => setAddDrawerVisible(true)}
              />
            </RequirePermissions>
          }
          secondaryActionButton={[
            <RequirePermissions key="correctiveActions" role="correctiveActions" level={permissionLevels.COMPLETE}>
              <SeaCheckButton
                label={'History'}
                variant={SeaButtonVariant.Secondary}
                onPress={() => setHistoryModalVisible(true)}
              />
            </RequirePermissions>,
            <RequirePermissions key="correctiveActionsSettings" role="correctiveActions" level={permissionLevels.EDIT}>
              <SeaSettingsButton key={'settings'} onPress={() => setIsOpenSettingsDrawer(true)} />
            </RequirePermissions>,
          ]}
          subNav={headerSubNavigation}
        />

        {/* Filter Row */}
        <SeaFilterRow
          searchValue={searchValue}
          onSearchValueChange={handleSearchFilterChange}
          primaryFilters={primaryFilters}
          secondaryFilters={[]}
          filterText={filterText}
        />

        {/* Table View */}
        <View style={styles.tableView}>
          <SeaTable columns={buildColumns()} rows={buildRows(filteredCorrectiveActions)} />
        </View>
      </ScrollablePageLayout>
      {addDrawerVisible && (
        <EditCorrectiveActionDrawer
          correctiveAction={newCorrectiveActionData}
          visible={addDrawerVisible}
          onClose={() => setAddDrawerVisible(false)}
          mode={DrawerMode.Create}
        />
      )}

      {isOpenSettingsDrawer && (
        <CorrectiveActionSettingsDrawer
          title="Corrective Action List Settings"
          visible={isOpenSettingsDrawer}
          onClose={() => setIsOpenSettingsDrawer(false)}
        />
      )}
    </RequirePermissions>
  )
}

const styles = StyleSheet.create({
  tableView: {
    marginTop: 16,
  },
})
