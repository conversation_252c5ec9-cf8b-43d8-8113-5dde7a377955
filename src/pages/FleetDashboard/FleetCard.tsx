import {
  SeaCard,
  SeaCardBody,
  SeaCardContent,
  SeaCardHeader,
  SeaCardThumbnail,
} from '@src/components/_atoms/SeaCard/SeaCard'
import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { SeaStatusBattery } from '@src/components/_atoms/SeaStatusBattery/SeaStatusBattery'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { Vessel } from '@src/shared-state/Core/vessel'
import { colors } from '@src/theme/globalStyle'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import React, { useCallback, useMemo, useState } from 'react'
import { Linking, TouchableOpacity, View } from 'react-native'
import { calculateStatusPercent } from '@src/components/_organisms/VesselDashboard/Metrics'
import { buildStatuses, emptyStatusBattery } from '@src/lib/statusBattery'
import { useFleetStatsMapToStatusBattery } from '@src/hooks/stats/useFleetStatsMapToStatusBattery'
import { VesselCollectionsUsedForStats } from '@src/shared-state/General/fleetStats'
import { useFocusEffect } from '@react-navigation/native'

interface FleetCardProps {
  vessel: Vessel
  containerWidth: number
  onPress?: () => void
}
export const FleetCard: React.FC<FleetCardProps> = ({ vessel, containerWidth, onPress }) => {
  const { styles } = useStyles(styleSheet)
  const { isTabletWidth, isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()

  const [vesselId, setVesselId] = useState<string>()
  useFocusEffect(
    useCallback(() => {
      if (vessel.id) {
        setVesselId(vessel.id)
      }
    }, [vessel])
  )

  const vesselStats = useFleetStatsMapToStatusBattery(
    [vesselId ?? ''],
    [
      VesselCollectionsUsedForStats.SAFETY_CHECK_ITEMS,
      VesselCollectionsUsedForStats.SAFETY_EQUIPMENT_ITEMS,
      VesselCollectionsUsedForStats.DRILLS,
      VesselCollectionsUsedForStats.JOBS,
      VesselCollectionsUsedForStats.SCHEDULED_MAINTENANCE_TASKS,
      VesselCollectionsUsedForStats.SPARE_PARTS,
      VesselCollectionsUsedForStats.INCIDENTS,
      VesselCollectionsUsedForStats.RISK_ASSESSMENTS,
      VesselCollectionsUsedForStats.DANGEROUS_GOODS,
      VesselCollectionsUsedForStats.VESSEL_CERTIFICATES,
      VesselCollectionsUsedForStats.VESSEL_DOCUMENTS,
    ]
  )

  const statuses = useMemo(() => {
    return buildStatuses(vesselStats ?? emptyStatusBattery)
  }, [vesselStats])

  const vesselStatusPercent = useMemo(() => {
    return calculateStatusPercent([vesselStats])
  }, [vesselStats])

  const numberOfColumns = useMemo(() => {
    if (isLargeDesktopWidth) return 3
    if (isTabletWidth || isDesktopWidth) return 2
    return 1
  }, [isTabletWidth, isDesktopWidth, isLargeDesktopWidth])

  return (
    <TouchableOpacity
      key={vessel.id}
      style={{
        width: containerWidth / numberOfColumns - 20,
        minWidth: 120,
        maxWidth: 500,
      }}
      onPress={onPress}>
      <SeaCard>
        <SeaCardThumbnail files={vessel.images} onPress={onPress}>
          <TouchableOpacity
            onPress={() => Linking.openURL(vessel.marineTrafficLink!)}
            style={styles.healthStatusMarker}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                gap: 5,
              }}>
              <SeaIcon icon="planner_review" color="grey" fill={false} />
              <SeaTypography variant={'label'} color={'grey'} containerStyle={{ marginBottom: 0 }}>
                {vesselStatusPercent}
              </SeaTypography>
            </View>
          </TouchableOpacity>

          {vessel.marineTrafficLink && (
            <TouchableOpacity onPress={() => Linking.openURL(vessel.marineTrafficLink!)} style={styles.mapMarker}>
              <SeaIcon icon="location_on" color="grey" fill={false} />
            </TouchableOpacity>
          )}
        </SeaCardThumbnail>
        <SeaCardBody>
          <SeaCardHeader
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              gap: 10,
            }}>
            <SeaTypography
              variant="cardTitle"
              containerStyle={{
                flex: 1,
              }}
              numberOfLines={1}
              ellipsizeMode="tail">
              {vessel.name}
            </SeaTypography>

            {/* TODO: Need to add the backend logic for this */}
            {/* <SeaStack gap={8} direction="row" align="center">
              {!vessel.isShoreFacility && (
                <SeaStack
                  gap={4}
                  direction="row"
                  align="center"
                  style={styles.badge}
                >
                  <SeaIcon
                    icon="location_on"
                    fill={false}
                    size={12}
                    color={theme.colors.grey}
                  />
                  <SeaTypography variant="body">At Sea</SeaTypography>
                </SeaStack>
              )}
              <SeaStack>
                <SeaIcon
                  icon="person"
                  fill={false}
                  size={20}
                  color={theme.colors.grey}
                />
                <SeaTypography variant="body">3</SeaTypography>
              </SeaStack>
            </SeaStack> */}
          </SeaCardHeader>
          <SeaCardContent>
            <SeaStatusBattery statuses={statuses} />
          </SeaCardContent>
        </SeaCardBody>
      </SeaCard>
    </TouchableOpacity>
  )
}

const styleSheet = createStyleSheet(theme => ({
  mapMarker: {
    position: 'absolute',
    top: 8,
    right: 8,
    borderRadius: 30,
    width: 30,
    height: 30,
    backgroundColor: theme.colors.white,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },

  healthStatusMarker: {
    position: 'absolute',
    top: 8,
    left: 8,
    borderRadius: 30,
    height: 30,
    paddingHorizontal: 10,
    backgroundColor: theme.colors.white,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'flex-start',
  },

  badge: {
    backgroundColor: colors.white,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: theme.colors.borderColor,
  },
}))
