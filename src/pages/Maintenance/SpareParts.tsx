import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
} from '@src/components/_atoms/SeaTable/SeaTable'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { renderCategoryName } from '@src/lib/categories'
import { formatValue, getCurrencyFromRegion } from '@src/lib/util'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { sharedState } from '@src/shared-state/shared-state'
import { SparePart } from '@src/shared-state/VesselMaintenance/spareParts'
import React, { useCallback, useMemo, useRef, useState } from 'react'
import { StyleSheet, Text, View } from 'react-native'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { SeaPageCard, SeaPageCardTitle } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { usePermission } from '@src/hooks/usePermission'
import { useRouter } from 'expo-router'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { EditSparePartDrawer } from '@src/components/_organisms/Maintenance/SparePart/EditSparePartDrawer'
import { ScrollablePageLayout } from '@src/layout/ScrollablePageLayout/ScrollablePageLayout'
import { SeaTableImage } from '@src/components/_atoms/SeaTable/SeaTableImage'
import { SeaTableIconFlag, SeaTableIconLocation } from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'
import { DrawerMode } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { SubNav } from '@src/components/_molecules/SeaSubNav/SeaSubNav'
import { MaintenanceSettingsDrawer } from '@src/components/_organisms/Maintenance/MaintenanceSettingsDrawer'
import { SeaSettingsButton } from '@src/components/_molecules/IconButtons/SeaSettingsButton'
import { SeaFilterRow } from '@src/components/_molecules/SeaFilterRow/SeaFilterRow'
import { useFilteredList } from '@src/hooks/filters/useFilteredList'
import { searchFilter } from '@src/hooks/filters/searchFilter'
import { genericFilter } from '@src/hooks/filters/genericFilter'
import { useDebouncedSearch } from '@src/hooks/useDebouncedSearch'
import { SeaToggleFilter } from '@src/components/_molecules/SeaFilterRow/filters/SeaToggleFilter'
import { SeaDropdownFilter } from '@src/components/_molecules/SeaFilterRow/filters/SeaDropdownFilter'
import { SeaCriticalFilterButton } from '@src/components/_molecules/SeaFilterRow/filters/SeaCriticalFilterButton'
import Animated, {
  interpolate,
  useAnimatedScrollHandler,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated'
import { Extrapolation } from 'react-native-reanimated/src/interpolation'

const SparePartsPermissions = {
  sparePartsList: { level: permissionLevels.CREATE },
}

interface SparePartsProps {
  headerSubNavigation: SubNav[]
}

export const SpareParts = ({ headerSubNavigation }: SparePartsProps) => {
  const vessel = sharedState.vessel.use()
  const spareParts = sharedState.spareParts.use()
  const equipment = sharedState.equipment.use()
  const vesselLocations = sharedState.vesselLocations.use()
  const licenseeSettings = sharedState.licenseeSettings.use()
  const vesselSystems = sharedState.vesselSystems.use()

  const router = useRouter()

  const [isVisibleAddDrawer, setIsVisibleAddDrawer] = useState(false)
  const [isOpenSettingsDrawer, setIsOpenSettingsDrawer] = useState(false)

  // Filter States
  const [isAllFilterActive, setIsAllFilterActive] = useState(true)
  const [isOutOfStockFilterActive, setIsOutOfStockFilterActive] = useState(false)
  const [isLowStockFilterActive, setIsLowStockFilterActive] = useState(false)
  const [isCriticalFilterActive, setIsCriticalFilterActive] = useState(false)
  const [isSearchFilterActive, setIsSearchFilterActive] = useState(false)
  const [isSystemFilterActive, setIsSystemFilterActive] = useState(false)
  const [isEquipmentFilterActive, setIsEquipmentFilterActive] = useState(false)
  const [isLocationFilterActive, setIsLocationFilterActive] = useState(false)

  // Filter Values
  const [systemFilterValue, setSystemFilterValue] = useState('')
  const [equipmentFilterValue, setEquipmentFilterValue] = useState('')
  const [locationFilterValue, setLocationFilterValue] = useState('')

  // Search
  const { searchValue, debouncedSearchValue, setSearchValue } = useDebouncedSearch(300)

  // Base data for filtering
  const allItems = useMemo(() => spareParts?.all ?? [], [spareParts?.all])

  const modulePermissions = usePermission<typeof SparePartsPermissions>({
    modules: SparePartsPermissions,
  })

  const handleRow = useCallback(
    (item: SparePart) => {
      router.navigate({
        pathname: getRoutePath(Routes.SPARE_PARTS_LIST_VIEW),
        params: {
          vesselId: vessel?.id,
          sparePartId: item.id,
        },
      })
    },
    [router, vessel?.id]
  )

  const checkContainsCritical = useCallback(
    (equipmentIds: string[] | undefined) => {
      if (!equipmentIds) return false

      return equipmentIds?.some(id => equipment?.byId[id]?.isCritical) ?? false
    },
    [equipment?.byId]
  )

  const seaTableColumns = useMemo(() => {
    if (!equipment || !licenseeSettings || !vesselLocations) return []

    return [
      {
        label: '',
        width: 60,
        render: sparePart => <SeaTableImage files={sparePart.files} />,
        compactModeOptions: {
          isThumbnail: true,
        },
      },
      {
        label: 'Item',
        value: sparePart => sparePart.item,
        style: { fontWeight: 'bold' },
        compactModeOptions: {
          rowPosition: CompactRowPosition.Title,
        },
      },
      {
        label: 'In Stock',
        value: sparePart => {
          return formatValue(sparePart.quantity)
        },
        compactModeOptions: {
          label: {
            show: true,
            valueWidth: CompactValueWidth.Large,
          },
        },
      },
      ...(isLowStockFilterActive
        ? [
            {
              label: 'Minimum Stock',
              value: (sparePart: SparePart) => {
                return formatValue(sparePart.minQuantity)
              },
              compactModeOptions: {
                hideRow: true,
              },
            },
          ]
        : []),
      {
        label: 'Equipment',
        value: sparePart => formatValue(sparePart.equipmentList),
        compactModeOptions: {
          hideRow: true,
        },
      },
      {
        label: 'Location',
        icon: () => <SeaTableIconLocation />,
        value: sparePart => formatValue(renderCategoryName(sparePart.locationId, vesselLocations)),
        compactModeOptions: {
          label: {
            show: true,
            valueWidth: CompactValueWidth.Large,
          },
        },
      },
      {
        label: 'Part #',
        value: sparePart => formatValue(sparePart.partNum),
        compactModeOptions: {
          hideRow: true,
        },
      },
      {
        label: 'Unit Price',
        value: sparePart => getCurrencyFromRegion(licenseeSettings?.region) + ' ' + formatValue(sparePart.unitPrice),
        compactModeOptions: {
          hideRow: true,
        },
      },
      {
        label: 'Critical',
        render: sparePart => {
          const isCritical = checkContainsCritical(sparePart.equipmentIds)
          return isCritical ? <SeaTableIconFlag /> : <></>
        },
        compactModeOptions: {
          rowPosition: CompactRowPosition.BottomRightCorner,
        },
      },
    ] as SeaTableColumn<SparePart>[]
  }, [equipment, checkContainsCritical, licenseeSettings, vesselLocations, isLowStockFilterActive])

  // Filter options
  const systemFilterOptions = useMemo(() => {
    if (!spareParts?.filterOptions) return []

    return spareParts.filterOptions.systemIds.map((id: string) => ({
      label: renderCategoryName(id, vesselSystems),
      value: id,
    }))
  }, [spareParts?.filterOptions, vesselSystems])

  const equipmentFilterOptions = useMemo(() => {
    if (!spareParts?.filterOptions) return []

    const options = spareParts.filterOptions.equipmentIds.map((id: string) => ({
      label: equipment?.byId[id]?.equipment ?? '',
      value: id,
    }))

    return options.filter(option => {
      if (systemFilterValue) {
        const _equipment = equipment?.byId[option.value]
        return _equipment?.systemId === systemFilterValue
      }
      return true
    })
  }, [equipment?.byId, spareParts?.filterOptions, systemFilterValue])

  const locationFilterOptions = useMemo(() => {
    if (!spareParts?.filterOptions) return []
    return spareParts.filterOptions.locationIds.map((id: string) => ({
      label: renderCategoryName(id, vesselLocations),
      value: id,
    }))
  }, [spareParts?.filterOptions, vesselLocations])

  // Count data for toggle filters
  const outOfStockCount = useMemo(() => {
    return allItems.filter(item => item.quantity === 0).length
  }, [allItems])

  const lowStockCount = useMemo(() => {
    return allItems.filter(item => item.quantity && item.minQuantity && item.quantity < item.minQuantity).length
  }, [allItems])

  // Build filtered list using the new filtering system
  const { data: filteredData, filterText } = useFilteredList({
    data: allItems,
    filters: [
      genericFilter<SparePart>(isAllFilterActive, () => true, ``),
      searchFilter<SparePart>(isSearchFilterActive, debouncedSearchValue, item => [
        item.item ?? '', // Item name
        item.partNum ?? '', // Part number
        item.equipmentList ?? '', // Equipment list
        renderCategoryName(item.locationId, vesselLocations), // Location
      ]),
      genericFilter<SparePart>(isOutOfStockFilterActive, item => item.quantity === 0, `Out of Stock`),
      genericFilter<SparePart>(
        isLowStockFilterActive,
        item => (item.quantity && item.minQuantity ? item.quantity < item.minQuantity : false),
        `Low Stock`
      ),
      genericFilter<SparePart>(isCriticalFilterActive, item => checkContainsCritical(item.equipmentIds), `Critical`),
      genericFilter<SparePart>(
        isSystemFilterActive,
        item => item.systemId === systemFilterValue,
        `System: ${renderCategoryName(systemFilterValue, vesselSystems)}`
      ),
      genericFilter<SparePart>(
        isEquipmentFilterActive,
        item => item.equipmentIds?.includes(equipmentFilterValue) ?? false,
        `Equipment: ${equipment?.byId[equipmentFilterValue]?.equipment ?? ''}`
      ),
      genericFilter<SparePart>(
        isLocationFilterActive,
        item => item.locationId === locationFilterValue,
        `Location: ${renderCategoryName(locationFilterValue, vesselLocations)}`
      ),
    ],
  })

  // Complex filter change handlers
  const handleAllFilterChange = useCallback(
    (enabled: boolean) => {
      // Prevent deselecting the 'All' filter if it's already enabled
      if (!enabled && isAllFilterActive) {
        return
      }

      setIsAllFilterActive(enabled)

      // Reset ALL other filters when 'All' is enabled
      if (enabled) {
        setSearchValue('')
        setIsSearchFilterActive(false)
        setIsOutOfStockFilterActive(false)
        setIsLowStockFilterActive(false)
        setIsCriticalFilterActive(false)
        setSystemFilterValue('')
        setEquipmentFilterValue('')
        setLocationFilterValue('')
        setIsSystemFilterActive(false)
        setIsEquipmentFilterActive(false)
        setIsLocationFilterActive(false)
      }
    },
    [setSearchValue, isAllFilterActive]
  )

  const handleOutOfStockFilterChange = useCallback(
    (enabled: boolean) => {
      setIsOutOfStockFilterActive(enabled)

      // Disable All filter when Out of Stock is enabled
      if (enabled) {
        setIsAllFilterActive(false)
        setIsLowStockFilterActive(false)
        // Disable search when toggle is enabled
        setSearchValue('')
        setIsSearchFilterActive(false)
      } else if (
        !isLowStockFilterActive &&
        !isCriticalFilterActive &&
        !isSystemFilterActive &&
        !isEquipmentFilterActive &&
        !isLocationFilterActive
      ) {
        // Re-enable All if no other filters are active
        setIsAllFilterActive(true)
      }
    },
    [
      isLowStockFilterActive,
      isCriticalFilterActive,
      isSystemFilterActive,
      isEquipmentFilterActive,
      isLocationFilterActive,
      setSearchValue,
    ]
  )

  const handleLowStockFilterChange = useCallback(
    (enabled: boolean) => {
      setIsLowStockFilterActive(enabled)

      // Disable All filter when Low Stock is enabled
      if (enabled) {
        setIsAllFilterActive(false)
        setIsOutOfStockFilterActive(false)
        // Disable search when toggle is enabled
        setSearchValue('')
        setIsSearchFilterActive(false)
      } else if (
        !isOutOfStockFilterActive &&
        !isCriticalFilterActive &&
        !isSystemFilterActive &&
        !isEquipmentFilterActive &&
        !isLocationFilterActive
      ) {
        // Re-enable All if no other filters are active
        setIsAllFilterActive(true)
      }
    },
    [
      isOutOfStockFilterActive,
      isCriticalFilterActive,
      isSystemFilterActive,
      isEquipmentFilterActive,
      isLocationFilterActive,
      setSearchValue,
    ]
  )

  const handleSearchFilterChange = (value: string) => {
    setSearchValue(value)
    setIsSearchFilterActive(value.trim().length > 0)

    if (value.trim()) {
      // Search is mutually exclusive - disable all other filters
      setIsAllFilterActive(false)
      setIsOutOfStockFilterActive(false)
      setIsLowStockFilterActive(false)
      setIsCriticalFilterActive(false)
      setSystemFilterValue('')
      setEquipmentFilterValue('')
      setLocationFilterValue('')
      setIsSystemFilterActive(false)
      setIsEquipmentFilterActive(false)
      setIsLocationFilterActive(false)
    } else {
      // Re-enable All filter when search is cleared
      setIsAllFilterActive(true)
    }
  }

  const handleSystemFilterChange = useCallback(
    (value: string) => {
      setSystemFilterValue(value)
      setIsSystemFilterActive(value !== '')

      // Disable All filter when dropdown is selected
      if (value !== '') {
        setIsAllFilterActive(false)
        // Disable search when dropdown is selected
        setSearchValue('')
        setIsSearchFilterActive(false)
      } else if (
        !isOutOfStockFilterActive &&
        !isLowStockFilterActive &&
        !isCriticalFilterActive &&
        !isEquipmentFilterActive &&
        !isLocationFilterActive
      ) {
        // Re-enable All if all filters are cleared
        setIsAllFilterActive(true)
      }
    },
    [
      isOutOfStockFilterActive,
      isLowStockFilterActive,
      isCriticalFilterActive,
      isEquipmentFilterActive,
      isLocationFilterActive,
      setSearchValue,
    ]
  )

  const handleEquipmentFilterChange = useCallback(
    (value: string) => {
      setEquipmentFilterValue(value)
      setIsEquipmentFilterActive(value !== '')

      // Disable All filter when dropdown is selected
      if (value !== '') {
        setIsAllFilterActive(false)
        // Disable search when dropdown is selected
        setSearchValue('')
        setIsSearchFilterActive(false)
      } else if (
        !isOutOfStockFilterActive &&
        !isLowStockFilterActive &&
        !isCriticalFilterActive &&
        !isSystemFilterActive &&
        !isLocationFilterActive
      ) {
        // Re-enable All if all filters are cleared
        setIsAllFilterActive(true)
      }
    },
    [
      isOutOfStockFilterActive,
      isLowStockFilterActive,
      isCriticalFilterActive,
      isSystemFilterActive,
      isLocationFilterActive,
      setSearchValue,
    ]
  )

  const handleLocationFilterChange = useCallback(
    (value: string) => {
      setLocationFilterValue(value)
      setIsLocationFilterActive(value !== '')

      // Disable All filter when dropdown is selected
      if (value !== '') {
        setIsAllFilterActive(false)
        // Disable search when dropdown is selected
        setSearchValue('')
        setIsSearchFilterActive(false)
      } else if (
        !isOutOfStockFilterActive &&
        !isLowStockFilterActive &&
        !isCriticalFilterActive &&
        !isSystemFilterActive &&
        !isEquipmentFilterActive
      ) {
        // Re-enable All if all filters are cleared
        setIsAllFilterActive(true)
      }
    },
    [
      isOutOfStockFilterActive,
      isLowStockFilterActive,
      isCriticalFilterActive,
      isSystemFilterActive,
      isEquipmentFilterActive,
      setSearchValue,
    ]
  )

  // Filter arrays
  const primaryFilters = useMemo(
    () => [
      <SeaToggleFilter
        key={'all'}
        label={'All'}
        count={allItems.length}
        value={isAllFilterActive}
        onChangeValue={handleAllFilterChange}
        isActive={isAllFilterActive}
      />,
      <SeaToggleFilter
        key={'outOfStock'}
        label={'Out of Stock'}
        count={outOfStockCount}
        value={isOutOfStockFilterActive}
        onChangeValue={handleOutOfStockFilterChange}
        isActive={isOutOfStockFilterActive}
      />,
      <SeaToggleFilter
        key={'lowStock'}
        label={'Low Stock'}
        count={lowStockCount}
        value={isLowStockFilterActive}
        onChangeValue={handleLowStockFilterChange}
        isActive={isLowStockFilterActive}
      />,
    ],
    [
      allItems.length,
      isAllFilterActive,
      handleAllFilterChange,
      outOfStockCount,
      isOutOfStockFilterActive,
      handleOutOfStockFilterChange,
      lowStockCount,
      isLowStockFilterActive,
      handleLowStockFilterChange,
    ]
  )

  const secondaryFilters = useMemo(
    () => [
      <SeaCriticalFilterButton
        key={'critical'}
        isActive={isCriticalFilterActive}
        value={isCriticalFilterActive}
        onChangeValue={setIsCriticalFilterActive}
      />,
      <SeaDropdownFilter
        key={'system'}
        label={'System:'}
        value={systemFilterValue}
        onChangeValue={handleSystemFilterChange}
        options={systemFilterOptions}
        isActive={isSystemFilterActive}
      />,
      <SeaDropdownFilter
        key={'equipment'}
        label={'Equipment:'}
        value={equipmentFilterValue}
        onChangeValue={handleEquipmentFilterChange}
        options={equipmentFilterOptions}
        isActive={isEquipmentFilterActive}
      />,
      <SeaDropdownFilter
        key={'location'}
        label={'Location:'}
        value={locationFilterValue}
        onChangeValue={handleLocationFilterChange}
        options={locationFilterOptions}
        isActive={isLocationFilterActive}
      />,
    ],
    [
      isCriticalFilterActive,
      systemFilterValue,
      handleSystemFilterChange,
      systemFilterOptions,
      isSystemFilterActive,
      equipmentFilterValue,
      handleEquipmentFilterChange,
      equipmentFilterOptions,
      isEquipmentFilterActive,
      locationFilterValue,
      handleLocationFilterChange,
      locationFilterOptions,
      isLocationFilterActive,
    ]
  )

  const rows = useMemo(
    () => buildRows(filteredData ?? [], (item: SparePart) => handleRow(item)),
    [filteredData, handleRow]
  )

  // React-native - scroll - old school
  // const scrollY = useRef(new Animated.Value(0)).current

  // Reanimated
  const MAX_HEADER_HEIGHT = 50
  const MIN_HEADER_HEIGHT = 40
  const COLLAPSE_RANGE = MAX_HEADER_HEIGHT - MIN_HEADER_HEIGHT // 60

  // const scrollY = useSharedValue(0)
  const scrollDrivenOffset = useSharedValue(0) // 0..COLLAPSE_RANGE (how much header is hidden)

  // progress: 0 (expanded) -> 1 (collapsed)
  const headerStyle = useAnimatedStyle(() => {
    const progress = scrollDrivenOffset.value / COLLAPSE_RANGE
    return {
      transform: [{ scale: interpolate(progress, [0, 1], [1, 0.9], Extrapolation.CLAMP) }],
      opacity: interpolate(progress, [0, 1], [1, 0.3], Extrapolation.CLAMP),
      padding: interpolate(progress, [0, 10], [100, 0], Extrapolation.CLAMP),
    }
  })

  return (
    <>
      <Animated.View style={[{ backgroundColor: 'skyblue' }, headerStyle]}>
        <Text style={{ fontSize: 20 }}>Shrinking Header</Text>
      </Animated.View>
      {/*<ScrollablePageLayout>*/}
      <RequirePermissions role="sparePartsList" level={permissionLevels.VIEW} showDenial={true}>
        <View style={styles.container}>
          <SeaPageCard
            scrollDrivenOffset={scrollDrivenOffset}
            titleComponent={
              <SeaPageCardTitle title={vessel?.isShoreFacility ? 'Inventory List' : 'Spare Parts List'} />
            }
            primaryActionButton={
              modulePermissions.sparePartsList ? (
                <SeaButton
                  onPress={() => setIsVisibleAddDrawer(true)}
                  variant={SeaButtonVariant.Primary}
                  label={'Add New'}
                  iconOptions={{ icon: 'add' }}
                />
              ) : undefined
            }
            secondaryActionButton={[
              <SeaDownloadButton key={'download'} onPress={() => alert('This functionality is not completed yet')} />,
              <SeaSettingsButton key={'settings'} onPress={() => setIsOpenSettingsDrawer(true)} />,
            ]}
            subNav={headerSubNavigation}
          />

          <SeaFilterRow
            searchValue={searchValue}
            onSearchValueChange={handleSearchFilterChange}
            primaryFilters={primaryFilters}
            secondaryFilters={secondaryFilters}
            filterText={filterText}
          />

          {/* Table View */}
          <View style={styles.tableView}>
            <SeaTable
              columns={seaTableColumns}
              showGroupedTable={false}
              rows={rows}
              scrollDrivenOffset={scrollDrivenOffset}
              // scrollY={scrollY}
              // onScrollHandler={onScrollHandler}
            />
          </View>
        </View>
      </RequirePermissions>
      {/*</ScrollablePageLayout>*/}
      {isVisibleAddDrawer && (
        <EditSparePartDrawer
          onClose={() => setIsVisibleAddDrawer(false)}
          visible={isVisibleAddDrawer}
          mode={DrawerMode.Create}
        />
      )}
      {isOpenSettingsDrawer && (
        <MaintenanceSettingsDrawer
          title="Spare Parts List Settings"
          visible={isOpenSettingsDrawer}
          onClose={() => setIsOpenSettingsDrawer(false)}
        />
      )}
    </>
  )
}

const buildRows = (items: SparePart[], onPress: (item: SparePart) => void) => {
  return items.map(item => ({
    data: item,
    // status: getStatus(item),
    onPress: (item: SparePart) => onPress(item),
  }))
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    height: '100%',
    // backgroundColor: 'red',
  },
  tableView: {
    flex: 1,
    height: '100%',
    width: '100%',
    marginTop: 16,
  },
})
