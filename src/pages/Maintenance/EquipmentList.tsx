import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
} from '@src/components/_atoms/SeaTable/SeaTable'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { CategoriesData, renderCategoryName } from '@src/lib/categories'
import { formatValue } from '@src/lib/util'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { sharedState } from '@src/shared-state/shared-state'
import { Equipment } from '@src/shared-state/VesselMaintenance/equipment'
import React, { useCallback, useMemo, useState } from 'react'
import { StyleSheet, View } from 'react-native'
import { SeaSettingsButton } from '@src/components/_molecules/IconButtons/SeaSettingsButton'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { SeaPageCard, SeaPageCardTitle } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { usePermission } from '@src/hooks/usePermission'
import { useRouter } from 'expo-router'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { EditEquipmentListDrawer } from '@src/components/_organisms/Maintenance/EquipmentList/EditEquipmentListDrawer'
import { ScrollablePageLayout } from '@src/layout/ScrollablePageLayout/ScrollablePageLayout'
import { SeaTableImage } from '@src/components/_atoms/SeaTable/SeaTableImage'
import { SeaTableIconFlag, SeaTableIconLocation } from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'
import { DrawerMode } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { MaintenanceSettingsDrawer } from '@src/components/_organisms/Maintenance/MaintenanceSettingsDrawer'
import { SubNav } from '@src/components/_molecules/SeaSubNav/SeaSubNav'
import { SeaFilterRow } from '@src/components/_molecules/SeaFilterRow/SeaFilterRow'
import { useFilteredList } from '@src/hooks/filters/useFilteredList'
import { searchFilter } from '@src/hooks/filters/searchFilter'
import { criticalFilter } from '@src/hooks/filters/criticalFilter'
import { genericFilter } from '@src/hooks/filters/genericFilter'
import { useDebouncedSearch } from '@src/hooks/useDebouncedSearch'
import { SeaCriticalFilterButton } from '@src/components/_molecules/SeaFilterRow/filters/SeaCriticalFilterButton'
import { SeaToggleFilter } from '@src/components/_molecules/SeaFilterRow/filters/SeaToggleFilter'
import { SeaDropdownFilter } from '@src/components/_molecules/SeaFilterRow/filters/SeaDropdownFilter'

const EquipmentListPermissions = {
  maintenanceSchedule: { level: permissionLevels.CREATE },
}

interface EquipmentListProps {
  headerSubNavigation?: SubNav[]
}

export const EquipmentList = ({ headerSubNavigation = undefined }: EquipmentListProps) => {
  const vesselId = sharedState.vesselId.use()

  const vesselLocations = sharedState.vesselLocations.use()
  const equipment = sharedState.equipment.use()
  const vesselSystems = sharedState.vesselSystems.use()

  const router = useRouter()

  const [isVisibleAddDrawer, setIsVisibleAddDrawer] = useState(false)
  const [isOpenSettingsDrawer, setIsOpenSettingsDrawer] = useState(false)

  // Filter States
  const [isAllFilterActive, setIsAllFilterActive] = useState(true)
  const [isSearchFilterActive, setIsSearchFilterActive] = useState(false)
  const [isCriticalFilterActive, setIsCriticalFilterActive] = useState(false)
  const [isSystemFilterActive, setIsSystemFilterActive] = useState(false)
  const [isLocationFilterActive, setIsLocationFilterActive] = useState(false)

  // Filter Values
  const [systemFilterValue, setSystemFilterValue] = useState('')
  const [locationFilterValue, setLocationFilterValue] = useState('')

  // Search
  const { searchValue, debouncedSearchValue, setSearchValue } = useDebouncedSearch(300)

  // Base data for filtering
  const allItems = useMemo(() => equipment?.all ?? [], [equipment?.all])

  const modulePermissions = usePermission<typeof EquipmentListPermissions>({
    modules: EquipmentListPermissions,
  })

  // Filter options
  const systemFilterOptions = useMemo(() => {
    if (!equipment?.filterOptions) return []

    return equipment.filterOptions.systemIds.map((id: string) => ({
      label: renderCategoryName(id, vesselSystems),
      value: id,
    }))
  }, [equipment?.filterOptions, vesselSystems])

  const locationFilterOptions = useMemo(() => {
    if (!equipment?.filterOptions) return []
    return equipment.filterOptions.locationIds.map((id: string) => ({
      label: renderCategoryName(id, vesselLocations),
      value: id,
    }))
  }, [equipment, vesselLocations])

  // Build filtered list using the new filtering system
  const { data: filteredData, filterText } = useFilteredList({
    data: allItems,
    filters: [
      genericFilter<Equipment>(isAllFilterActive, () => true, ``),
      searchFilter<Equipment>(isSearchFilterActive, debouncedSearchValue, item => [
        item.equipment ?? '', // Equipment name
        item.make ?? '', // Make
        item.model ?? '', // Model
        item.serial ?? '', // Serial
        renderCategoryName(item.systemId, vesselSystems), // System
        renderCategoryName(item.locationId, vesselLocations), // Location
      ]),
      criticalFilter<Equipment>(isCriticalFilterActive, item => item.isCritical ?? false),
      genericFilter<Equipment>(
        isSystemFilterActive,
        item => item.systemId === systemFilterValue,
        `System: ${renderCategoryName(systemFilterValue, vesselSystems)}`
      ),
      genericFilter<Equipment>(
        isLocationFilterActive,
        item => item.locationId === locationFilterValue,
        `Location: ${renderCategoryName(locationFilterValue, vesselLocations)}`
      ),
    ],
  })

  // Filter change handlers
  const handleAllFilterChange = useCallback(
    (enabled: boolean) => {
      // Prevent deselecting the 'All' filter if it's already enabled
      if (!enabled && isAllFilterActive) {
        return
      }

      setIsAllFilterActive(enabled)

      // Reset other filters when 'All' is enabled
      if (enabled) {
        setSearchValue('')
        setIsSearchFilterActive(false)
        setSystemFilterValue('')
        setLocationFilterValue('')
        setIsSystemFilterActive(false)
        setIsLocationFilterActive(false)
      }
    },
    [setSearchValue, isAllFilterActive]
  )

  const handleSearchFilterChange = (value: string) => {
    setSearchValue(value)
    setIsSearchFilterActive(value.trim().length > 0)

    if (value.trim()) {
      setIsAllFilterActive(false)
    } else {
      setIsAllFilterActive(true)
    }
  }

  const handleSystemFilterChange = useCallback(
    (value: string) => {
      setSystemFilterValue(value)
      setIsSystemFilterActive(value !== '')

      // Disable 'All' filter when dropdown is selected
      if (value !== '') {
        setIsAllFilterActive(false)
      } else if (locationFilterValue === '') {
        // Re-enable 'All' if both dropdowns are cleared
        setIsAllFilterActive(true)
      }
    },
    [locationFilterValue]
  )

  const handleLocationFilterChange = useCallback(
    (value: string) => {
      setLocationFilterValue(value)
      setIsLocationFilterActive(value !== '')

      // Disable 'All' filter when dropdown is selected
      if (value !== '') {
        setIsAllFilterActive(false)
      } else if (systemFilterValue === '') {
        // Re-enable 'All' if both dropdowns are cleared
        setIsAllFilterActive(true)
      }
    },
    [systemFilterValue]
  )

  const handleRow = useCallback(
    (item: Equipment) => {
      router.navigate({
        pathname: getRoutePath(Routes.EQUIPMENT_LIST_VIEW),
        params: {
          vesselId: vesselId,
          equipmentId: item.id,
        },
      })
    },
    [router, vesselId]
  )

  // Filter arrays
  const primaryFilters = useMemo(
    () => [
      <SeaToggleFilter
        key={'all'}
        label={`All`}
        count={allItems.length}
        value={isAllFilterActive}
        onChangeValue={handleAllFilterChange}
        isActive={isAllFilterActive}
      />,
    ],
    [allItems.length, isAllFilterActive, handleAllFilterChange]
  )

  const secondaryFilters = useMemo(
    () => [
      <SeaCriticalFilterButton
        key={'critical'}
        isActive={isCriticalFilterActive}
        value={isCriticalFilterActive}
        onChangeValue={setIsCriticalFilterActive}
      />,
      <SeaDropdownFilter
        key={'system'}
        label={'System:'}
        value={systemFilterValue}
        onChangeValue={handleSystemFilterChange}
        options={systemFilterOptions}
        isActive={isSystemFilterActive}
      />,
      <SeaDropdownFilter
        key={'location'}
        label={'Location:'}
        value={locationFilterValue}
        onChangeValue={handleLocationFilterChange}
        options={locationFilterOptions}
        isActive={isLocationFilterActive}
      />,
    ],
    [
      isCriticalFilterActive,
      systemFilterValue,
      handleSystemFilterChange,
      systemFilterOptions,
      isSystemFilterActive,
      locationFilterValue,
      handleLocationFilterChange,
      locationFilterOptions,
      isLocationFilterActive,
    ]
  )

  const columns = useMemo(() => buildColumns(vesselLocations), [vesselLocations])
  const rows = useMemo(
    () => buildRows(filteredData ?? [], (item: Equipment) => handleRow(item), vesselSystems),
    [filteredData, handleRow, vesselSystems]
  )

  return (
    <>
      {/*<ScrollablePageLayout>*/}
      <RequirePermissions role="maintenanceSchedule" level={permissionLevels.VIEW} showDenial={true}>
        <SeaPageCard
          titleComponent={<SeaPageCardTitle title={'Equipment List'} />}
          primaryActionButton={
            modulePermissions.maintenanceSchedule ? (
              <SeaButton
                onPress={() => setIsVisibleAddDrawer(true)}
                variant={SeaButtonVariant.Primary}
                label={'Add New'}
                iconOptions={{ icon: 'add' }}
              />
            ) : undefined
          }
          secondaryActionButton={[
            <SeaDownloadButton key={'download'} onPress={() => alert('This functionality is not completed yet')} />,
            <SeaSettingsButton key={'settings'} onPress={() => setIsOpenSettingsDrawer(true)} />,
          ]}
          subNav={headerSubNavigation}
        />

        <SeaFilterRow
          searchValue={searchValue}
          onSearchValueChange={handleSearchFilterChange}
          primaryFilters={primaryFilters}
          secondaryFilters={secondaryFilters}
          filterText={filterText}
        />

        {/* Table View */}
        <View style={styles.tableView}>
          <SeaTable columns={columns} showGroupedTable rows={rows} />
        </View>
        {isVisibleAddDrawer && (
          <EditEquipmentListDrawer
            onClose={() => setIsVisibleAddDrawer(false)}
            visible={isVisibleAddDrawer}
            mode={DrawerMode.Create}
          />
        )}
      </RequirePermissions>
      {/*</ScrollablePageLayout>*/}
      {isOpenSettingsDrawer && (
        <MaintenanceSettingsDrawer
          title="Equipment List Settings"
          visible={isOpenSettingsDrawer}
          onClose={() => setIsOpenSettingsDrawer(false)}
        />
      )}
    </>
  )
}

const buildRows = (items: Equipment[], onPress: (item: Equipment) => void, vesselSystems?: CategoriesData) => {
  return items.map(item => ({
    data: item,
    onPress: (item: Equipment) => onPress(item),
    group: (item: Equipment) => renderCategoryName(item.systemId, vesselSystems),
  }))
}

const buildColumns = (vesselLocations?: CategoriesData) => {
  return [
    {
      label: '',
      width: 60,
      render: item => <SeaTableImage files={item.files} />,
      compactModeOptions: {
        isThumbnail: true,
      },
    },
    {
      label: 'Equipment',
      value: item => item.equipment,
      style: { fontWeight: 'bold' },
      compactModeOptions: {
        rowPosition: CompactRowPosition.Title,
      },
    },
    {
      label: 'Location',
      icon: () => <SeaTableIconLocation />,
      value: item => {
        return formatValue(renderCategoryName(item.locationId, vesselLocations))
      },
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Large,
        },
      },
    },
    {
      label: 'Make',
      value: item => item.make,
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Large,
        },
      },
    },
    {
      label: 'Model',
      value: item => item.model,
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Large,
        },
      },
    },
    {
      label: 'Serial #',
      value: item => item.serial,
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Large,
        },
      },
    },
    {
      label: 'Critical',
      render: item => <>{item?.isCritical ? <SeaTableIconFlag /> : null}</>,
      compactModeOptions: {
        rowPosition: CompactRowPosition.BottomRightCorner,
      },
    },
  ] as SeaTableColumn<Equipment>[]
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    height: '100%',
    // backgroundColor: 'red',
  },
  tableView: {
    flex: 1,
    height: '100%',
    width: '100%',
    marginTop: 16,
  },
})
