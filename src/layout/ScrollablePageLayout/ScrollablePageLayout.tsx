import React from 'react'
import { FlatList, FlatListProps, ScrollView, View } from 'react-native'

interface ScrollablePageLayoutProps<T> extends Omit<FlatListProps<T>, 'data' | 'renderItem' | 'keyExtractor'> {}

/**
 * This component is used to wrap the content of a page that needs to be scrollable for specially
 * large Table components and data
 *
 * Notes: This flat list loads the children as a single item
 *
 * @constructor
 */
export const ScrollablePageLayout = ({ children, ...props }: ScrollablePageLayoutProps<T>) => {
  // return (
  //   <FlatList
  //     {...props}
  //     /** This is magic is to load the flat list where all the children are loaded as a single item */
  //     data={['pageLayout'] as any[]}
  //     nestedScrollEnabled={true}
  //     removeClippedSubviews={false}
  //     renderItem={() => (
  //       <>
  //         {/** The page content is loaded here */}
  //         {children}
  //       </>
  //     )}
  //   />
  // )
  // return <ScrollView {...props}>{children}</ScrollView>
  return (
    <View style={{ flex: 1, backgroundColor: 'red', padding: 50, height: '100%' }} {...props}>
      {children}
    </View>
  )
  // return children
}
