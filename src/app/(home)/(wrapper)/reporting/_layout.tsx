import React, { useCallback, useContext, useState } from 'react'
import { Stack } from 'expo-router'
import { DateRange, DateRangeOption } from '@src/components/_atoms/_inputs/SeaDateRange/SeaDateRangeDropdown'
import { sharedState } from '@src/shared-state/shared-state'
import { subtractInterval } from '@src/lib/datesAndTime'
import { useFocusEffect } from '@react-navigation/native'

type ReportSettingsContextValue = {
  selectedVesselIds: string[]
  setSelectedVesselIds: (selectedVesselIds: string[]) => void
  dateRange: DateRange
  setDateRange: (dateRange: DateRange) => void
  dateRangeDescription: string
  setDateRangeDescription: (dateRangeDescription: string) => void
}

export enum PostFetchAction {
  Save = 'save',
  // None = 'none',
}

// @ts-ignore
const ReportSettingsContext = React.createContext<ReportSettingsContextValue>({})

export function useReportSettings(): ReportSettingsContextValue {
  return useContext<ReportSettingsContextValue>(ReportSettingsContext)
}

export default function LayoutPage() {
  // Shared Data
  const today = sharedState.today.use()!
  const vesselIds = sharedState.vesselIds.use()

  const [selectedVesselIds, setSelectedVesselIds] = useState<string[]>([])
  const [dateRangeDescription, setDateRangeDescription] = useState(DateRangeOption.Last12Months)
  const [dateRange, setDateRange] = useState<DateRange>({
    label: 'Last 12 Months',
    from: subtractInterval(today, '1y').toISODate(),
    to: today,
  })

  useFocusEffect(
    useCallback(() => {
      if (vesselIds && selectedVesselIds?.length === 0) {
        setSelectedVesselIds(vesselIds)
      }
    }, [selectedVesselIds?.length, vesselIds])
  )

  const value: ReportSettingsContextValue = {
    selectedVesselIds,
    setSelectedVesselIds,
    dateRange,
    setDateRange,
    dateRangeDescription,
    setDateRangeDescription,
  }

  return (
    <ReportSettingsContext.Provider value={value}>
      <Stack
        screenOptions={{
          gestureEnabled: false,
          headerBackVisible: false,
          animation: 'none',
          headerShown: false,
        }}
      />
    </ReportSettingsContext.Provider>
  )
}
