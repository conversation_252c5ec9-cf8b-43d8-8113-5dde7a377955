import { PostFetchAction, useReportSettings } from '@src/app/(home)/(wrapper)/reporting/_layout'
import {
  useSafetyEquipmentReportSettings,
  useSafetyReportSettings,
} from '@src/app/(home)/(wrapper)/reporting/reportingSafety/_layout'
import { SafetyEquipmentExpiriesTableWithGraph } from '@src/pages/Reporting/Safety/live/SafetyEquipmentExpiriesTableWithGraph'
import { useReportingSafetyEquipments } from '@src/shared-state/Reporting/SafetyReporting/useReportingSafetyEquipment'
import { useReportingSafetyItemsByIds } from '@src/shared-state/Reporting/SafetyReporting/useReportingSafetyItemsById'

export default function ReportingSafetyEquipmentExpiriesPage() {
  // Report Settings
  const { selectedVesselIds } = useReportSettings()

  // Form the data
  const { filters } = useSafetyReportSettings()
  const { data, fetchedDatetime } = useSafetyEquipmentReportSettings()
  const { seaFilterTagsValue } = filters

  // Handle the data fetching on page Load
  const vesselSafetyItemsById = useReportingSafetyItemsByIds(true, selectedVesselIds)

  // Refresh the data
  useReportingSafetyEquipments(
    // visible && graphAvailability.safetyEquipment,
    true,
    selectedVesselIds,
    // TODO: Check if we need to filter everytime
    // seaFilterTagsValue.overdue.isActive,
    // seaFilterTagsValue.upcoming.isActive,
    PostFetchAction.Save
  )

  // console.log('TTT - datat count I', data.length)

  return (
    <SafetyEquipmentExpiriesTableWithGraph
      safetyEquipmentItemsData={data ?? []}
      selectedVesselIds={selectedVesselIds}
      seaFilterTagsValue={seaFilterTagsValue}
      fetchedDatetime={fetchedDatetime}
      vesselSafetyItemsById={vesselSafetyItemsById}
    />
  )
}
