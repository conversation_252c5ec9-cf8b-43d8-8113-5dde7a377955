import { useCallback, useEffect, useMemo, useState } from 'react'
import { useWindowDimensions } from 'react-native'
import {
  DEVICE_SIZES,
  getDeviceSize,
  getDeviceWidth,
  isDeviceEqualOrLargerThan,
  isDeviceEqualOrSmallerThan,
} from '@src/lib/device'
import { useFocusEffect } from '@react-navigation/native'

/**
 * Custom hook to determine the device size based on window dimensions.
 *
 * @returns {Object} An object containing the width and boolean flags for device types.
 */
export const useDeviceSize = () => {
  const { width } = useWindowDimensions()
  const [deviceSize, setDeviceSize] = useState(getDeviceSize(width))

  const isEqualOrBiggerThan = useCallback(
    (size: DEVICE_SIZES) => isDeviceEqualOrLargerThan(deviceSize, size),
    [deviceSize]
  )
  const isEqualOrSmallerThan = useCallback(
    (size: DEVICE_SIZES) => isDeviceEqualOrSmallerThan(deviceSize, size),
    [deviceSize]
  )

  // useEffect(() => {
  //   const newDeviceSize = getDeviceSize(width)
  //   if (deviceSize !== newDeviceSize) {
  //     setDeviceSize(newDeviceSize)
  //   }
  // }, [deviceSize, width])

  useFocusEffect(
    useCallback(() => {
      const newDeviceSize = getDeviceSize(width)
      if (deviceSize !== newDeviceSize) {
        setDeviceSize(newDeviceSize)
      }
    }, [deviceSize, width])
  )

  return { deviceSize, isEqualOrBiggerThan, isEqualOrSmallerThan }
}
/**
 * Custom hook to determine the width size based on window dimensions.
 * Returns an object that will return true for isMobileWidth, isTabletWidth or isDesktopWidth and false for the rest
 *
 * returns { isMobileWidth: true, isTabletWidth: false, isDesktopWidth: false }
 */
export const useDeviceWidth = () => {
  const { deviceSize } = useDeviceSize()
  return useMemo(() => getDeviceWidth(deviceSize), [deviceSize])
}
