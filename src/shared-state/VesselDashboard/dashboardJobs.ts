import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { SharedStateConfig, sharedState } from '@src/shared-state/shared-state'
import { canView } from '@src/shared-state/Core/userPermissions'
import { Job } from '@src/shared-state/VesselMaintenance/jobs'
import { registerFiles } from '@src/shared-state/FileSyncSystem/filesToCache'
import { MAX_DATE } from '@src/lib/datesAndTime'

export type DashboardJobsData = {
  top5: Job[]
  numHighestPriority: number
}

export const dashboardJobsConfig: SharedStateConfig<DashboardJobsData> = {
  isAlwaysActive: false,
  dependencies: ['vesselId'],
  countLiveDocs: () => sharedState.dashboardJobs.current?.numHighestPriority ?? 0,
  run: (done, set, clear) => {
    clear()
    const vesselId = sharedState.vesselId.current

    if (vesselId && canView('jobList')) {
      return onSnapshot(
        query(
          collection(firestore, 'jobs'),
          where('vesselId', '==', vesselId),
          where('state', '==', 'active'),
          where('priority', 'in', ['8urgent', '6high']),
          orderBy('priority', 'desc')
        ),
        snap => {
          done()
          const upcoming = snap.docs.map(doc => {
            registerFiles(doc.data().files, 'jobs', doc.data())
            return {
              id: doc.id,
              ...doc.data(),
            } as Job
          })

          upcoming.sort((a, b) => {
            // jobs with dateDue will be sorted higher than default alphabetical
            if (a.priority === b.priority) {
              return (
                // (a.dateDue ? a.dateDue : Number.MAX_SAFE_INTEGER) -
                // (b.dateDue ? b.dateDue : Number.MAX_SAFE_INTEGER)
                (a.dateDue ? a.dateDue : MAX_DATE).localeCompare(b.dateDue ? b.dateDue : MAX_DATE)
              )
            }
            return 0
          })

          if (upcoming) {
            set({
              top5: upcoming.slice(0, 5),
              numHighestPriority: upcoming.length,
            })
          }
        },
        error => {
          done()
          // This should be very rare
          console.log(`Failed to access jobs for vessel ${vesselId} on dashboard`, error)
        }
      )
    } else {
      done()
    }
  },
}
