import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { SharedStateConfig, sharedState } from '@src/shared-state/shared-state'
import { canView } from '@src/shared-state/Core/userPermissions'
import { getDayOffset, warnDays } from '@src/lib/datesAndTime'
import { SafetyCheckItem } from '@src/shared-state/VesselSafety/safetyCheckItems'
import { registerFiles } from '@src/shared-state/FileSyncSystem/filesToCache'

//
// Loads dashboard safety items (active)
//

export type DashboardSafetyCheckItemsData = {
  top5: SafetyCheckItem[]
  numHighestPriority: number
  byId: {
    [id: string]: SafetyCheckItem
  }
}

export const dashboardSafetyCheckItemsConfig: SharedStateConfig<DashboardSafetyCheckItemsData> = {
  isAlwaysActive: false,
  dependencies: ['dashboardSafetyCheckFaultedItems', 'dashboardSafetyCheckUpcomingItems'],
  run: (done, set, clear) => {
    done()
    const dashboardSafetyCheckFaultedItems = sharedState.dashboardSafetyCheckFaultedItems.current
    const dashboardSafetyCheckUpcomingItems = sharedState.dashboardSafetyCheckUpcomingItems.current
    if (dashboardSafetyCheckFaultedItems || dashboardSafetyCheckUpcomingItems) {
      const prioritised = [...(dashboardSafetyCheckFaultedItems || []), ...(dashboardSafetyCheckUpcomingItems || [])]
      const byId: { [id: string]: SafetyCheckItem } = {}
      prioritised.forEach(item => {
        registerFiles(item.files, 'safetyCheckItems', item)
        byId[item.id] = item
      })
      set({
        byId,
        top5: prioritised.slice(0, 5),
        numHighestPriority: prioritised.length,
      })
    } else {
      clear()
    }
  },
  notes: 'The top5 faulted and upcoming safety check items for the vessel dashboard',
}

export const dashboardSafetyCheckFaultedItemsConfig: SharedStateConfig<SafetyCheckItem[]> = {
  isAlwaysActive: false,
  dependencies: ['vesselId', 'todayMillis'], // todayMillis is used due to calculating around days
  countLiveDocs: () => sharedState.dashboardSafetyCheckFaultedItems.current?.length ?? 0,
  run: (done, set, clear) => {
    clear()
    const vesselId = sharedState.vesselId.current
    if (vesselId && canView('safetyEquipmentChecks')) {
      return onSnapshot(
        query(
          collection(firestore, 'safetyCheckItems'),
          where('vesselId', '==', vesselId),
          where('state', '==', 'active'),
          where('hasFault', '==', true),
          orderBy('dateDue', 'asc')
        ),
        snap => {
          done()
          const faulted = snap.docs.map(doc => {
            return {
              id: doc.id,
              ...doc.data(),
            } as SafetyCheckItem
          })
          set(faulted)
        },
        error => {
          done()
          // This should be very rare
          console.log(`Failed to access faulted dashboard safety check items for vessel ${vesselId}`, error)
        }
      )
    } else {
      done()
    }
  },
  notes: 'Faulted safety check items for dashboard use only',
}

export const dashboardSafetyCheckUpcomingItemsConfig: SharedStateConfig<SafetyCheckItem[]> = {
  isAlwaysActive: false,
  dependencies: ['vesselId', 'todayMillis'], // todayMillis is used due to calculating around days
  countLiveDocs: () => sharedState.dashboardSafetyCheckUpcomingItems.current?.length ?? 0,
  run: (done, set, clear) => {
    clear()
    const vesselId = sharedState.vesselId.current
    if (vesselId && canView('safetyEquipmentChecks')) {
      return onSnapshot(
        query(
          collection(firestore, 'safetyCheckItems'),
          where('vesselId', '==', vesselId),
          where('state', '==', 'active'),
          where('dateDue', '<', getDayOffset(warnDays.safetyEquipmentChecks[0])),
          where('hasFault', '==', false),
          orderBy('dateDue', 'asc')
        ),
        snap => {
          done()
          const upcoming = snap.docs.map(doc => {
            return {
              id: doc.id,
              ...doc.data(),
            } as SafetyCheckItem
          })
          set(upcoming)
        },
        error => {
          done()
          // This should be very rare
          console.log(`Failed to access upcoming dashboard safety check items for vessel ${vesselId}`, error)
        }
      )
    } else {
      done()
    }
  },
  notes: "Upcoming safety check items that aren't faults for dashboard use only",
}
