import { SharedStateConfig, sharedState } from '@src/shared-state/shared-state'
import { canView } from '@src/shared-state/Core/userPermissions'
import { getDayOffset, warnDays } from '@src/lib/datesAndTime'
import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { TrainingTask } from '@src/shared-state/Crew/trainingTasks'

export type DashboardVesselCrewTrainingData = {
  top5: TrainingTask[]
  totalExpiredOrDueSoon: number
}

export const dashboardVesselCrewTrainingConfig: SharedStateConfig<DashboardVesselCrewTrainingData> = {
  isAlwaysActive: false,
  dependencies: ['vesselId'],
  countLiveDocs: () => sharedState.dashboardVesselCrewTraining.current?.totalExpiredOrDueSoon ?? 0,
  run: (done, set, clear) => {
    clear()
    const vesselId = sharedState.vesselId.current
    if (vesselId && canView('crewTraining')) {
      return onSnapshot(
        query(
          collection(firestore, 'trainingTasks'),
          where('vesselId', '==', vesselId),
          where('state', '==', 'active'),
          where('dateDue', '<', getDayOffset(warnDays.crewTraining[0])),
          orderBy('dateDue', 'asc')
        ),
        snap => {
          done()
          const upcoming = snap.docs.map(doc => {
            return {
              id: doc.id,
              ...doc.data(),
            } as TrainingTask
          })

          set({
            top5: upcoming.slice(0, 5),
            totalExpiredOrDueSoon: upcoming.length,
          })
        },
        error => {
          done()
          console.log(`Failed to access crew training for vessel ${vesselId} on vessel dashboard`, error)
        }
      )
    } else {
      done()
    }
  },
}
