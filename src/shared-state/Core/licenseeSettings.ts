import { doc, docExists, DocumentSnapshot, onSnapshot } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { SharedStateConfig, UpdateableDocument, sharedState } from '@src/shared-state/shared-state'
import { FileSyncDeviceSettingsJson } from '@src/shared-state/FileSyncSystem/fileSyncDeviceSettings'

export type RiskRegisterSettings = {
  version: 1 | 2
  likelihoods: {
    rows: number[]
    [Ln: string]:
      | {
          name: string
          description: string
        }
      | number[]
  }
  consequences: {
    columns: number[]
    [Cn: string]:
      | {
          name: string
          description: string
        }
      | number[]
  }
  matrix: Record<string, string>
  evaluations: {
    name: string
    color: string
    description: string
  }[]
  riskPrompts: string[]
  controlPrompts: string[]
}

export interface LicenseeSettings extends UpdateableDocument {
  hasMfaEmail: boolean
  hasOffline: boolean
  maxSessionSeconds: number
  region: string
  timeZone: string
  riskRegister: RiskRegisterSettings
  hasReporting: boolean
  hasIncidents: boolean
  hasSafetyCheckTaskTime: boolean
  hasMaintenanceTaskTime: boolean
  previousLicenseeIds?: string[] // Holds userIds for users who were the licensee in the past
  correctiveActionTags?: string[] // List of tags that can be used for corrective actions
  hasCorrectiveActions: boolean
  divisions?: {
    id: string
    name: string
    parentId: 'root' | string
  }[] // See: divisions.ts where this is used
  deviceStorageDefaults?: FileSyncDeviceSettingsJson
}

export const licenseeSettingsConfig: SharedStateConfig<LicenseeSettings> = {
  isAlwaysActive: true,
  dependencies: ['licenseeId'],
  countLiveDocs: () => (sharedState.licenseeSettings.current ? 1 : 0),
  run: (done, set, clear) => {
    clear()
    const licenseeId = sharedState.licenseeId.current
    if (licenseeId) {
      return onSnapshot(
        doc(firestore, 'licenseeSettings', licenseeId),
        (doc: DocumentSnapshot) => {
          done()
          if (docExists(doc)) {
            set({
              ...doc.data(),
            } as LicenseeSettings)
          }
        },
        error => {
          done()
          console.log(`Error getting licenseeSettings for licensee ${licenseeId}`, error)
        }
      )
    }
  },
}
