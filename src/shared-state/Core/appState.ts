import { AppState as RNAppState, Platform } from 'react-native'
import { sharedState } from '@src/shared-state/shared-state'
import { doc, setDoc } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase/services/firestore.service'
import { setAppIsOkToRestart } from '@src/shared-state/General/diagnoseRefreshProblems'
import { disableFirestoreNetwork, enableFirestoreNetwork } from './firestoreState'

export type AppState = {
  isActive: boolean
  lastActive?: number
  lastInactive?: number
}

export const appStateConfig = {
  isAlwaysActive: true,
  default: {
    isActive: true, // Assume when the app is initialising, it will be active
  },
  notes: 'Source: initAppState',
}

export const initAppState = () => {
  // Set initial state
  onAppStateChanged(RNAppState.currentState, true)

  // Subscribe to app state changes
  const subscription = RNAppState.addEventListener('change', nextAppState => {
    onAppStateChanged(nextAppState, false)
  })

  // Return cleanup function
  return () => {
    subscription.remove()
  }
}

export const onAppStateChanged = async (nextAppState: string, justStarted = false) => {
  const isAppActive = nextAppState === 'active'

  // Let diagnoseIosRefresh that the next startup is intentional or not.
  // If the app is going into the background (i.e. start.isActive is false), the user could be about to shut down the app.
  // However, if the app is in the foreground, then if the the app restarts it is probably unintentional
  setAppIsOkToRestart(!isAppActive, isAppActive ? 'App to foreground' : 'App to background')

  const appState = {
    ...sharedState.appState.current,
  } as AppState
  appState.isActive = isAppActive
  if (appState.isActive) {
    appState.lastActive = Date.now()
  } else {
    appState.lastInactive = Date.now()
  }
  sharedState.appState.set(appState)

  // // Diagnose issues with ios apps coming back into the foreground
  // if (Platform.OS === "ios" && !justStarted) {
  //   if (isAppActive) {
  //     // While firestore network is still disabled, let's check if indexedDb is still functional
  //     let isBroken = false;
  //     setDoc(
  //       doc(firestore, "global", "iosTest"),
  //       {
  //         whenLastActive: Date.now(),
  //       },
  //       { merge: true },
  //     )
  //       .catch((error) => {
  //         console.error("Failed to update iosTest", error);
  //         if (
  //           error?.message &&
  //           error.message.toLowerCase().indexOf("indexeddb") !== -1
  //         ) {
  //           // This is where we know there an indexedDb problem has occurred
  //           isBroken = true;
  //           sharedState.navigateTo.set("/restart?reason=indexedDbError");
  //           disableFirestoreNetwork();
  //         }
  //       })
  //       .then(() => {
  //         console.log("iosTest succeeded");
  //       });
  //
  //     //debugApp('File Caching', `iOS app coming into foreground, testing to see if we're ok...`);
  //     setTimeout(() => {
  //       //debugApp('File Caching', `iOS Enable firestore network? appState.isActive=${sharedState.appState.isActive} isBroken=${isBroken}`);
  //       if (sharedState.appState.isActive && !isBroken) {
  //         enableFirestoreNetwork();
  //       }
  //     }, 5 * 1000);
  //   } else {
  //     disableFirestoreNetwork();
  //   }
  // }
}

// export const onAppStateChanged = async (nextAppState: string, justStarted = false) => {
//     const isAppActive = nextAppState === 'active';

//     // Update app restart status
//     await setAppIsOkToRestart(
//         !isAppActive,
//         isAppActive ? 'App to foreground' : 'App to background'
//     );

//     const appState = {
//         ...sharedState.appState.current
//     } as AppState;

//     appState.isActive = isAppActive;
//     if (appState.isActive) {
//         appState.lastActive = Date.now();
//     } else {
//         appState.lastInactive = Date.now();
//     }
//     sharedState.appState.set(appState);

//     // Handle iOS-specific issues
//     if (Platform.OS === 'ios' && !justStarted) {
//         if (isAppActive) {
//             // Test IndexedDB functionality when app comes to foreground
//             let isBroken = false;
//             try {
//                 await setDoc(
//                     doc(firestore, 'global', 'iosTest'),
//                     {
//                         whenLastActive: Date.now()
//                     },
//                     { merge: true }
//                 );
//                 console.log('iosTest succeeded');
//             } catch (error: any) {
//                 console.error('Failed to update iosTest', error);
//                 if (error?.message?.toLowerCase().includes('indexeddb')) {
//                     isBroken = true;
//                     sharedState.navigateTo.set('/restart?reason=indexedDbError');
//                     await disableFirestoreNetwork();
//                 }
//             }

//             // Enable Firestore network after delay if app is still active and not broken
//             setTimeout(async () => {
//                 if (sharedState.appState.current?.isActive && !isBroken) {
//                     await enableFirestoreNetwork();
//                 }
//             }, 5000);
//         } else {
//             await disableFirestoreNetwork();
//         }
//     }
// };

// Optional: Add background/foreground detection for Android
let appStateTimeout: NodeJS.Timeout | null = null

export const checkAppState = () => {
  if (appStateTimeout) {
    clearTimeout(appStateTimeout)
  }

  appStateTimeout = setTimeout(() => {
    const currentState = RNAppState.currentState
    onAppStateChanged(currentState, false)
  }, 500)
}

// Optional: Add more detailed state tracking
export const getDetailedAppState = (): 'active' | 'background' | 'inactive' | 'unknown' => {
  const currentState = RNAppState.currentState
  switch (currentState) {
    case 'active':
      return 'active'
    case 'background':
      return 'background'
    case 'inactive':
      return 'inactive'
    default:
      return 'unknown'
  }
}
