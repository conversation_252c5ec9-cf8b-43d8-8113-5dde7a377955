import {
  doc,
  docExists,
  DocumentData,
  DocumentSnapshot,
  onSnapshot,
  setDoc,
} from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase/services/firestore.service'
import { SharedStateConfig, UpdateableDocument, sharedState } from '@src/shared-state/shared-state'
import * as Device from 'expo-device'
import * as Application from 'expo-application'
import Constants from 'expo-constants'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { formatSeaDate, makeDateTime, toInt } from '@src/lib/util'
import { Platform } from 'react-native'
import packageJson from '../../../package.json'
import 'react-native-get-random-values'
import { v4 as uuidv4 } from 'uuid'
import * as SecureStore from 'expo-secure-store'
import * as FileSystem from 'expo-file-system'
import { isAndroid, isIOS, isNative } from '@src/lib/device'

export const buildNumber: Record<string, string> = {
  ios: Constants.expoConfig?.ios?.buildNumber ?? 'unknown',
  android: Constants.expoConfig?.android?.versionCode?.toString() ?? 'unknown',
  web: Constants.expoConfig?.version ?? 'unknown',
}

export interface DeviceInfo extends UpdateableDocument {
  androidSDKVersion?: number
  deviceId: string
  diskFree?: number
  diskTotal?: number
  iOSVersion?: string
  isVirtual: boolean
  lastDate: string
  manufacturer: string
  memUsed?: number
  model: string
  name?: string
  operatingSystem: string
  osVersion: string
  platform: string
  userAgent?: string
  realDiskFree?: number
  realDiskTotal?: number
  timezone: string
  userId: string
  webViewVersion?: string
  whenCreated?: number
  whenUpdated: number
  version: string
  build: number
}

export let physicalDeviceInfo = {} as DeviceInfo

export const initPhysicalDeviceInfo = async () => {
  try {
    // const deviceInfo: Partial<DeviceInfo> = {
    //     androidSDKVersion: Platform.OS === 'android' ? Platform.Version : undefined,
    //     iOSVersion: Platform.OS === 'ios' ? Platform.Version.toString() : undefined,
    //     isVirtual: Device.isDevice,
    //     manufacturer: Device.brand || undefined,
    //     model: Device.modelName || await Device.modelId(),
    //     name: Device.deviceName || undefined,
    //     operatingSystem: Device.osName || Platform.OS,
    //     osVersion: Device.osVersion || Platform.Version.toString(),
    //     platform: Platform.OS,
    //     version: Device.osVersion || Platform.Version.toString(),
    //     build: parseInt(buildNumber[Platform.OS] || '1'),
    // };
    const deviceInfo: Partial<DeviceInfo> = {
      platform: Platform.OS,
      isVirtual: !Device.isDevice,
    }

    // Basic device info
    if (Device.modelName) deviceInfo.model = Device.modelName
    if (Device.osName) deviceInfo.operatingSystem = Device.osName.toLowerCase()
    if (Device.osVersion) deviceInfo.osVersion = Device.osVersion
    if (Device.brand) deviceInfo.manufacturer = Device.brand
    if (Device.deviceName) deviceInfo.name = Device.deviceName

    // Storage info (if available)
    try {
      const storage = await FileSystem.getFreeDiskStorageAsync()
      const totalStorage = await FileSystem.getTotalDiskCapacityAsync()
      if (storage) deviceInfo.diskFree = storage
      if (totalStorage) deviceInfo.diskTotal = totalStorage
    } catch (e) {
      console.log('Storage info not available')
    }

    // Application info
    try {
      if (isNative) {
        if (isAndroid) {
          deviceInfo.androidSDKVersion = Platform.Version as number
          deviceInfo.deviceId = Application.getAndroidId() ?? undefined
        } else if (isIOS) {
          deviceInfo.iOSVersion = Platform.Version.toString()
          deviceInfo.deviceId = (await Application.getIosIdForVendorAsync()) ?? undefined
        }
        deviceInfo.version = Application.nativeApplicationVersion ?? undefined
      }
    } catch (e) {
      console.log('Application info not available', e)
    }

    // Remove undefined properties
    Object.keys(deviceInfo).forEach(key => {
      if (deviceInfo[key as keyof DeviceInfo] === undefined) {
        delete deviceInfo[key as keyof DeviceInfo]
      }
    })

    // Get network info
    // const networkState = await Network.getNetworkStateAsync();
    // console.log(`Network state:`, networkState);

    console.log(`Physical device info:`, deviceInfo)
    AsyncStorage.setItem(`_deviceInfo`, JSON.stringify(deviceInfo))
    physicalDeviceInfo = deviceInfo as DeviceInfo
  } catch (error) {
    console.error('Error initializing physical device info:', error)
  }
}

export const deviceInfoConfig: SharedStateConfig<DeviceInfo> = {
  isAlwaysActive: true,
  dependencies: ['deviceId', 'userId'],
  countLiveDocs: () => (sharedState.deviceInfo.current ? 1 : 0),
  run: (done, set, clear) => {
    clear()
    // console.log('deviceInfoConfig: run');
    const deviceId = sharedState.deviceId.current
    const userId = sharedState.userId.current

    if (userId && deviceId) {
      let isActive = true

      // Load deviceInfo from Firestore
      const cleanup = onSnapshot(
        doc(firestore, 'deviceInfo', `${userId}${deviceId}`),
        (doc: DocumentSnapshot<DocumentData>) => {
          done()
          console.log('deviceInfo doc', doc)
          // @ts-ignore
          if (docExists(doc)) {
            console.log('doc exists', doc.data())
            set({
              id: doc.id,
              ...doc.data(),
            } as DeviceInfo)
          } else {
            console.log('doc does not exist')
          }
        },
        error => {
          done()
          clear()
          console.log(`Error getting deviceInfo for id=${userId}${deviceId}`, error)
        }
      )

      // Record latest deviceInfo
      const updateDeviceInfo = async () => {
        try {
          if (!isActive) return

          const hasRunBefore = (await AsyncStorage.getItem('hasRunBefore')) === 'true'
          console.log('hasRunBefore date', typeof hasRunBefore)
          const deviceId = sharedState.deviceId.current
          const userId = sharedState.userId.current

          if (!deviceId || !userId) return

          console.log(`Saving deviceInfo under docId=${userId}${deviceId}`)

          console.log('updateDeviceInfo physicalDeviceInfo', physicalDeviceInfo)

          const deviceInfo = {
            ...physicalDeviceInfo,
            timezone: makeDateTime().zoneName,
            lastDate: formatSeaDate(),
            userId: userId,
            deviceId: deviceId,
            version: packageJson.version, // read from package.json
            build: toInt(buildNumber[Platform.OS]),
            // TODO: Check why the userAgent is undefined for Sims
            userAgent: navigator?.userAgent ?? 'Test SIM agent',
            whenUpdated: Date.now(),
          }
          if (!hasRunBefore) {
            deviceInfo.whenCreated = Date.now()
          }

          console.log('deviceInfo last', deviceInfo)

          // await
          // setDoc
          //     (
          //         doc(firestore, 'deviceInfo', `${userId}${deviceId}`),
          //         deviceInfo,
          //         { merge: true }
          //     )
          await setDoc(doc(firestore, 'deviceInfo', `${userId}${deviceId}`), deviceInfo, { merge: true })
          // .then(() => {
          //     console.log('DeviceInfo updated successfully');
          // })
          // .catch((error) => {
          //     console.error('Error updating deviceInfo:', error);
          // });

          if (!hasRunBefore) {
            console.log(`------ Sea Flux has been freshly run on this device for the first time!`)
            AsyncStorage.setItem('hasRunBefore', 'true')
          }
        } catch (error) {
          console.error('Error updating device info:', error)
        }
      }

      // Initial update
      updateDeviceInfo()

      return () => {
        isActive = false
        cleanup()
      }
    }
  },
}

// export const deviceInfoConfig: SharedStateConfig<DeviceInfo> = {
//     isAlwaysActive: true,
//     dependencies: ['deviceId', 'userId'],
//     countLiveDocs: () => sharedState.deviceInfo.current ? 1 : 0,
//     run: (done, set, clear) => {
//         clear();
//         const deviceId = sharedState.deviceId.current;
//         const userId = sharedState.userId.current;

//         if (userId && deviceId) {
//             let isActive = true;
//             // Load deviceInfo from Firestore
//             const cleanup = onSnapshot(
//                 doc(firestore, 'deviceInfo', `${userId}${deviceId}`),
//                 (doc) => {
//                     done();
//                     if (docExists(doc)) {
//                         set({
//                             id: doc.id,
//                             ...doc.data()
//                         } as DeviceInfo);
//                     }
//                 },
//                 (error) => {
//                     done();
//                     clear();
//                     // console.log(`Error getting deviceInfo for id=${userId}${deviceId}`, error);
//                 }
//             );

//             // Record latest deviceInfo
//             let hasRunBefore = window.localStorage.getItem('hasRunBefore') ? true : false;
//             Device.getInfo().then((info) => {
//                 if (!isActive) return;
//                 const deviceId = sharedState.deviceId.current;
//                 const userId = sharedState.userId.current;

//                 // console.log(`Saving deviceInfo under docId=${userId}${deviceId}`);
//                 const info = AsyncStorage.getItem('_deviceInfo')
//                 setDoc(
//                     doc(firestore, 'deviceInfo', `${userId}${deviceId}`),
//                     {
//                         ...info,
//                         timezone: makeDateTime().zoneName,
//                         lastDate: formatSeaDate(),
//                         userId: userId,
//                         deviceId: deviceId,
//                         version: packageJson.version,
//                         build: toInt(packageJson.build),
//                         userAgent: navigator?.userAgent,
//                         whenCreated: hasRunBefore ? undefined : Date.now(),
//                         whenUpdated: Date.now()
//                     },
//                     { merge: true }
//                 );
//                 if (!hasRunBefore) {
//                     // console.log(`------ Sea Flux has been freshly run on this device or browser for the first time!`);
//                     window.localStorage.setItem('hasRunBefore', 'true');
//                 }
//             });

//             return () => {
//                 isActive = false;
//                 cleanup();
//             };
//         }
//     },
// };

export const getDeviceId = async () => {
  if (Platform.OS === 'web') {
    return await getDeviceIdForWeb()
  }

  const newUniqueId =
    Platform.OS === 'android'
      ? (Application.getAndroidId() ?? uuidv4())
      : ((await Application.getIosIdForVendorAsync()) ?? uuidv4())

  let uniqueId = null
  try {
    uniqueId = await SecureStore.getItemAsync('uniqueDeviceId')
  } catch (error) {
    console.error(error)
  }

  try {
    if (!uniqueId) {
      uniqueId = newUniqueId
      await SecureStore.setItemAsync('uniqueDeviceId', uniqueId)
    }
  } catch (error) {
    uniqueId = newUniqueId
    console.error(error)
  }

  return uniqueId
}

const getDeviceIdForWeb = async () => {
  const newUniqueId = uuidv4()

  let uniqueId = null
  try {
    uniqueId = await AsyncStorage.getItem('uniqueDeviceId')
  } catch (error) {
    console.error(error)
  }

  try {
    if (!uniqueId) {
      uniqueId = newUniqueId
      await AsyncStorage.setItem('uniqueDeviceId', uniqueId)
    }
  } catch (error) {
    uniqueId = newUniqueId
    console.error(error)
  }

  return uniqueId
}
