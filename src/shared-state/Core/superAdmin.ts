import {
  doc,
  docExists,
  DocumentData,
  DocumentSnapshot,
  onSnapshot,
} from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { SharedStateConfig, sharedState } from '@src/shared-state/shared-state'
import { UserType } from './user'

//
// Loads superAdmin data based on superAdminId.
// We know the user has superAdmin access if this value is defined.
//

export type SuperAdmin = UserType

export const superAdminConfig: SharedStateConfig<SuperAdmin> = {
  isAlwaysActive: true,
  dependencies: ['superAdminId'],
  countLiveDocs: () => (sharedState.superAdmin.current ? 1 : 0),
  run: (done, set, clear) => {
    clear()
    const superAdminId = sharedState.superAdminId.current
    if (superAdminId) {
      return onSnapshot(
        doc(firestore, 'users', superAdminId),
        (doc: DocumentSnapshot<DocumentData>) => {
          done()
          if (docExists(doc)) {
            console.log('No superAdmin matching id! ' + superAdminId)
            clear()
          } else {
            //console.log('got user', snapshot.docs[0].data())
            set({
              id: doc.id,
              ...doc.data(),
            })
          }
          sharedState.userPending.set(false)
        },
        error => {
          done()
          console.log(`Error getting superAdmin ${superAdminId}`, error)
          sharedState.userPending.set(false)
        }
      )
    }
  },
}
