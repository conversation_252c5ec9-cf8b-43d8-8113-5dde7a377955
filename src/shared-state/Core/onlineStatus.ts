import NetInfo from '@react-native-community/netinfo'
import { SharedStateConfig, sharedState } from '@src/shared-state/shared-state'
import { fakeErrorWaitMillis } from '@src/shared-state/General/errorsOnHold'

export type OnlineStatus = {
  isOnline: boolean
  lastWentOnline: number
  lastWentOffline: number
  history: number[]
}

export const onlineStatusConfig: SharedStateConfig<OnlineStatus> = {
  isAlwaysActive: true,
  default: {
    isOnline: false,
    lastWentOnline: Date.now(),
    lastWentOffline: Date.now(),
    history: [],
  },
}

export const initOnlineStatus = () => {
  let interval: any = null

  // Helper function to update online status
  const updateOnlineStatus = (isConnected: boolean) => {
    if (isConnected === sharedState.onlineStatus.current?.isOnline) {
      return // We only care if isConnected has changed since the last call
    }

    const onlineStatus = {
      ...sharedState.onlineStatus.current,
    } as OnlineStatus
    onlineStatus.isOnline = isConnected

    const now = Date.now()
    onlineStatus.history.unshift(now)
    if (onlineStatus.history.length > 10) {
      onlineStatus.history.pop()
    }

    sharedState.triggerProcessErrorsOnHold.set(false)
    if (onlineStatus.isOnline) {
      // Coming online
      onlineStatus.lastWentOnline = now
      interval = setTimeout(() => {
        if (
          sharedState.onlineStatus.current?.isOnline &&
          Date.now() - sharedState.onlineStatus.current.lastWentOnline >= fakeErrorWaitMillis - 1000
        ) {
          console.log('Allowing all errors to be shown')
          sharedState.triggerProcessErrorsOnHold.set(true)
        }
      }, fakeErrorWaitMillis)
    } else {
      // Going offline
      onlineStatus.lastWentOffline = now
    }

    sharedState.onlineStatus.set(onlineStatus as OnlineStatus)
  }

  // Initial check
  NetInfo.fetch().then(state => {
    updateOnlineStatus(state.isConnected ?? false)
  })

  // Subscribe to network state updates
  const unsubscribe = NetInfo.addEventListener(state => {
    updateOnlineStatus(state.isConnected ?? false)
  })

  // Return cleanup function
  return () => {
    if (interval) {
      clearTimeout(interval)
    }
    unsubscribe()
  }
}
