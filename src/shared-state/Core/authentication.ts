import { onAuthStateChanged } from '@src/lib/firebase/services/auth.service'
import { auth } from '@src/lib/firebase'
import { sharedState } from '@src/shared-state/shared-state'

export const initAuthentication = () => {
  let cleanup: (() => void) | undefined

  onAuthStateChanged(auth, (authUser: any) => {
    console.log('onAuthStateChanged', authUser)
    sharedState.authUser.set(authUser)
    sharedState.user.clear()
    sharedState.superAdmin.clear()
    if (!authUser) {
      console.log('userPending.set(false)')
      sharedState.userPending.set(false) // We can't get a user, so there's nothing left to wait for
    }
  }).then(unsubscribe => {
    console.log('unsubscribe', unsubscribe)
    cleanup = unsubscribe
  })

  // Return synchronous cleanup function
  return () => {
    console.log('cleanup', cleanup)
    if (cleanup) cleanup()
  }
}
