import {
  DocumentData,
  QueryDocumentSnapshot,
  QueryOrderByConstraint,
  collection,
  orderBy,
  where,
} from '@src/lib/firebase/services/firestore.service'
import { firestore, setupArrayQueryListener } from '@src/lib/firebase'
import { useCallback, useState } from 'react'
import { getDayOffset, warnDays } from '@src/lib/datesAndTime'
import { VesselCertificate } from '../../VesselDocuments/vesselCertificates'
import { registerFiles } from '@src/shared-state/FileSyncSystem/filesToCache'
import { useFocusEffect } from '@react-navigation/native'
import { PostFetchAction } from '@src/app/(home)/(wrapper)/reporting/_layout'
import { useVesselCertificateReportSettings } from '@src/app/(home)/(wrapper)/reporting/reportingVesselDocument/_layout'
import { DateTime } from 'luxon'

export const useReportingVesselCertificates = (selectedVesselIds: string[], postFetchAction: PostFetchAction) => {
  const [reportingVesselCertificates, setReportingVesselCertificates] = useState<VesselCertificate[]>()

  useFocusEffect(
    useCallback(() => {
      if (selectedVesselIds?.length === 0) {
        // Nothing to load
        setReportingVesselCertificates([])
        return
      }

      if (selectedVesselIds?.length) {
        const baseConstraints = [where('state', '==', 'active')]
        baseConstraints.push(where('dateExpires', '<', getDayOffset(warnDays.vesselCertificates[0])))

        console.log('TTT - Trigger call - Certificates', '1')
        return setupArrayQueryListener(
          'vesselCertificates', // what
          collection(firestore, 'vesselCertificates'),
          baseConstraints,
          'vesselId',
          'in',
          selectedVesselIds,
          [orderBy('dateExpires', 'asc') as QueryOrderByConstraint],
          (docs: QueryDocumentSnapshot<DocumentData>[], isCombined: boolean) => {
            // processDocs
            const all = [] as VesselCertificate[]
            docs.forEach(doc => {
              const certificate = {
                id: doc.id,
                ...doc.data(),
              } as VesselCertificate
              registerFiles(certificate.files, 'vesselCertificates', certificate)
              all.push(certificate)
            })

            // if (isCombined) {
            //   all.sort((a, b) => {
            //     //return (a.dateExpires ?? 0) - (b.dateExpires ?? 0);
            //     return (a.dateExpires ?? MIN_DATE).localeCompare(b.dateExpires ?? MIN_DATE)
            //   })
            // }

            setReportingVesselCertificates(all)
          }
        )
      }
    }, [selectedVesselIds])
  )

  const { setData, setFetchedDatetime } = useVesselCertificateReportSettings()
  useFocusEffect(
    useCallback(() => {
      if (postFetchAction === PostFetchAction.Save && reportingVesselCertificates) {
        setData(reportingVesselCertificates)
        setFetchedDatetime(DateTime.now().toMillis())
      }
    }, [postFetchAction, reportingVesselCertificates, setData, setFetchedDatetime])
  )

  return reportingVesselCertificates
}
