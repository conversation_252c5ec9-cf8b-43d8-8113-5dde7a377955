import { DocumentData, QueryDocumentSnapshot, collection, where } from '@src/lib/firebase/services/firestore.service'
import { firestore, setupArrayQueryListener } from '@src/lib/firebase'
import { useCallback, useEffect, useState } from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { ReportingEquipmentByIdData } from './useReportingEquipmentById'
import { ScheduledMaintenanceTask } from '../../VesselMaintenance/maintenanceSchedule'
import { useEnginesFromVessels } from '../../VesselMaintenance/useEnginesFromVessels'
import { dayDifferenceBetweenDates, getToday, warnDays } from '@src/lib/datesAndTime'
import { useFocusEffect } from '@react-navigation/native'
import { PostFetchAction } from '@src/app/(home)/(wrapper)/reporting/_layout'
import { DateTime } from 'luxon'
import { useMaintenanceScheduleTaskReportSettings } from '@src/app/(home)/(wrapper)/reporting/reportingMaintenance/_layout'

const makeTasks = (docs: QueryDocumentSnapshot<DocumentData>[]) => {
  return docs.map(doc => {
    return {
      id: doc.id,
      ...doc.data(),
    } as ScheduledMaintenanceTask
  })
}

type ScheduledMaintenanceTaskData = {
  prioritised: ScheduledMaintenanceTask[]
  byId: { [id: string]: ScheduledMaintenanceTask }
  byPriority: {
    overdue: ScheduledMaintenanceTask[]
    upcoming: ScheduledMaintenanceTask[]
  }
}

export const useReportingMaintenanceTasks = (
  isActive: boolean,
  selectedVesselIds: string[],
  // equipmentById: ReportingEquipmentByIdData | undefined,
  postFetchAction?: PostFetchAction
) => {
  const engines = useEnginesFromVessels(isActive ? selectedVesselIds : undefined)!
  const [reportingMaintenanceTasks, setReportingMaintenanceTasks] = useState<ScheduledMaintenanceTaskData>()

  useFocusEffect(
    useCallback(() => {
      setReportingMaintenanceTasks(undefined)
      if (selectedVesselIds.length === 0) {
        return
      }

      if (isActive && selectedVesselIds?.length && engines) {
        // const includeOverdue = selectedTaskStatuses.includes('overdue')
        // const includeUpcoming = selectedTaskStatuses.includes('upcoming')
        //
        // if (!includeOverdue && !includeUpcoming) {
        //   return
        // }

        console.log('TTT - Trigger call', 'Maintenance 1')

        return setupArrayQueryListener(
          'scheduledMaintenanceTasks',
          collection(firestore, 'scheduledMaintenanceTasks'),
          [where('state', '==', 'active')],
          'vesselId',
          'in',
          selectedVesselIds,
          [],
          (docs: QueryDocumentSnapshot<DocumentData>[], isCombined: boolean) => {
            const all = makeTasks(docs)
            const filteredTasks: ScheduledMaintenanceTask[] = []
            // const byId: { [id: string]: ScheduledMaintenanceTask } = {}
            // const byPriority: ScheduledMaintenanceTaskData['byPriority'] = {
            //   overdue: [],
            //   upcoming: [],
            // }

            all.forEach(task => {
              let priority = Number.MAX_SAFE_INTEGER
              let useHours = false

              if (task.dateDue) {
                // const daysAway = (task.dateDue - todayMillis) / (24 * 60 * 60 * 1000);
                const daysAway = dayDifferenceBetweenDates(getToday(), task.dateDue)
                priority = daysAway
              }

              if (task.engineId && task.engineHoursDue && engines?.byId[task.engineId]?.hours !== undefined) {
                task.engineHoursLeft = task.engineHoursDue - engines.byId[task.engineId].hours
                const engineDaysOrder = (task.engineHoursLeft / 50) * 7
                if (engineDaysOrder < priority) {
                  useHours = true
                  priority = engineDaysOrder
                }
              }

              const isOverdue = priority < 0
              const isUpcoming = priority >= 0 && priority < warnDays.maintenanceSchedule[0]

              // if ((includeOverdue && isOverdue) || (includeUpcoming && isUpcoming)) {
              //   task.priority = priority
              //   task.useHours = useHours
              //   filteredTasks.push(task)
              //   byId[task.id] = task
              //
              //   if (isOverdue) {
              //     byPriority.overdue.push(task)
              //   } else if (isUpcoming) {
              //     byPriority.upcoming.push(task)
              //   }
              // }

              if (isOverdue || isUpcoming) {
                task.priority = priority
                task.useHours = useHours
                filteredTasks.push(task)
              }
            })

            // Filter by criticality
            const finalTasks = filteredTasks.sort((a, b) => (a.priority || 0) - (b.priority || 0))
            // if (equipmentCriticality === 'critical' && equipmentById) {
            //   finalTasks = finalTasks.filter(item => equipmentById[item.equipmentId]?.isCritical)
            // } else if (equipmentCriticality === 'nonCritical' && equipmentById) {
            //   finalTasks = finalTasks.filter(item => !equipmentById[item.equipmentId]?.isCritical)
            // }
            //
            // if (isCombined) {
            //   filteredTasks.sort((a, b) => (a.priority || 0) - (b.priority || 0))
            // }
            setReportingMaintenanceTasks({
              prioritised: finalTasks,
              byId: {},
              byPriority: {},
            })
          }
        )
      }
    }, [
      isActive,
      selectedVesselIds,
      // selectedTaskStatuses,
      // equipmentById,
      // equipmentCriticality,
      engines,
    ])
  )

  const { setData, setFetchedDatetime } = useMaintenanceScheduleTaskReportSettings()
  useFocusEffect(
    useCallback(() => {
      if (postFetchAction === PostFetchAction.Save && reportingMaintenanceTasks) {
        console.log('TTT - Save call', 'Maintenance 1', reportingMaintenanceTasks.prioritised.length)
        setData(reportingMaintenanceTasks.prioritised)
        setFetchedDatetime(DateTime.now().toMillis())
      }
    }, [postFetchAction, reportingMaintenanceTasks, setData, setFetchedDatetime])
  )

  return reportingMaintenanceTasks
}
