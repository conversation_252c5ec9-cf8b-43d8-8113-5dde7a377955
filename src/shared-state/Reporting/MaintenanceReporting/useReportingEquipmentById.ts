import { DocumentData, QueryDocumentSnapshot, collection, where } from '@src/lib/firebase/services/firestore.service'
import { firestore, setupArrayQueryListener } from '@src/lib/firebase'
import { useCallback, useEffect, useState } from 'react'
import { Equipment } from '../../VesselMaintenance/equipment'
import { useFocusEffect } from '@react-navigation/native'

export type ReportingEquipmentByIdData = {
  [id: string]: Equipment
}

export const useReportingEquipmentByIds = (isActive: boolean, selectedVesselIds: string[]) => {
  const [reportingEquipmentById, setReportingEquipmentById] = useState<ReportingEquipmentByIdData>()

  useFocusEffect(
    useCallback(() => {
      setReportingEquipmentById(undefined)
      if (isActive && selectedVesselIds?.length) {
        return setupArrayQueryListener(
          'equipment', // what
          collection(firestore, 'equipment'),
          [where('state', 'in', ['active', 'deleted'])], // baseConstraints
          'vesselId',
          'in',
          selectedVesselIds,
          [], // orderBy
          (docs: QueryDocumentSnapshot<DocumentData>[], isCombined: boolean) => {
            // processDocs
            const byId = {} as {
              [id: string]: Equipment
            }
            docs.forEach(doc => {
              byId[doc.id] = doc.data() as Equipment
            })
            setReportingEquipmentById(byId)
          }
        )
      }
    }, [isActive, selectedVesselIds])
  )

  return reportingEquipmentById
}
