import {
  DocumentData,
  QueryDocumentSnapshot,
  QueryOrderByConstraint,
  collection,
  orderBy,
  where,
} from '@src/lib/firebase/services/firestore.service'
import { firestore, setupArrayQueryListener } from '@src/lib/firebase'
import { useCallback, useEffect, useState } from 'react'
import { ReportingEquipmentByIdData } from './useReportingEquipmentById'
//import { useReportSettings } from "../../../pages/Reporting/Reporting";
import { MaintenanceTaskCompleted } from '../../VesselMaintenance/maintenanceTasksCompleted'
import { addInterval, makeDateTime } from '@src/lib/datesAndTime'
import { useFocusEffect } from '@react-navigation/native'
import { PostFetchAction, useReportSettings } from '@src/app/(home)/(wrapper)/reporting/_layout'
import { DateTime } from 'luxon'
import { useMaintenanceTaskCompletedReportSettings } from '@src/app/(home)/(wrapper)/reporting/reportingMaintenance/_layout'

// const dateRange = { from: '20250101', to: '20260101' } // Dummy Value. Todo! Use useReportSettings hook once reporting filters are created.

export const useReportingCompletedMaintenanceTasks = (
  isActive: boolean,
  selectedVesselIds: string[],
  postFetchAction?: PostFetchAction
) => {
  const { dateRange } = useReportSettings()

  const [reportingCompletedMaintenanceTasks, setReportingCompletedMaintenanceTasks] =
    useState<MaintenanceTaskCompleted[]>()

  useFocusEffect(
    useCallback(() => {
      if (selectedVesselIds.length === 0) {
        // Nothing to load
        setReportingCompletedMaintenanceTasks([])
        return
      }
      setReportingCompletedMaintenanceTasks(undefined)
      console.log('TTT - Trigger call', 'Maintenance 2')
      if (isActive && selectedVesselIds?.length) {
        const baseConstraints = [where('state', '==', 'completed')]
        baseConstraints.push(where('type', 'in', ['job', 'unscheduled', 'scheduled']))
        baseConstraints.push(where('whenCompleted', '>=', makeDateTime(dateRange.from).toMillis()))
        baseConstraints.push(where('whenCompleted', '<', addInterval(dateRange.to, '1d').toMillis()))

        // let isCriticalityOk = (item: MaintenanceTaskCompleted) => true
        // if (equipmentCriticality === 'critical') {
        //   isCriticalityOk = (item: MaintenanceTaskCompleted) => {
        //     return item.equipmentId && equipmentById[item.equipmentId]?.isCritical ? true : false
        //   }
        // } else if (equipmentCriticality === 'nonCritical') {
        //   isCriticalityOk = (item: MaintenanceTaskCompleted) => {
        //     return !(item.equipmentId && equipmentById[item.equipmentId]?.isCritical)
        //   }
        // }

        return setupArrayQueryListener(
          'maintenanceTasksCompleted', // what
          collection(firestore, 'maintenanceTasksCompleted'),
          baseConstraints,
          'vesselId',
          'in',
          selectedVesselIds,
          [orderBy('whenCompleted', 'desc') as QueryOrderByConstraint],
          (docs: QueryDocumentSnapshot<DocumentData>[], isCombined: boolean) => {
            // processDocs
            const list = [] as MaintenanceTaskCompleted[]

            const scheduledTaskById = {} as {
              [id: string]: MaintenanceTaskCompleted
            }

            docs.forEach(doc => {
              const item = {
                id: doc.id,
                ...doc.data(),
                timesCompleted: 1,
              } as MaintenanceTaskCompleted
              // if (isCriticalityOk(item)) {
              if (item.type === 'scheduled' && item.maintenanceTaskId) {
                if (scheduledTaskById[item.maintenanceTaskId]?.timesCompleted !== undefined) {
                  ;(scheduledTaskById[item.maintenanceTaskId].timesCompleted as number)++
                  if (item.whenCompleted > scheduledTaskById[item.maintenanceTaskId].whenCompleted) {
                    scheduledTaskById[item.maintenanceTaskId].whenCompleted = item.whenCompleted
                  }
                } else {
                  scheduledTaskById[item.maintenanceTaskId] = item
                  list.push(item)
                }
              } else {
                list.push(item)
              }
              // }
            })

            // if (isCombined) {
            //   list.sort((a, b) => {
            //     return b.whenCompleted - a.whenCompleted
            //   })
            // }

            setReportingCompletedMaintenanceTasks(list)
          }
        )
      }
    }, [isActive, selectedVesselIds, dateRange])
  )

  const { setData, setFetchedDatetime } = useMaintenanceTaskCompletedReportSettings()
  useFocusEffect(
    useCallback(() => {
      if (postFetchAction === PostFetchAction.Save && reportingCompletedMaintenanceTasks) {
        setData(reportingCompletedMaintenanceTasks)
        setFetchedDatetime(DateTime.now().toMillis())
      }
    }, [postFetchAction, reportingCompletedMaintenanceTasks, setData, setFetchedDatetime])
  )

  return reportingCompletedMaintenanceTasks
}
