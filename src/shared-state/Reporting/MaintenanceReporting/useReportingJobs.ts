import {
  DocumentData,
  QueryDocumentSnapshot,
  QueryOrderByConstraint,
  collection,
  orderBy,
  where,
} from '@src/lib/firebase/services/firestore.service'
import { firestore, setupArrayQueryListener } from '@src/lib/firebase'
import { useCallback, useEffect, useState } from 'react'
import { ReportingEquipmentByIdData } from './useReportingEquipmentById'
import { Job, jobPriorities } from '../../VesselMaintenance/jobs'
import { registerFiles } from '@src/shared-state/FileSyncSystem/filesToCache'
import { MAX_DATE } from '@src/lib/datesAndTime'
import { PostFetchAction } from '@src/app/(home)/(wrapper)/reporting/_layout'
import { useFocusEffect } from '@react-navigation/native'
import { DateTime } from 'luxon'
import { useJobReportSettings } from '@src/app/(home)/(wrapper)/reporting/reportingMaintenance/_layout'

const queryJobPriorities = Object.keys(jobPriorities)

export const useReportingJobs = (
  isActive: boolean,
  selectedVesselIds: string[],
  // selectedJobPriorities: string[] | undefined,
  postFetchAction?: PostFetchAction
) => {
  const [reportingJobs, setReportingJobs] = useState<Job[]>()

  useFocusEffect(
    useCallback(() => {
      if (selectedVesselIds.length === 0) {
        // Nothing to load
        setReportingJobs([])
        return
      }

      console.log('TTT - Trigger call', 'Maintenance 3')
      if (isActive && selectedVesselIds?.length) {
        return setupArrayQueryListener(
          'jobs', // what
          collection(firestore, 'jobs'),
          [where('state', '==', 'active'), where('priority', 'in', queryJobPriorities)],
          'vesselId',
          'in',
          selectedVesselIds,
          [orderBy('task', 'asc') as QueryOrderByConstraint],
          (docs: QueryDocumentSnapshot<DocumentData>[], isCombined: boolean) => {
            // processDocs
            let all = [] as Job[]

            docs.forEach(doc => {
              const job = {
                id: doc.id,
                ...doc.data(),
              } as Job
              registerFiles(job.files, 'jobs', job)
              all.push(job)
            })

            // all.sort((a, b) => {
            //   if (a.priority !== b.priority) {
            //     // sort first by priority
            //     return b.priority.localeCompare(a.priority)
            //   } else if (a.dateDue !== b.dateDue) {
            //     // next sort by dateDue (may be undefined)
            //     //return (a.dateDue ? a.dateDue : Number.MAX_SAFE_INTEGER) - (b.dateDue ? b.dateDue : Number.MAX_SAFE_INTEGER);
            //     return (a.dateDue ? a.dateDue : MAX_DATE).localeCompare(b.dateDue ? b.dateDue : MAX_DATE)
            //   } else {
            //     // sort by task
            //     return a.task.localeCompare(b.task)
            //   }
            // })

            setReportingJobs(all)
          }
        )
      }
    }, [isActive, selectedVesselIds])
  )

  const { setData, setFetchedDatetime } = useJobReportSettings()
  useFocusEffect(
    useCallback(() => {
      if (postFetchAction === PostFetchAction.Save && reportingJobs) {
        setData(reportingJobs)
        setFetchedDatetime(DateTime.now().toMillis())
      }
    }, [postFetchAction, reportingJobs, setData, setFetchedDatetime])
  )

  return reportingJobs
}
