import {
  DocumentData,
  QueryDocumentSnapshot,
  QueryOrderByConstraint,
  collection,
  orderBy,
  where,
} from '@src/lib/firebase/services/firestore.service'
import { firestore, setupArrayQueryListener } from '@src/lib/firebase'
import { sharedState } from '@src/shared-state/shared-state'
import { useCallback, useEffect, useState } from 'react'
import { UserType } from '@src/shared-state/Core/user'
import { ActionLogEntry } from '../../General/actionLog'
import { addInterval, makeDateTime } from '@src/lib/datesAndTime'
import { useFocusEffect } from '@react-navigation/native'
import { useReportSettings } from '@src/app/(home)/(wrapper)/reporting/_layout'

export const useReportingActionLog = (
  isActive: boolean,
  selectedVesselIds: string[],
  filteredUsers: UserType[] | undefined
) => {
  const licenseeId = sharedState.licenseeId.use()
  const { dateRange } = useReportSettings()

  const [reportingActionLog, setReportingActionLog] = useState<ActionLogEntry[]>()

  useFocusEffect(
    useCallback(() => {
      if (filteredUsers?.length === 0) {
        // Nothing to load
        setReportingActionLog([])
        return
      }

      if (isActive && filteredUsers) {
        console.log('TTT - triggering inside')
        const userIds = filteredUsers.map(user => {
          return user.id
        }) as string[]
        return setupArrayQueryListener(
          'actionLog', // what
          collection(firestore, 'actionLog'),
          [
            where('licenseeId', '==', licenseeId),
            where('when', '>=', makeDateTime(dateRange.from).toMillis()),
            where('when', '<', addInterval(dateRange.to, '1d').toMillis()),
          ],
          'userId',
          'in',
          userIds,
          [orderBy('when', 'desc') as QueryOrderByConstraint], // orderBy
          (docs: QueryDocumentSnapshot<DocumentData>[], isCombined: boolean) => {
            // processDocs
            const array = docs.map(doc => {
              return {
                id: doc.id,
                ...doc.data(),
              } as ActionLogEntry
            })
            if (isCombined) {
              array.sort((a, b) => {
                return b.when - a.when
              })
            }
            setReportingActionLog(array)
          }
        )
      }
    }, [filteredUsers, isActive, licenseeId, dateRange.from, dateRange.to])
  )

  return reportingActionLog
}
