import { DocumentData, QueryDocumentSnapshot, collection } from '@src/lib/firebase/services/firestore.service'
import { firestore, setupArrayQueryListener } from '@src/lib/firebase'
import { useCallback, useState } from 'react'
import { SafetyEquipmentItem } from '../../VesselSafety/safetyEquipmentItems'
import { UncategorisedData } from '@src/lib/categories'
import { useFocusEffect } from '@react-navigation/native'

export type ReportingSafetyItemById = UncategorisedData & {
  [id: string]: {
    name: string
    isCritical?: boolean
  }
}

export const useReportingSafetyItemsByIds = (isActive: boolean, selectedVesselIds: string[]) => {
  const [reportingSafetyItemById, setReportingSafetyItemById] = useState<ReportingSafetyItemById>()

  useFocusEffect(
    useCallback(() => {
      setReportingSafetyItemById(undefined)
      if (
        isActive && // (required for any of the graphs)
        selectedVesselIds?.length
      ) {
        console.log('TTT - Trigger call - Safety', '1')

        return setupArrayQueryListener(
          'vesselSafetyItems', // what
          collection(firestore, 'vesselSafetyItems'),
          [], // baseConstraints
          'vesselId',
          'in',
          selectedVesselIds,
          [], // orderBy
          (docs: QueryDocumentSnapshot<DocumentData>[], isCombined: boolean) => {
            // processDocs
            const byId = {} as {
              [id: string]: {
                name: string
                isCritical?: boolean
              }
            }
            docs.forEach(doc => {
              const item = {
                id: doc.id,
                ...doc.data(),
              } as SafetyEquipmentItem
              byId[item.id] = {
                name: item.name,
              }
              if (item.isCritical) {
                byId[doc.id].isCritical = true
              }
            })
            setReportingSafetyItemById(byId)
          }
        )
      }
    }, [isActive, selectedVesselIds])
  )

  return reportingSafetyItemById
}
