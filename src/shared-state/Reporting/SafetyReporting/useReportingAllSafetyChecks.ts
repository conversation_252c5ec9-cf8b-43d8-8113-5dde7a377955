import { useFocusEffect } from '@react-navigation/native'
import { useCallback, useState } from 'react'
import { SafetyCheckItem } from '@src/shared-state/VesselSafety/safetyCheckItems'
import { useReportingFaultedSafetyChecks } from '@src/shared-state/Reporting/SafetyReporting/useReportingFaultedSafetyChecks'
import { useReportingDueSafetyChecks } from '@src/shared-state/Reporting/SafetyReporting/useReportingDueSafetyChecks'
import { PostFetchAction } from '@src/app/(home)/(wrapper)/reporting/_layout'
import { DateTime } from 'luxon'
import {
  useSafetyCheckReportSettings,
  useSafetyReportSettings,
} from '@src/app/(home)/(wrapper)/reporting/reportingSafety/_layout'

export const useReportingAllSafetyChecks = (selectedVesselIds: string[], postFetchAction?: PostFetchAction) => {
  const { setData, setFetchedDatetime } = useSafetyCheckReportSettings()
  const {
    filters: { selectedSafetyCheckStatuses },
  } = useSafetyReportSettings()

  const [allSafetyChecks, setAllSafetyChecks] = useState<SafetyCheckItem[]>()

  const faultedSafetyChecks = useReportingFaultedSafetyChecks(true, selectedVesselIds, selectedSafetyCheckStatuses)
  const dueSafetyChecks = useReportingDueSafetyChecks(true, selectedVesselIds, selectedSafetyCheckStatuses)

  useFocusEffect(
    useCallback(() => {
      if (faultedSafetyChecks && dueSafetyChecks) {
        const combinedSafetyChecks = [...faultedSafetyChecks, ...dueSafetyChecks]
        setAllSafetyChecks(combinedSafetyChecks)
      }
    }, [dueSafetyChecks, faultedSafetyChecks])
  )

  useFocusEffect(
    useCallback(() => {
      if (postFetchAction === PostFetchAction.Save && allSafetyChecks) {
        setData(allSafetyChecks)
        setFetchedDatetime(DateTime.now().toMillis())
      }
    }, [postFetchAction, allSafetyChecks, setData, setFetchedDatetime])
  )

  return allSafetyChecks
}
