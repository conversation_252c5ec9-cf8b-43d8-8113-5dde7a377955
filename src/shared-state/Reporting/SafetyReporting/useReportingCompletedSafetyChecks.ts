import { DocumentData, QueryDocumentSnapshot, collection, where } from '@src/lib/firebase/services/firestore.service'
import { firestore, setupArrayQueryListener } from '@src/lib/firebase'
import { useCallback, useState } from 'react'
import { SafetyCheckCompleted } from '../../VesselSafety/useCompletedSafetyCheckItems'
import { ReportingSafetyItemById } from './useReportingSafetyItemsById'
import { useFocusEffect } from '@react-navigation/native'

export const useReportingCompletedSafetyChecks = (
  isActive: boolean,
  selectedVesselIds: string[],
  completedSafetyChecks:
    | {
        [id: string]: number
      }
    | undefined,
  vesselSafetyItemsById: ReportingSafetyItemById | undefined
) => {
  const [reportingCompletedSafetyChecks, setReportingCompletedSafetyChecks] = useState<SafetyCheckCompleted[]>()

  useFocusEffect(
    useCallback(() => {
      setReportingCompletedSafetyChecks(undefined)

      if (isActive && selectedVesselIds?.length && completedSafetyChecks && vesselSafetyItemsById) {
        // let isCriticalityOk = (item: SafetyCheckCompleted) => true
        // if (safetyCriticality === 'critical') {
        //   isCriticalityOk = (item: SafetyCheckCompleted) => {
        //     return vesselSafetyItemsById[item.itemId]?.isCritical ? true : false
        //   }
        // } else if (safetyCriticality === 'nonCritical') {
        //   isCriticalityOk = (item: SafetyCheckCompleted) => {
        //     return !vesselSafetyItemsById[item.itemId]?.isCritical
        //   }
        // }
        console.log('TTT - Trigger call - Safety', '6')

        return setupArrayQueryListener(
          'safetyCheckItems', // what
          collection(firestore, 'safetyCheckItems'),
          [where('state', '==', 'active')],
          'vesselId',
          'in',
          selectedVesselIds,
          [], // orderBy
          (docs: QueryDocumentSnapshot<DocumentData>[], isCombined: boolean) => {
            // processDocs
            const list: SafetyCheckCompleted[] = []
            docs.forEach(doc => {
              if (completedSafetyChecks[doc.id]) {
                const data = {
                  id: doc.id,
                  ...doc.data(),
                } as SafetyCheckCompleted

                list.push({
                  id: doc.id,
                  name: vesselSafetyItemsById[data.itemId]?.name,
                  files: data.files ?? [],
                  interval: data.interval,
                  isCritical: vesselSafetyItemsById[data.itemId]?.isCritical,
                  vesselId: data.vesselId,
                  timesCompleted: completedSafetyChecks[doc.id],
                  itemId: data.itemId,
                } as SafetyCheckCompleted)
              }
            })

            // TODO: Must be done on the Tables
            // // Sort alphabetically
            // list.sort((a, b) => {
            //   if (a.name && b.name) {
            //     return a.name.localeCompare(b.name)
            //   } else {
            //     return 0
            //   }
            // })

            setReportingCompletedSafetyChecks(list)
          }
        )
      }
    }, [isActive, selectedVesselIds, completedSafetyChecks, vesselSafetyItemsById])
  )

  return reportingCompletedSafetyChecks
}
