import { useReportingCompletedSafetyChecksMap } from '@src/shared-state/Reporting/SafetyReporting/useReportingCompletedSafetyChecksMap'
import { useReportingCompletedSafetyChecks } from '@src/shared-state/Reporting/SafetyReporting/useReportingCompletedSafetyChecks'
import { ReportingSafetyItemById } from '@src/shared-state/Reporting/SafetyReporting/useReportingSafetyItemsById'
import { PostFetchAction } from '@src/app/(home)/(wrapper)/reporting/_layout'
import { useSafetyCheckCompletedReportSettings } from '@src/app/(home)/(wrapper)/reporting/reportingSafety/_layout'
import { useFocusEffect } from '@react-navigation/native'
import { useCallback } from 'react'
import { DateTime } from 'luxon'

export const useReportingAllCompletedSafetyChecks = (
  isActive: boolean,
  selectedVesselIds: string[],
  vesselSafetyItemsById: ReportingSafetyItemById | undefined,
  postFetchAction?: PostFetchAction
) => {
  const { setData, setFetchedDatetime } = useSafetyCheckCompletedReportSettings()
  const completedSafetyChecksMap = useReportingCompletedSafetyChecksMap(isActive, selectedVesselIds)
  const completedSafetyChecks = useReportingCompletedSafetyChecks(
    isActive,
    selectedVesselIds,
    completedSafetyChecksMap,
    vesselSafetyItemsById
    // safetyCriticality
  )

  useFocusEffect(
    useCallback(() => {
      if (postFetchAction === PostFetchAction.Save && completedSafetyChecks) {
        setData(completedSafetyChecks)
        setFetchedDatetime(DateTime.now().toMillis())
      }
    }, [postFetchAction, completedSafetyChecks, setData, setFetchedDatetime])
  )

  return completedSafetyChecks
}
