import {
  DocumentData,
  QueryDocumentSnapshot,
  QueryOrderByConstraint,
  collection,
  orderBy,
  where,
} from '@src/lib/firebase/services/firestore.service'
import { firestore, setupArrayQueryListener } from '@src/lib/firebase'
import { useCallback, useState } from 'react'
import { SafetyCheckItem } from '../../VesselSafety/safetyCheckItems'
import { makeSafetyItems, sortSafetyItems } from './reportingSafetyFunctions'
import { useFocusEffect } from '@react-navigation/native'

export const useReportingFaultedSafetyChecks = (
  isActive: boolean,
  selectedVesselIds: string[],
  selectedSafetyCheckStatuses: string[]
) => {
  const [reportingFaultedSafetyChecks, setReportingFaultedSafetyChecks] = useState<SafetyCheckItem[]>()

  useFocusEffect(
    useCallback(() => {
      setReportingFaultedSafetyChecks(undefined)
      if (isActive && selectedVesselIds?.length && selectedSafetyCheckStatuses.includes('faulted')) {
        console.log('TTT - Trigger call - Safety', '3')

        return setupArrayQueryListener(
          'safetyCheckItems', // what
          collection(firestore, 'safetyCheckItems'),
          [where('state', '==', 'active'), where('hasFault', '==', true)],
          'vesselId',
          'in',
          selectedVesselIds,
          [orderBy('dateDue', 'asc') as QueryOrderByConstraint],
          (docs: QueryDocumentSnapshot<DocumentData>[], isCombined: boolean) => {
            // processDocs
            const all = makeSafetyItems(docs) as SafetyCheckItem[]
            if (isCombined) {
              sortSafetyItems(all)
            }
            setReportingFaultedSafetyChecks(all)
          }
        )
      }
    }, [isActive, selectedVesselIds, selectedSafetyCheckStatuses])
  )

  return reportingFaultedSafetyChecks
}
