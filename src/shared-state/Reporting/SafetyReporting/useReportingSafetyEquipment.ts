import {
  DocumentData,
  QueryDocumentSnapshot,
  QueryOrderByConstraint,
  collection,
  orderBy,
  where,
} from '@src/lib/firebase/services/firestore.service'
import { firestore, setupArrayQueryListener } from '@src/lib/firebase'
import { useCallback, useState } from 'react'
import { getDayOffset, warnDays } from '@src/lib/datesAndTime'
import { sharedState } from '@src/shared-state/shared-state'
import { makeSafetyItems, sortSafetyItems } from './reportingSafetyFunctions'
import { ReportingSafetyItemById } from './useReportingSafetyItemsById'
import { SafetyEquipmentItem } from '../../VesselSafety/safetyEquipmentItems'
import { useSafetyEquipmentReportSettings } from '@src/app/(home)/(wrapper)/reporting/reportingSafety/_layout'
import { DateTime } from 'luxon'
import { useFocusEffect } from '@react-navigation/native'
import { PostFetchAction } from '@src/app/(home)/(wrapper)/reporting/_layout'

export const useReportingSafetyEquipments = (
  isActive: boolean,
  selectedVesselIds: string[],
  // TODO: Check if we need to filter everytime
  // includeOverdue: boolean,
  // includeUpcoming: boolean,
  // TODO: Make this into an enum
  postFetchAction?: PostFetchAction
  // safetyCriticality: string
) => {
  const [reportingSafetyEquipment, setReportingSafetyEquipment] = useState<SafetyEquipmentItem[]>()

  useFocusEffect(
    useCallback(() => {
      // console.log('TTT - Trigger call - INSIDE', )
      // if (selectedSafetyEquipmentStatuses.length === 0 || selectedVesselIds.length === 0) {
      //   // Nothing to load
      //   setReportingSafetyEquipment([])
      //   return
      // }
      // setReportingSafetyEquipment(undefined)
      if (!isActive) {
        return
      }

      if (isActive && selectedVesselIds?.length) {
        // const includeOverdue = selectedSafetyEquipmentStatuses.includes('overdue')
        // const includeUpcoming = selectedSafetyEquipmentStatuses.includes('upcoming')
        // if (!includeOverdue && !includeUpcoming) {
        //   return
        // }
        //
        const baseConstraints = [where('state', '==', 'active')]
        // if (includeOverdue && includeUpcoming) {
        //   baseConstraints.push(where('dateDue', '<', getDayOffset(warnDays.safetyEquipmentExpiries[0])))
        // } else if (includeOverdue) {
        //   // Include overdue only
        //   baseConstraints.push(where('dateDue', '<', today))
        // } else {
        //   // Include upcoming only
        //   baseConstraints.push(
        //     where('dateDue', '<', getDayOffset(warnDays.safetyEquipmentExpiries[0])),
        //     where('dateDue', '>=', today)
        //   )
        // }

        console.log('TTT - Trigger call - Safety', '4')
        return setupArrayQueryListener(
          'safetyEquipmentItems', // what
          collection(firestore, 'safetyEquipmentItems'),
          baseConstraints,
          'vesselId',
          'in',
          selectedVesselIds,
          [orderBy('dateDue', 'asc') as QueryOrderByConstraint],
          (docs: QueryDocumentSnapshot<DocumentData>[], isCombined: boolean) => {
            // processDocs
            const all = makeSafetyItems(docs) as SafetyEquipmentItem[]

            // // Filter by criticality
            // if (vesselSafetyItemsById) {
            //   if (safetyCriticality === 'critical') {
            //     all = all.filter(item => vesselSafetyItemsById[item.itemId]?.isCritical)
            //   } else if (safetyCriticality === 'nonCritical') {
            //     all = all.filter(item => !vesselSafetyItemsById[item.itemId]?.isCritical)
            //   }
            // }

            // if (isCombined) {
            //   sortSafetyItems(all)
            // }
            setReportingSafetyEquipment(all)
          }
        )
      }
    }, [
      isActive,
      selectedVesselIds,
      // TODO: Check if we need to filter everytime
      // includeOverdue,
      // includeUpcoming,
      // safetyCriticality,
    ])
  )

  const { setData, setFetchedDatetime } = useSafetyEquipmentReportSettings()

  useFocusEffect(
    useCallback(() => {
      if (postFetchAction === PostFetchAction.Save && reportingSafetyEquipment) {
        setData(reportingSafetyEquipment)
        setFetchedDatetime(DateTime.now().toMillis())
      }
    }, [postFetchAction, reportingSafetyEquipment, setData, setFetchedDatetime])
  )

  return reportingSafetyEquipment
}
