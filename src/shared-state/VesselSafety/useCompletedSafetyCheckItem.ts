import {
  doc,
  docExists,
  DocumentData,
  DocumentSnapshot,
  onSnapshot,
} from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { useState, useEffect } from 'react'
import { SafetyCheckCompleted } from './useCompletedSafetyCheckItems'
import { registerFiles } from '@src/shared-state/FileSyncSystem/filesToCache'

export const useCompletedSafetyCheckItem = (
  completedSafetyCheckId: string | undefined
): SafetyCheckCompleted | null | undefined => {
  const [completedSafetyCheck, setCompletedSafetyCheck] = useState<SafetyCheckCompleted | null | undefined>(undefined)

  useEffect(() => {
    setCompletedSafetyCheck(undefined)

    if (!completedSafetyCheckId) {
      setCompletedSafetyCheck(null)
      return
    }

    return onSnapshot(
      doc(firestore, 'safetyCheckCompleted', completedSafetyCheckId),
      (doc: DocumentSnapshot<DocumentData>) => {
        if (docExists(doc)) {
          const item = {
            id: doc.id,
            ...doc.data(),
          } as SafetyCheckCompleted
          registerFiles(item.files, 'safetyCheckCompleted', item)
          setCompletedSafetyCheck(item)
        } else {
          setCompletedSafetyCheck(null)
        }
      },
      error => {
        console.error(`Failed to fetch completed safety check id=${completedSafetyCheckId}`, error)
        setCompletedSafetyCheck(null)
      }
    )
  }, [completedSafetyCheckId])

  return completedSafetyCheck
}
