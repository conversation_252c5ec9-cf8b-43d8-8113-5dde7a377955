import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { CreateableDocument, SharedStateConfig, UpdateableDocument, sharedState } from '@src/shared-state/shared-state'
import { canView } from '@src/shared-state/Core/userPermissions'
import { getDayOffset, warnDays } from '@src/lib/datesAndTime'
import { registerFiles } from '@src/shared-state/FileSyncSystem/filesToCache'

//
// Loads safety check items
//

export interface SafetyCheckItem extends CreateableDocument, UpdateableDocument {
  deletedBy?: string
  description?: string
  faultJobId?: string
  files: string[]
  hasFault?: boolean
  interval: string
  isCritical?: boolean
  itemId: string
  location?: string
  locationId?: string
  name?: string
  state: string
  vesselId: string
  assignedTo?: string[]
  whenDeleted?: number
  dateDue: string
  whenFaulted?: number
  whenLastChecked: number
  categoryId: string
  estimatedTime?: number
}

export type SafetyCheckItemsData = {
  all: SafetyCheckItem[]
  byCategoryId: {
    [categoryId: string]: SafetyCheckItem[]
  }
  byId: {
    [id: string]: SafetyCheckItem
  }
  prioritised: SafetyCheckItem[]
  count: number
}

export const safetyCheckItemsConfig: SharedStateConfig<SafetyCheckItemsData> = {
  isAlwaysActive: false,
  dependencies: ['vesselId', 'vesselSafetyItems', 'todayMillis', 'userPermissions'],
  countLiveDocs: () => sharedState.safetyCheckItems.current?.count ?? 0,
  run: (done, set, clear) => {
    clear()
    const vesselId = sharedState.vesselId.current
    const vesselSafetyItems = sharedState.vesselSafetyItems.current
    if (vesselId && vesselSafetyItems?.ids && canView('safetyEquipmentChecks')) {
      return onSnapshot(
        query(
          collection(firestore, 'safetyCheckItems'),
          where('vesselId', '==', vesselId),
          where('state', 'in', ['active', 'completed']),
          orderBy('dateDue', 'asc')
        ),
        snap => {
          done()
          const items = snap.docs.map(doc => {
            return {
              id: doc.id,
              ...doc.data(),
            } as SafetyCheckItem
          })
          const byId = {} as {
            [id: string]: SafetyCheckItem
          }
          const byCategoryId = {} as {
            [categoryId: string]: SafetyCheckItem[]
          }
          const byItemId = {} as {
            [id: string]: SafetyCheckItem[]
          }
          items.forEach((item: SafetyCheckItem) => {
            registerFiles(item.files, 'safetyCheckItems', item)
            byId[item.id] = item
            if (byItemId[item.itemId] === undefined && item.state === 'active') {
              byItemId[item.itemId] = []
            }
            if (byCategoryId[item.categoryId] === undefined && item.state === 'active') {
              byCategoryId[item.categoryId] = []
            }
            byItemId[item.itemId].push(item)
          })

          let prioritised = [...items].filter(item => item.state === 'active')
          prioritised.sort((a, b) => {
            return (
              // a.dateDue -
              // (a.hasFault ? 1000000000000 : 0) -
              // (b.dateDue - (b.hasFault ? 1000000000000 : 0))
              // If faulted, we'll prefix with 0 so they sort to the top
              `${a.hasFault ? '0' : ''}${a.dateDue}`.localeCompare(`${b.hasFault ? '0' : ''}${b.dateDue}`)
            )
          })
          const maxDateDue = getDayOffset(warnDays.safetyEquipmentChecks[warnDays.safetyEquipmentChecks.length - 1])
          for (let i = 0; i < prioritised.length; i++) {
            if (prioritised[i].dateDue > maxDateDue && !prioritised[i].hasFault) {
              prioritised = prioritised.slice(0, i)
              break
            }
          }
          const all = [] as SafetyCheckItem[]
          vesselSafetyItems.ids.forEach((id: string) => {
            if (byItemId[id]) {
              all.push(...byItemId[id])
            }
          })
          // After populating byCategoryId, sort each category's items
          Object.keys(byCategoryId).forEach(categoryId => {
            byCategoryId[categoryId].sort((a, b) => {
              const nameA = vesselSafetyItems.byId[a.itemId]?.name?.toLowerCase() || '' // Fallback to empty string if undefined
              const nameB = vesselSafetyItems.byId[b.itemId]?.name?.toLowerCase() || ''
              return nameA.localeCompare(nameB)
            })
          })
          set({
            all: all,
            byCategoryId,
            prioritised: prioritised,
            byId,
            count: items.length,
          })
        },
        error => {
          done()
          // This should be very rare
          console.log(`Failed to access safety check items for vessel ${vesselId}`, error)
        }
      )
    } else {
      done()
    }
  },
}
