import { SharedStateConfig, sharedState } from '@src/shared-state/shared-state'
import { CategoriesData, onCategoriesSnapshot } from '@src/lib/categories'

/**
 * Loads vesselSafetyItems
 */
export const vesselSafetyItemsConfig: SharedStateConfig<CategoriesData> = {
  isAlwaysActive: false,
  dependencies: ['vesselId'],
  countLiveDocs: () => sharedState.vesselSafetyItems.current?.ids.length ?? 0,
  run: (done, set, clear) => {
    clear()
    const vesselId = sharedState.vesselId.current
    if (vesselId) {
      return onCategoriesSnapshot(
        'vesselSafetyItems',
        'vesselId',
        vesselId,
        data => {
          // onLoaded
          done()
          set(data)
        },
        error => {
          // onError
          done()
          clear()
          console.log(`Error getting vesselSafetyItems`, error)
        }
      )
    } else {
      done()
    }
  },
}
