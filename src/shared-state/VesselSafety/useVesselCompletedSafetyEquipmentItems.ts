import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { useState, useEffect } from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { SafetyEquipmentTaskCompleted } from './useCompletedSafetyEquipmentItems'
import { addInterval, DateRange, makeDateTime } from '@src/lib/datesAndTime'

export type useSafetyEquipmentItemsData = Record<string, SafetyEquipmentTaskCompleted[]> | undefined

export const useVesselCompletedSafetyEquipmentItems = (
  dateRange: DateRange | undefined
): useSafetyEquipmentItemsData => {
  const [completedTasksByEquipmentId, setCompletedTasksByEquipmentId] =
    useState<Record<string, SafetyEquipmentTaskCompleted[]>>()
  const vesselId = sharedState.vesselId.use()

  useEffect(() => {
    setCompletedTasksByEquipmentId(undefined)

    if (vesselId && dateRange) {
      const _completedTasksBySafetyEquipmentId: Record<string, SafetyEquipmentTaskCompleted[]> = {}

      return onSnapshot(
        query(
          collection(firestore, 'safetyEquipmentTaskCompleted'),
          where('vesselId', '==', vesselId),
          where('state', '==', 'active'),
          where('whenCompleted', '>=', makeDateTime(dateRange.from).toMillis()),
          where('whenCompleted', '<=', addInterval(dateRange.to, '1d').toMillis()),
          orderBy('whenCompleted', 'desc')
        ),
        snap => {
          snap.docs.forEach(doc => {
            const item = {
              id: doc.id,
              ...doc.data(),
            } as SafetyEquipmentTaskCompleted
            if (!_completedTasksBySafetyEquipmentId[item.safetyEquipmentId]) {
              _completedTasksBySafetyEquipmentId[item.safetyEquipmentId] = []
            }
            _completedTasksBySafetyEquipmentId[item.safetyEquipmentId].push(item)
          })
          setCompletedTasksByEquipmentId(_completedTasksBySafetyEquipmentId)
        },
        error => {
          console.log(`Failed to fetch completed safety checks for vesselId=${vesselId}`, error.message, error, {
            vesselId,
            dateRange,
          })
        }
      )
    }
  }, [vesselId, dateRange])

  return completedTasksByEquipmentId
}
