import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { useState, useEffect } from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { SafetyCheckCompleted } from './useCompletedSafetyCheckItems'
import { addInterval, DateRange, makeDateTime } from '@src/lib/datesAndTime'

export type useSafetyCheckItemsData = Record<string, SafetyCheckCompleted[]> | undefined

export const useVesselCompletedSafetyCheckItems = (dateRange: DateRange | undefined): useSafetyCheckItemsData => {
  const [completedSafetyChecksById, setCompletedSafetyChecksById] = useState<Record<string, SafetyCheckCompleted[]>>()
  const vesselId = sharedState.vesselId.use()

  useEffect(() => {
    setCompletedSafetyChecksById(undefined)

    if (vesselId && dateRange) {
      const _completedSafetyChecksBySafetyCheckId: Record<string, SafetyCheckCompleted[]> = {}

      return onSnapshot(
        query(
          collection(firestore, 'safetyCheckCompleted'),
          where('vesselId', '==', vesselId),
          where('state', '==', 'active'),
          where('whenCompleted', '>=', makeDateTime(dateRange.from).toMillis()),
          where('whenCompleted', '<=', addInterval(dateRange.to, '1d').toMillis()),
          orderBy('whenCompleted', 'desc')
        ),
        snap => {
          snap.docs.forEach(doc => {
            const item = {
              id: doc.id,
              ...doc.data(),
            } as SafetyCheckCompleted
            if (!_completedSafetyChecksBySafetyCheckId[item.safetyCheckId]) {
              _completedSafetyChecksBySafetyCheckId[item.safetyCheckId] = []
            }
            _completedSafetyChecksBySafetyCheckId[item.safetyCheckId].push(item)
          })
          setCompletedSafetyChecksById(_completedSafetyChecksBySafetyCheckId)
        },
        error => {
          console.log(`Failed to fetch completed safety checks for vesselId=${vesselId}`, error.message, error, {
            vesselId,
            dateRange,
          })
        }
      )
    }
  }, [vesselId, dateRange])

  return completedSafetyChecksById
}
