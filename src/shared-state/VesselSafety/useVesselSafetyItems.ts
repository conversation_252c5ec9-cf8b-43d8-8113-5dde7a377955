import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { useState, useEffect } from 'react'
import { SafetyCheckItem } from './safetyCheckItems'

export type SafetyCheckItemsData = {
  all: SafetyCheckItem[]
  byId: {
    [id: string]: SafetyCheckItem
  }
}

export const useVesselSafetyItems = (shouldLoad?: boolean, vesselId?: string): SafetyCheckItemsData | undefined => {
  const [safetyChecks, setSafetyChecks] = useState<SafetyCheckItemsData>()

  useEffect(() => {
    setSafetyChecks(undefined)
    if (shouldLoad && vesselId) {
      return onSnapshot(
        query(collection(firestore, 'vesselSafetyItems'), where('vesselId', '==', vesselId), orderBy('name', 'asc')),
        snap => {
          const items: SafetyCheckItem[] = []
          const byId: {
            [id: string]: SafetyCheckItem
          } = {}
          snap.docs.forEach(doc => {
            const item = {
              id: doc.id,
              ...doc.data(),
            } as SafetyCheckItem
            items.push(item)
            byId[item.id] = item
          })
          setSafetyChecks({
            all: items,
            byId,
          })
        },
        error => {
          console.log(`Failed to access SafetyCheckItems for vesselId=${vesselId}`, error.message, error, {
            vesselId,
          })
        }
      )
    }
  }, [shouldLoad, vesselId])

  return safetyChecks
}
