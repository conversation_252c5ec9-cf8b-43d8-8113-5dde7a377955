import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { useEffect, useState } from 'react'
import { Drill } from './drills'

export const useVesselDrills = (isActive: boolean, selectedVesselId?: string) => {
  const [drills, setDrills] = useState<Drill[]>()

  useEffect(() => {
    setDrills(undefined)
    if (isActive && selectedVesselId) {
      return onSnapshot(
        query(
          collection(firestore, 'drills'),
          where('vesselId', '==', selectedVesselId),
          where('state', '==', 'active'),
          orderBy('name', 'asc')
        ),
        snap => {
          const all = [] as Drill[]

          snap.docs.forEach(doc => {
            const drill = {
              id: doc.id,
              ...doc.data(),
            } as Drill
            all.push(drill)
          })

          setDrills(all)
        },
        error => {
          // This should be very rare
          console.log(`Failed to useVeselDrills for ${selectedVesselId}`, error)
        }
      )
    }
  }, [isActive, selectedVesselId])

  return drills
}
