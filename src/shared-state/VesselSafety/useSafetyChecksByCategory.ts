import { collection, onSnapshot, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { useState, useEffect } from 'react'
import { SafetyCheckItem } from './safetyCheckItems'
import { formatInterval } from '@src/lib/util'
import { useSafetyCheckCategoriesForVessel } from './useSafetyCheckCategoriesForVessel'
import { useVesselSafetyItems } from './useVesselSafetyItems'

export type SafetyChecksData = {
  categories: {
    id: string
    name: string
  }[] // Sorted by category name
  byCategoryId: {
    [categoryId: string]: SafetyCheckItem[] // Sorted by name
  }
}

export const useSafetyChecksByCategory = (isActive?: boolean, vesselId?: string): SafetyChecksData | undefined => {
  const [safetyChecks, setSafetyChecks] = useState<SafetyChecksData>()
  const vesselSafetyItems = useVesselSafetyItems(isActive, vesselId)
  const safetyCheckCategories = useSafetyCheckCategoriesForVessel(isActive, vesselId)

  useEffect(() => {
    setSafetyChecks(undefined)
    if (isActive && vesselId && vesselSafetyItems && safetyCheckCategories) {
      return onSnapshot(
        query(
          collection(firestore, 'safetyCheckItems'),
          where('vesselId', '==', vesselId),
          where('state', 'in', ['active', 'completed'])
        ),
        snap => {
          const categories = [] as {
            id: string
            name: string
          }[]
          const byCategoryId: {
            [categoryId: string]: SafetyCheckItem[]
          } = {}

          snap.docs.forEach(doc => {
            const itemData = doc.data()
            const safetyItem = vesselSafetyItems.byId[itemData.itemId]
            const name = safetyItem ? `${safetyItem.name} (${formatInterval(itemData.interval)})` : ''
            const item = {
              id: doc.id,
              ...itemData,
              name: name,
              location: safetyItem?.location,
            } as SafetyCheckItem
            if (!byCategoryId[item.categoryId]) {
              byCategoryId[item.categoryId] = []
              categories.push({
                id: item.categoryId,
                name: safetyCheckCategories.byId[item.categoryId].name,
              })
            }
            byCategoryId[item.categoryId].push(item)
          })

          // Sort categories
          categories.sort((a, b) => {
            return a.name.localeCompare(b.name)
          })

          setSafetyChecks({
            byCategoryId,
            categories,
          })
        },
        error => {
          console.log(`Failed to access SafetyChecks for vesselId=${vesselId}`, error.message, error, {
            vesselId,
          })
        }
      )
    }
  }, [isActive, vesselId, vesselSafetyItems, safetyCheckCategories])

  return safetyChecks
}
