import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { useState, useEffect } from 'react'
import { CategoriesData, Category } from '@src/lib/categories'

export const useVesselLocationsForVessels = (
  shouldLoad?: boolean,
  vesselIds?: string[]
): CategoriesData | undefined => {
  const [vesselLocations, setVesselLocations] = useState<CategoriesData>()

  useEffect(() => {
    setVesselLocations(undefined)

    if (shouldLoad && vesselIds?.length) {
      return onSnapshot(
        query(collection(firestore, 'vesselLocations'), where('vesselId', 'in', vesselIds), orderBy('name', 'asc')),
        snap => {
          const items: Category[] = []
          const byId: {
            [id: string]: Category
          } = {}
          snap.docs.forEach(doc => {
            const item = {
              id: doc.id,
              ...doc.data(),
            } as Category
            items.push(item)
            byId[item.id] = item
          })
          setVesselLocations({
            ids: items.map(item => item.id),
            byId,
          })
        },
        error => {
          console.log(`Failed to access VesselLocations for vesselIds=${vesselIds}`, error.message, error, {
            vesselIds,
          })
        }
      )
    }
  }, [shouldLoad, vesselIds])

  return vesselLocations
}
