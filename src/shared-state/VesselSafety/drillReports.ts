import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { CreateableDocument, SharedStateConfig, UpdateableDocument, sharedState } from '@src/shared-state/shared-state'
import { canView } from '@src/shared-state/Core/userPermissions'
import { makeDateTime } from '@src/lib/datesAndTime'
import { UserType } from '@src/shared-state/Core/user'
import { renderFullName } from '@src/shared-state/Core/users'
import { Drill } from './drills'
import { registerFiles, registerSignature } from '@src/shared-state/FileSyncSystem/filesToCache'

//
// Load drill reports
//

export interface DrillReport extends CreateableDocument, UpdateableDocument {
  crewInvolvedIds: string[]
  drillIds: string[]
  /* @deprecated  - use `drillIds` instead. By saving crew data against the drill itself, we now only need the ID */
  drills: Drill[]
  equipment?: string
  files: string[]
  furtherTraining?: string
  location?: string
  modification?: string
  scenario?: string
  signature: string
  state: string
  vesselId: string
  dateCompleted: string
  dateDue?: string
  lastDone?: number
  daysOverdue?: number
  history?: string
}

export type DrillReportsData = {
  users: UserType[]
  byUserId: {
    [userId: string]: DrillReport[]
  }
  byDrillAndUser: {
    [compositeId: string]: {
      dateDue: string
      dateDueDiff: number
      report: DrillReport
    }
  }
  allReports: DrillReport[]
  byDrillId: {
    [drillId: string]: DrillReport[]
  }
  byId: {
    [drillId: string]: DrillReport
  }
}

export const drillReportsConfig: SharedStateConfig<DrillReportsData> = {
  isAlwaysActive: false,
  dependencies: ['vesselId', 'users', 'vesselDrills', 'todayMillis', 'userPermissions'],
  countLiveDocs: () => sharedState.drillReports.current?.allReports.length ?? 0,
  run: (done, set, clear) => {
    clear()
    const vesselId = sharedState.vesselId.current
    const users = sharedState.users.current
    const vesselDrills = sharedState.vesselDrills.current
    const today = sharedState.today.current
    if (vesselId && users && vesselDrills && canView('drills')) {
      return onSnapshot(
        query(
          collection(firestore, 'drillReports'),
          where('vesselId', '==', vesselId),
          where('state', '==', 'active'),
          orderBy('dateCompleted', 'desc')
        ),
        snap => {
          done()
          const reports = snap.docs.map(doc => {
            return {
              id: doc.id,
              ...doc.data(),
            } as DrillReport
          })

          // We need: byTaskAndUser --> will be set to the latest report
          // We need: users --> users that have completed at least one drill, sorted alphabetically
          const byDrillAndUser = {} as {
            [compositeId: string]: {
              dateDue: string
              dateDueDiff: number
              report: DrillReport
            }
          }
          const byUserId = {} as {
            [userId: string]: DrillReport[]
          }
          const drillUsers = [] as UserType[]

          reports.forEach((report: DrillReport) => {
            // report.dateDueDiff = Math.round(
            //     makeDateTime(report.dateDue).diff(today, ['days']).days
            // );

            report.crewInvolvedIds?.forEach((userId: string) => {
              if (
                users?.byId[userId]?.state === 'active' &&
                users.byId[userId].vesselIds?.indexOf(report.vesselId) !== -1
              ) {
                if (!byUserId[userId]) {
                  byUserId[userId] = []
                  drillUsers.push(users.byId[userId])
                }
                byUserId[userId].push(report)

                report.drills?.forEach((drill: Drill) => {
                  if (vesselDrills.byId[drill.id]?.state === 'active') {
                    const compositeId = `${drill.id}${userId}`
                    if (byDrillAndUser[compositeId] === undefined) {
                      byDrillAndUser[compositeId] = {
                        dateCompleted: drill.dateDue!,
                        dateDueDiff: Math.round(makeDateTime(drill.dateDue).diff(makeDateTime(today), ['days']).days),
                        report,
                      }
                    } else if ((drill.dateDue || 0) > byDrillAndUser[compositeId].dateDue) {
                      byDrillAndUser[compositeId].dateDue = drill.dateDue!
                      byDrillAndUser[compositeId].dateDueDiff = Math.round(
                        makeDateTime(drill.dateDue).diff(makeDateTime(today), ['days']).days
                      )
                    }
                  }
                })
              }
            })
          })
          drillUsers.sort((a, b) => {
            return renderFullName(a).localeCompare(renderFullName(b))
          })

          const allReports = [] as DrillReport[]
          const byDrillId = {} as {
            [drillId: string]: DrillReport[]
          }
          const byId = {} as {
            [drillId: string]: DrillReport
          }

          reports.forEach((report: DrillReport) => {
            registerFiles(report.files, 'drillReports', report)
            registerSignature(report.signature, 'drillReports', report)
            byId[report.id] = report
            allReports.push(report)

            report.drills?.forEach((drill: Drill) => {
              if (byDrillId[drill.id] === undefined) {
                byDrillId[drill.id] = []
              }
              byDrillId[drill.id].push(report)
            })
          })

          set({
            users: drillUsers,
            byUserId,
            byDrillAndUser,
            allReports,
            byDrillId,
            byId,
          })
        },
        error => {
          done()
          console.log(`Failed to access drill reports for vessel id=${vesselId}`, error)
        }
      )
    } else {
      done()
    }
  },
}
