import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { useState, useEffect } from 'react'
import { Category } from '@src/lib/categories'

type SafetyCheckCategory = Category & {
  vesselName: string
}

export type SafetyCheckCategoriesData = {
  all: SafetyCheckCategory[]
  byId: {
    [id: string]: SafetyCheckCategory
  }
}

export const useSafetyCheckCategoriesForVessel = (
  shouldLoad?: boolean,
  vesselId?: string
): SafetyCheckCategoriesData | undefined => {
  const [safetyCheckCategories, setSafetyCheckCategories] = useState<SafetyCheckCategoriesData>()
  useEffect(() => {
    setSafetyCheckCategories(undefined)

    if (shouldLoad && vesselId) {
      return onSnapshot(
        query(
          collection(firestore, 'safetyCheckCategories'),
          where('vesselId', '==', vesselId),
          where('state', '==', 'active'),
          orderBy('name', 'asc')
        ),
        snap => {
          const items: SafetyCheckCategory[] = []
          const byId: {
            [id: string]: SafetyCheckCategory
          } = {}
          snap.docs.forEach(doc => {
            const item = {
              id: doc.id,
              ...doc.data(),
            } as SafetyCheckCategory
            items.push(item)
            byId[item.id] = item
          })
          setSafetyCheckCategories({
            all: items,
            byId,
          })
        },
        error => {
          console.log(`Failed to access SafetyCheckCategories for vesselId=${vesselId}`, error.message, error, {
            vesselId,
          })
        }
      )
    }
  }, [shouldLoad, vesselId])

  return safetyCheckCategories
}
