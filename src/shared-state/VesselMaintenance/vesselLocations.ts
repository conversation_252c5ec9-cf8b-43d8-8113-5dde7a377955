import { SharedStateConfig, sharedState } from '@src/shared-state/shared-state'
import { CategoriesData, onCategoriesSnapshot } from '@src/lib/categories'

/**
 * Loads vesselLocations
 */
export const vesselLocationsConfig: SharedStateConfig<CategoriesData> = {
  isAlwaysActive: false,
  dependencies: ['vesselId'],
  countLiveDocs: () => sharedState.vesselLocations.current?.ids.length ?? 0,
  run: (done, set, clear) => {
    clear()
    const vesselId = sharedState.vesselId.current
    if (vesselId) {
      return onCategoriesSnapshot(
        'vesselLocations',
        'vesselId',
        vesselId,
        data => {
          // onLoaded
          done()
          set(data)
        },
        error => {
          // onError
          done()
          clear()
          console.log(`Error getting vesselLocations`, error)
        }
      )
    } else {
      done()
    }
  },
}
