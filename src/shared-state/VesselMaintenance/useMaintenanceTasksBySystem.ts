import { collection, onSnapshot, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { useEffect, useState } from 'react'
import { ScheduledMaintenanceTask } from './maintenanceSchedule'
import { useEquipment } from './useEquipment'
import { useVesselSystems } from './useVesselSystems'

export type ScheduledMaintenanceTasksBySystem = {
  systems: {
    id: string
    name: string
  }[] // Sorted by category name
  bySystemId: {
    [systemId: string]: ScheduledMaintenanceTask[] // Sorted by name
  }
}

export const useMaintenanceTasksBySystem = (isActive: boolean, selectedVesselId?: string) => {
  const vesselSystems = useVesselSystems(isActive ? selectedVesselId : undefined)
  const equipment = useEquipment(isActive ? selectedVesselId : undefined)
  const [maintenanceTasksBySystem, setMaintenanceTasksBySystem] = useState<ScheduledMaintenanceTasksBySystem>()

  useEffect(() => {
    setMaintenanceTasksBySystem(undefined)
    if (isActive && selectedVesselId && vesselSystems && equipment) {
      return onSnapshot(
        query(
          collection(firestore, 'scheduledMaintenanceTasks'),
          where('vesselId', '==', selectedVesselId),
          where('state', '==', 'active')
        ),
        snap => {
          const systems = [] as {
            id: string
            name: string
          }[]
          const bySystemId = {} as {
            [systemId: string]: ScheduledMaintenanceTask[]
          }

          snap.docs.forEach(doc => {
            const task = {
              id: doc.id,
              ...doc.data(),
            } as ScheduledMaintenanceTask
            task.equipment = equipment[task.equipmentId]

            if (task.equipment?.systemId) {
              if (!bySystemId[task.equipment.systemId]) {
                bySystemId[task.equipment.systemId] = []
                systems.push({
                  id: task.equipment.systemId,
                  name: vesselSystems.byId[task.equipment.systemId].name,
                })
              }
              bySystemId[task.equipment.systemId].push(task)
            }
          })

          // Sort systems
          systems.sort((a, b) => {
            return a.name.localeCompare(b.name)
          })

          setMaintenanceTasksBySystem({
            systems,
            bySystemId,
          })
        },
        error => {
          console.log(`Failed to useMaintenanceTasksBySystem for vesselId=${selectedVesselId}`, error.message, error, {
            selectedVesselId,
          })
        }
      )
    }
  }, [equipment, isActive, selectedVesselId, vesselSystems])

  return maintenanceTasksBySystem
}
