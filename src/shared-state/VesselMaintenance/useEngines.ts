import { useState, useEffect } from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { EnginesData, makeEnginesData } from './engines'
import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'

/**
 * Returns engines for the selectedVesselId.
 * If selectedVesselId matches sharedState.vesselId we can just use sharedState.engines
 */
export const useEngines = (selectedVesselId: string | undefined) => {
  const isActive = selectedVesselId ? true : false
  const vesselId = sharedState.vesselId.use(isActive)
  const engines = sharedState.engines.use(isActive)
  const [otherEngines, setOtherEngines] = useState<EnginesData>()

  useEffect(() => {
    setOtherEngines(undefined)
    if (selectedVesselId && (vesselId === undefined || vesselId !== selectedVesselId)) {
      // We need to load engines for a vesselId other than sharedState.vesselId
      return onSnapshot(
        query(
          collection(firestore, 'engines'),
          where('vesselId', '==', selectedVesselId),
          //where('state', '==', 'active')
          orderBy('name', 'asc')
        ),
        snap => {
          setOtherEngines(makeEnginesData(snap))
        },
        error => {
          // This should be very rare
          console.log(`Failed to useEngines for ${selectedVesselId}`, error)
        }
      )
    }
  }, [selectedVesselId, vesselId])

  if (vesselId && vesselId === selectedVesselId) {
    // We have already loaded via sharedState.vesselSystems
    return engines
  }
  return otherEngines
}
