import { useState, useEffect } from 'react'
import { CategoriesData, onCategoriesSnapshot } from '@src/lib/categories'
import { sharedState } from '@src/shared-state/shared-state'

/**
 * Returns vesselLocations for the selectedVesselId.
 * If selectedVesselId matches sharedState.vesselId we can just use sharedState.vesselLocations
 */
export const useVesselLocations = (selectedVesselId: string | undefined) => {
  const isActive = selectedVesselId ? true : false
  const vesselId = sharedState.vesselId.use(isActive)
  const vesselLocations = sharedState.vesselLocations.use(isActive)
  const [otherVesselLocations, setOtherVesselLocations] = useState<CategoriesData>()

  useEffect(() => {
    setOtherVesselLocations(undefined)
    if (selectedVesselId && (vesselId === undefined || vesselId !== selectedVesselId)) {
      // We need to load vesselLocations for a vesselId other than sharedState.vesselId
      return onCategoriesSnapshot(
        'vesselLocations',
        'vesselId',
        selectedVesselId,
        data => {
          // onLoaded
          setOtherVesselLocations(data)
        },
        error => {
          // onError
          console.log(`Error getting vesselLocations for vesselId=${selectedVesselId}`, error)
        }
      )
    }
  }, [selectedVesselId, vesselId])

  if (vesselId && vesselId === selectedVesselId) {
    // We have already loaded via sharedState.vesselLocations
    return vesselLocations
  }
  return otherVesselLocations
}
