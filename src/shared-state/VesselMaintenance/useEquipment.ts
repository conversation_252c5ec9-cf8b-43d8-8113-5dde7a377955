import { useState, useEffect } from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { Equipment } from './equipment'

export type EquipmentById = {
  [id: string]: Equipment
}

/**
 * Returns equipment for the selectedVesselId.
 * If selectedVesselId matches sharedState.vesselId we can just use sharedState.equipment
 */
export const useEquipment = (selectedVesselId: string | undefined) => {
  const isActive = selectedVesselId ? true : false
  const vesselId = sharedState.vesselId.use(isActive)
  const equipment = sharedState.equipment.use(isActive)
  const [otherEquipment, setOtherEquipment] = useState<EquipmentById>()

  useEffect(() => {
    setOtherEquipment(undefined)
    if (selectedVesselId && (vesselId === undefined || vesselId !== selectedVesselId)) {
      // We need to load equipment for a vesselId other than sharedState.vesselId
      return onSnapshot(
        query(
          collection(firestore, 'equipment'),
          where('vesselId', '==', selectedVesselId),
          where('state', 'in', ['active', 'deleted']),
          orderBy('equipment', 'asc')
        ),
        snap => {
          const byId = {} as EquipmentById
          snap.forEach(doc => {
            const equipment = {
              id: doc.id,
              ...doc.data(),
            } as Equipment
            byId[equipment.id] = equipment
          })
          setOtherEquipment(byId)
        },
        error => {
          // This should be very rare
          console.error(`Failed to useEquipment for vessel ${selectedVesselId}`, error)
        }
      )
    }
  }, [selectedVesselId, vesselId])

  if (vesselId && vesselId === selectedVesselId) {
    // We have already loaded via sharedState.vesselSystems
    return equipment?.byId
  }
  return otherEquipment
}
