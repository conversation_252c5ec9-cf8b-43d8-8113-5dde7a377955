import { useState, useEffect } from 'react'
import { collection, query, where, onSnapshot } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { Equipment } from './equipment'

export type LicenseeVesselSystemData = {
  byId: { [id: string]: Equipment }
}

export const useLicenseeEquipment = (vesselIds: string[] | undefined) => {
  const [licenseeEquipmentData, setLicenseeEquipmentData] = useState<LicenseeVesselSystemData>()

  useEffect(() => {
    setLicenseeEquipmentData(undefined)
    if (vesselIds?.length) {
      return onSnapshot(
        query(collection(firestore, 'equipment'), where('vesselId', 'in', vesselIds), where('state', '==', 'active')),
        snapshot => {
          const byId: { [id: string]: Equipment } = {}
          snapshot.forEach(doc => {
            const equipment = {
              id: doc.id,
              ...doc.data(),
            } as Equipment
            byId[equipment.id] = equipment
          })
          setLicenseeEquipmentData({ byId })
        },
        error => {
          console.error('Error fetching license equipment:', error)
        }
      )
    }

    return () => {
      setLicenseeEquipmentData(undefined)
    }
  }, [vesselIds])

  return licenseeEquipmentData
}
