import {
  DocumentData,
  QueryDocumentSnapshot,
  QueryOrderByConstraint,
  collection,
  orderBy,
  where,
} from '@src/lib/firebase/services/firestore.service'
import { firestore, setupArrayQueryListener } from '@src/lib/firebase'
import { CreateableDocument, SharedStateConfig, UpdateableDocument, sharedState } from '@src/shared-state/shared-state'
import { registerFiles } from '@src/shared-state/FileSyncSystem/filesToCache'
import { canView } from '@src/shared-state/Core/userPermissions'

//
// Load dangerous goods
//

export interface DangerousGood extends CreateableDocument, UpdateableDocument {
  class?: string
  deletedBy?: string
  imageFiles: string[]
  isHazardous: boolean
  location?: string
  msdsFiles: string[]
  name: string
  quantity?: string
  state: 'active' | 'deleted'
  vesselIds: string[]
  whenDeleted?: number
  dateExpires?: string
  // Generated at run time - not in original data
  searchText?: string
}

export type DangerousGoodsData = {
  all: DangerousGood[] // Active & alphabetical
  byId: {
    [goodId: string]: DangerousGood
  }
  byVesselId: {
    [vesselId: string]: DangerousGood[] // Active & alphabetical
  }
}

export const dangerousGoodsConfig: SharedStateConfig<DangerousGoodsData> = {
  isAlwaysActive: false,
  dependencies: ['vesselIds', 'userPermissions'],
  countLiveDocs: () => sharedState.dangerousGoods.current?.all.length ?? 0,
  run: (done, set, clear) => {
    clear()
    const vesselIds = sharedState.vesselIds.current
    if (vesselIds && vesselIds.length > 0 && canView('dangerousGoodsRegister')) {
      const searchText = (good: DangerousGood) => {
        let s = good.name
        if (good.class) s += good.class
        if (good.isHazardous) s += 'hazardous'
        if (good.location) s += good.location
        if (good.quantity) s += good.quantity
        return s.toLowerCase()
      }

      return setupArrayQueryListener(
        'dangerous goods', // what
        collection(firestore, 'dangerousGoods'),
        [where('state', '==', 'active')], // baseConstraints
        'vesselIds',
        'array-contains-any',
        vesselIds,
        [orderBy('name', 'asc') as QueryOrderByConstraint],
        (docs: QueryDocumentSnapshot<DocumentData>[], isCombined: boolean) => {
          // processDocs
          done()
          const all = docs.map(doc => {
            return {
              ...doc.data(),
              id: doc.id,
              searchText: searchText(doc.data() as DangerousGood),
            } as DangerousGood
          })

          if (isCombined) {
            // Need to sort by name
            all.sort((a, b) => {
              return a.name.localeCompare(b.name)
            })
          }

          const byId = {} as {
            [goodId: string]: DangerousGood
          }
          const byVesselId = {} as {
            [vesselId: string]: DangerousGood[]
          }
          all.forEach(good => {
            registerFiles(good.imageFiles, 'dangerousGoods', good)
            registerFiles(good.msdsFiles, 'dangerousGoods', good)
            byId[good.id] = good
            for (const vesselId of good.vesselIds) {
              if (byVesselId[vesselId] === undefined) {
                byVesselId[vesselId] = [] as DangerousGood[]
              }
              byVesselId[vesselId].push(good)
            }
          })

          set({
            all,
            byId,
            byVesselId,
          })
        },
        error => {
          done()
        }
      )
    }
  },
}
