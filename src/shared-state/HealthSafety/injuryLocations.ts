import { SharedStateConfig, sharedState } from '@src/shared-state/shared-state'
import { CategoriesData, onCategoriesSnapshot } from '@src/lib/categories'

/**
 * Loads injuryLocations
 */
export const injuryLocationsConfig: SharedStateConfig<CategoriesData> = {
  isAlwaysActive: false,
  dependencies: ['licenseeId', 'licenseeSettings'],
  countLiveDocs: () => sharedState.injuryLocations.current?.ids.length ?? 0,
  run: (done, set, clear) => {
    clear()
    const licenseeId = sharedState.licenseeId.current
    if (licenseeId && sharedState.licenseeSettings.current?.hasIncidents) {
      return onCategoriesSnapshot(
        'injuryLocations',
        'licenseeId',
        licenseeId,
        data => {
          // onLoaded
          done()
          set(data)
        },
        error => {
          // onError
          done()
          clear()
          console.log(`Error getting injuryLocations`, error)
        }
      )
    }
  },
}
