import { collection, limit, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { useEffect, useState } from 'react'
import { registerFiles } from '@src/shared-state/FileSyncSystem/filesToCache'
import { CorrectiveAction } from './correctiveActions'

/**
 * This is used to get a corrective action that is linked to an incident review
 */
export const useCorrectiveActionForIncidentReview = (incidentReviewId?: string) => {
  const [correctiveAction, setCorrectiveAction] = useState<CorrectiveAction>()
  useEffect(() => {
    setCorrectiveAction(undefined)
    if (incidentReviewId) {
      return onSnapshot(
        query(
          collection(firestore, 'correctiveActions'),
          where('incidentReviewId', '==', incidentReviewId),
          where('state', '==', 'active'),
          orderBy('dateDue', 'desc'),
          limit(1)
        ),
        snap => {
          if (snap.docs.length === 1) {
            const _correctiveAction = {
              id: snap.docs[0].id,
              ...snap.docs[0].data(),
            } as CorrectiveAction
            registerFiles(_correctiveAction.files, 'correctiveActions', _correctiveAction)
            setCorrectiveAction(_correctiveAction)
          }
        },
        error => {
          console.log(`Failed to access corrective action for incident review ${incidentReviewId} `, error)
        }
      )
    }
  }, [incidentReviewId])

  return correctiveAction
}
