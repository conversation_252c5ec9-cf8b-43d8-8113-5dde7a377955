import { SharedStateConfig, sharedState } from '@src/shared-state/shared-state'
import { CategoriesData, onCategoriesSnapshot } from '@src/lib/categories'

/**
 * Loads riskCategories
 */
export const riskCategoriesConfig: SharedStateConfig<CategoriesData> = {
  isAlwaysActive: false,
  dependencies: ['licenseeId'],
  countLiveDocs: () => sharedState.riskCategories.current?.ids.length ?? 0,
  run: (done, set, clear) => {
    clear()
    const licenseeId = sharedState.licenseeId.current
    if (licenseeId) {
      return onCategoriesSnapshot(
        'riskCategories',
        'licenseeId',
        licenseeId,
        data => {
          // onLoaded
          done()
          set(data)
        },
        error => {
          // onError
          done()
          clear()
          console.log(`Error getting riskCategories`, error)
        }
      )
    }
  },
}
