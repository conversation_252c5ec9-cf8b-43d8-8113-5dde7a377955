import { DocumentData, QueryDocumentSnapshot, collection, where } from '@src/lib/firebase/services/firestore.service'
import { firestore, setupArrayQueryListener } from '@src/lib/firebase'
import { SharedStateConfig, sharedState } from '@src/shared-state/shared-state'
import { canView } from '@src/shared-state/Core/userPermissions'
import { Job } from '@src/shared-state/VesselMaintenance/jobs'
import { registerFiles } from '@src/shared-state/FileSyncSystem/filesToCache'

export type SafetyMeetingJobsData = {
  byId: {
    [id: string]: Job
  }
}

export const safetyMeetingJobsConfig: SharedStateConfig<SafetyMeetingJobsData> = {
  isAlwaysActive: false,
  dependencies: ['vesselIds', 'userPermissions'],
  countLiveDocs: () => sharedState.jobs.current?.count ?? 0,
  run: (done, set, clear) => {
    clear()
    const vesselIds = sharedState.vesselIds.current
    if (vesselIds && canView('healthSafetyMeetings')) {
      return setupArrayQueryListener(
        'health & safety meeting jobs', // what
        collection(firestore, 'jobs'),
        [where('state', 'in', ['active', 'completed']), where('addedFromMeetingId', '!=', null)],
        'vesselId',
        'in',
        vesselIds,
        [], // orderBy
        (
          docs: QueryDocumentSnapshot<DocumentData>[],
          isCombined: boolean // (not needed as there is no sorting)
        ) => {
          // processDocs
          done()
          const byId = {} as {
            [id: string]: Job
          }
          docs.forEach(doc => {
            byId[doc.id] = {
              id: doc.id,
              ...doc.data(),
            } as Job
            registerFiles(byId[doc.id].files, 'jobs', byId[doc.id])
          })
          set({
            byId,
          })
        },
        error => {
          done()
          console.log(`Failed to get safetyMeetingJobs for vesselIds=${vesselIds}`, error)
        }
      )
    }
  },
}
