import { SharedStateConfig, sharedState } from '@src/shared-state/shared-state'
import { CategoriesData, Category, onCategoriesSnapshot } from '@src/lib/categories'

export interface IncidentCause extends Category {
  licenseeId: string
}

/**
 * Loads incidentCauses
 */
export const incidentCausesConfig: SharedStateConfig<CategoriesData> = {
  isAlwaysActive: false,
  dependencies: ['licenseeId', 'licenseeSettings'],
  countLiveDocs: () => sharedState.incidentCauses.current?.ids.length ?? 0,
  run: (done, set, clear) => {
    clear()
    const licenseeId = sharedState.licenseeId.current
    if (licenseeId && sharedState.licenseeSettings.current?.hasIncidents) {
      return onCategoriesSnapshot(
        'incidentCauses',
        'licenseeId',
        licenseeId,
        data => {
          // onLoaded
          done()
          set(data)
        },
        error => {
          // onError
          done()
          clear()
          console.log(`Error getting incidentCauses`, error)
        }
      )
    }
  },
}
