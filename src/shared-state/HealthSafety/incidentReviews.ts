import { collection, where, QueryDocumentSnapshot, DocumentData } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase/services/firestore.service'
import { CreateableDocument, sharedState, SharedStateConfig, UpdateableDocument } from '@src/shared-state/shared-state'
import { canView } from '@src/shared-state/Core/userPermissions'
import { setupArrayQueryListener } from '@src/lib/firebase'
import { registerFiles, registerSignature } from '@src/shared-state/FileSyncSystem/filesToCache'

//
// Loads incident reviews.
// An incident may have 0 or 1 incident reviews.
//

export const authoritiesNotifiedForReview: Record<string, string> = {
  n: "No, it's not requried",
  y: 'Yes',
  amsa: 'Yes, AMSA has been notified',
  whs: 'Yes, WHS has been notified',
  amsaWhs: 'Yes, both AMSA and WHS have been notified',
  mnz: 'Yes, Maritime New Zealand has been notified',
  ws: 'Yes, Work Safe has been notified',
  mnzWs: 'Yes, both MNZ and WS have been notified',
  mca: 'Yes, MCA has been notified',
  hse: 'Yes, HSE has been notified',
  miab: 'Yes, MAIB has been noitified',
  mcaHse: 'Yes, MCA and HSE have been notified',
  mcaMiab: 'Yes, MCA and MAIB have been notified',
  hseMiab: 'Yes, HSE and MAIB have notified',
  mcaHseMiab: 'Yes, MCA, HSE, and MAIB have been notified',
}

export const incidentSeverities: Record<number, string> = {
  0: 'Not Set',
  10: 'Low',
  20: 'Medium',
  30: 'High',
  40: 'Severe',
  50: 'Catastrophic',
}

export const yesNoNa: Record<string, string> = {
  na: 'Not Applicable',
  n: 'No',
  y: 'Yes',
}

export const controlStrategies: Record<string, string> = {
  na: 'Not Applicable',
  elimination: 'Elimination',
  substitution: 'Substitution',
  Isolation: 'Isolation',
  engineering: 'Engineering',
  admin: 'Administrative',
  ppe: 'PPE',
}

export const addedToRiskRegister: Record<string, string> = {
  na: 'Not Applicable',
  already: 'Already in risk assessments',
  y: 'Yes',
  n: 'No',
}

export const injuryConclusions: Record<string, string> = {
  none: 'None',
  basicFirstAid: 'Basic First Aid',
  doctor: 'Doctor - No lost work time injury',
  doctorLost: 'Doctor - Lost work time injury',
  hospital: 'Hospitalisation - No lost work time injury',
  hospitalLost: 'Hospitalisation - Lost work time injury',
}

export const lostTimes: Record<string, string> = {
  '0': 'None',
  '1d': '1 day',
  '2d': '2 days',
  '3d': '3 days',
  '4d': '4 days',
  '5d': '5 days',
  '6d': '6 days',
  '7d': '7 days',
  '2w': '2 weeks',
  '3w': '3 weeks',
  '4w': '4 weeks',
  '5w': '5 weeks',
  '6w': '6 weeks',
  '7w': '7 weeks',
  '8w': '8 weeks',
  '3m': '3 months',
  '4m': '4 months',
  '5m': '5 months',
  '6m': '6 months',
}

export interface IncidentReview extends CreateableDocument, UpdateableDocument {
  addedToRiskRegister?: string
  analysis?: string
  causeIds: string[]
  completedBy?: string
  correctiveActionIds?: string[]
  deletedBy?: string
  externalRequired?: string
  files: string[]
  injuryConclusions?: InjuryConclusions[]
  licenseeId: string
  lostTime?: string
  notifiedAuthorities?: string
  prevention?: string
  severity: number
  signature?: string
  state: string
  strategies: ControlStrategies[]
  vesselId: string
  whenCompleted?: number
  whenDeleted?: number
}

export type IncidentReviewsData = {
  byId: {
    [incidentId: string]: IncidentReview
  }
}

export const incidentReviewsConfig: SharedStateConfig<IncidentReviewsData> = {
  isAlwaysActive: false,
  dependencies: ['vesselIds', 'licenseeSettings', 'userPermissions'],
  countLiveDocs: () => Object.keys(sharedState.incidentReviews.current?.byId ?? {}).length,
  run: (done, set, clear) => {
    clear()
    const vesselIds = sharedState.vesselIds.current
    if (
      vesselIds &&
      vesselIds.length > 0 &&
      sharedState.licenseeSettings.current?.hasIncidents &&
      canView('incidentAccidentMedicalRegister')
    ) {
      return setupArrayQueryListener(
        'incidentReviews', // what
        collection(firestore, 'incidentReviews'),
        [where('state', 'in', ['draft', 'completed'])], // baseConstraints
        'vesselId',
        'in',
        vesselIds,
        [],
        (docs: QueryDocumentSnapshot<DocumentData>[], isCombined: boolean) => {
          // processDocs
          done()
          const byId = {} as {
            [id: string]: IncidentReview
          }
          docs.forEach(doc => {
            const review = {
              id: doc.id,
              ...doc.data(),
            } as IncidentReview
            byId[doc.id] = review
            registerFiles(review.files, 'incidentReviews', review)
            registerSignature(review.signature, 'incidentReviews', review)
          })
          set({
            byId,
          })
        },
        error => {
          done()
        }
      )
    }
  },
}

export type InjuryConclusions = keyof typeof injuryConclusions
export type ControlStrategies = keyof typeof controlStrategies
