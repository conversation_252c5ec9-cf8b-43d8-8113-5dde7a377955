import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { useEffect, useState } from 'react'
import { CreateableDocument, sharedState, UpdateableDocument } from '@src/shared-state/shared-state'
import { addInterval, DateRange, makeDateTime } from '@src/lib/datesAndTime'

export interface RiskReviewed extends CreateableDocument, UpdateableDocument {
  files: string[]
  notes?: string
  reviewedBy: string
  riskId: string
  state: string
  vesselIds: string[]
  dateReviewed: string
}

export type RisksReviewedData = {
  all: RiskReviewed[]
  byRiskId: Record<string, RiskReviewed[]>
}

export const useRisksReviewedDateRange = (dateRange?: DateRange) => {
  const vesselIds = sharedState.vesselIds.use(dateRange ? 1 : 0)
  const [risksReviewed, setRisksReviewed] = useState<RisksReviewedData>()

  useEffect(() => {
    setRisksReviewed(undefined)
    if (dateRange && vesselIds?.length) {
      return onSnapshot(
        query(
          collection(firestore, 'risksReviewed'),
          where('vesselIds', 'array-contains-any', vesselIds),
          where('state', '==', 'active'),
          where('dateReviewed', '>=', makeDateTime(dateRange.from).toISODate()),
          where('dateReviewed', '<=', addInterval(dateRange.to, '1d').toISODate()),
          orderBy('dateReviewed', 'desc')
        ),
        snap => {
          const byRiskId: Record<string, RiskReviewed[]> = {}
          const all = snap.docs.map(doc => {
            const riskReviewed = {
              id: doc.id,
              ...doc.data(),
            } as RiskReviewed
            if (!byRiskId[riskReviewed.riskId]) {
              byRiskId[riskReviewed.riskId] = []
            }
            byRiskId[riskReviewed.riskId].push(riskReviewed)
            return riskReviewed
          })
          setRisksReviewed({
            all,
            byRiskId,
          })
        },
        error => {
          console.log(`Failed to access risks reviewed for vesselIds ${vesselIds.join(', ')} `, error)
        }
      )
    }
  }, [dateRange, vesselIds])

  return risksReviewed
}
