import { SharedStateConfig, sharedState } from '@src/shared-state/shared-state'
import { CategoriesData, onCategoriesSnapshot } from '@src/lib/categories'

/**
 * Loads incidentCategories
 */
export const incidentCategoriesConfig: SharedStateConfig<CategoriesData> = {
  isAlwaysActive: false,
  dependencies: ['licenseeId', 'licenseeSettings'],
  countLiveDocs: () => sharedState.incidentCategories.current?.ids.length ?? 0,
  run: (done, set, clear) => {
    clear()
    const licenseeId = sharedState.licenseeId.current
    if (licenseeId && sharedState.licenseeSettings.current?.hasIncidents) {
      return onCategoriesSnapshot(
        'incidentCategories',
        'licenseeId',
        licenseeId,
        data => {
          // onLoaded
          done()
          set(data)
        },
        error => {
          // onError
          done()
          clear()
          console.log(`Error getting incidentCategories`, error)
        }
      )
    }
  },
}
