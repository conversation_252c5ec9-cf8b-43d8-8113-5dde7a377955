import {
  collection,
  DocumentData,
  orderBy,
  QueryDocumentSnapshot,
  QueryOrderByConstraint,
  where,
} from '@src/lib/firebase/services/firestore.service'
import { firestore, setupArrayQueryListener } from '@src/lib/firebase'
import {
  CreateableDocument,
  SharedStateConfig,
  SyncableDocument,
  UpdateableDocument,
  sharedState,
} from '@src/shared-state/shared-state'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { canView } from '@src/shared-state/Core/userPermissions'
import { registerFiles } from '@src/shared-state/FileSyncSystem/filesToCache'

// TODO: Ideally use 1 StateEnum across the entire app and across all the collections
export enum CorrectiveActionState {
  Active = 'active',
  Completed = 'completed',
  Deleted = 'deleted',
}

export interface CorrectiveAction extends CreateableDocument, UpdateableDocument, SyncableDocument {
  correctiveActionNum: string
  assignedTo?: string
  completedBy?: string
  completedNotes?: string
  completedFiles?: string[]
  deletedBy?: string
  description: string
  emailReminder?: string
  files: string[]
  licenseeId: string
  state: string
  tags?: string[]
  title: string
  vesselIds: string[]
  whenCompleted?: number
  whenDeleted?: number
  dateDue?: string
  dateToRemind?: string
  incidentReviewId?: string
  // Generated at run time - not in original data
  searchText?: string
}

export type CorrectiveActionsData = {
  byId: Record<string, CorrectiveAction>
  byVesselId: Record<string, CorrectiveAction[]> // Contains both active and completed CorrectiveActions
  array: {
    completed: CorrectiveAction[]
    active: CorrectiveAction[]
    all: CorrectiveAction[]
  }
}

export const correctiveActionsConfig: SharedStateConfig<CorrectiveActionsData> = {
  isAlwaysActive: false,
  dependencies: ['vesselIds', 'userPermissions'],
  countLiveDocs: () => sharedState.correctiveActions.current?.array.all.length ?? 0,
  run: (done, set, clear) => {
    clear()
    const vesselIds = sharedState.vesselIds.current
    if (vesselIds && canView('correctiveActions')) {
      const makeSearchText = (correctiveAction: CorrectiveAction) => {
        let s = correctiveAction.title
        if (correctiveAction.assignedTo) {
          s += renderFullNameForUserId(correctiveAction.assignedTo)
        }
        if (correctiveAction.description) {
          s += correctiveAction.description
        }
        if (correctiveAction.correctiveActionNum) {
          s += correctiveAction.correctiveActionNum
        }
        if (correctiveAction.tags) {
          s += correctiveAction.tags.join('')
        }
        return s.toLowerCase()
      }

      return setupArrayQueryListener(
        'correctiveActions', // what
        collection(firestore, 'correctiveActions'),
        [where('state', 'in', ['active', 'completed'])], // baseConstraints
        'vesselIds',
        'array-contains-any',
        vesselIds,
        [orderBy('title', 'asc') as QueryOrderByConstraint],
        (docs: QueryDocumentSnapshot<DocumentData>[], isCombined: boolean) => {
          // processDocs
          done()

          const all = docs.map(doc => {
            return {
              id: doc.id,
              ...doc.data(),
            } as CorrectiveAction
          })

          if (isCombined) {
            // Sort by title
            all.sort((a, b) => {
              return a.title.localeCompare(b.title)
            })
          }

          const active = [] as CorrectiveAction[]
          const completed = [] as CorrectiveAction[]
          const byId = {} as Record<string, CorrectiveAction>
          const byVesselId = {} as Record<string, CorrectiveAction[]>

          all.forEach(correctiveAction => {
            correctiveAction.searchText = makeSearchText(correctiveAction)
            byId[correctiveAction.id] = correctiveAction
            all.push(correctiveAction)
            registerFiles(correctiveAction.files, 'correctiveActions', correctiveAction)
            if (correctiveAction.state === 'active') {
              active.push(correctiveAction)
            } else {
              completed.push(correctiveAction)
            }
            if (correctiveAction.vesselIds) {
              correctiveAction.vesselIds.forEach(vesselId => {
                byVesselId[vesselId] ??= []
                byVesselId[vesselId].push(correctiveAction)
              })
            }
          })

          set({
            array: {
              completed,
              active,
              all,
            },
            byId,
            byVesselId,
          })
        },
        error => {
          // onError
          console.log(`Error getting correctiveActions`, error.message, error)
          done()
        }
      )
    } else {
      done()
    }
  },
}
