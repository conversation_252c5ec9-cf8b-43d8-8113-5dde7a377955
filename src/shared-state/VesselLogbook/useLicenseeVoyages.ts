import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { formatMonthISO, addInterval, makeDateTime, DateRange } from '@src/lib/datesAndTime'
import { canView } from '@src/shared-state/Core/userPermissions'
import { Voyage } from './voyages'
import { useCallback, useEffect, useState } from 'react'
import { useFocusEffect } from '@react-navigation/native'

export type LicenseeVoyageData = {
  all: Voyage[]
  byVessel: { [key: string]: Voyage[] }
  vessels: string[]
  byCategory: { [key: string]: Voyage[] }
  categories: string[]
}

export const useLicenseeVoyages = (dateRange: DateRange | undefined, selectedVesselIds: string[] | undefined) => {
  const [completedVoyages, setCompletedVoyages] = useState<LicenseeVoyageData | undefined>(undefined)

  useFocusEffect(
    useCallback(() => {
      setCompletedVoyages(undefined)

      if (canView('logbook') && dateRange && selectedVesselIds?.length) {
        console.log('TTT - Trigger LogBook - 1', dateRange, selectedVesselIds)
        return onSnapshot(
          query(
            collection(firestore, 'voyages'),
            where('vesselId', 'in', selectedVesselIds),
            where('state', 'in', ['started', 'completed']),
            where('whenDeparted', '>=', makeDateTime(dateRange.from).toMillis()),
            where('whenDeparted', '<', addInterval(dateRange.to, '1d').toMillis()),
            orderBy('whenDeparted', 'desc')
          ),
          snap => {
            const _voyages = snap.docs
              .map(doc => {
                const voyage = {
                  id: doc.id,
                  ...doc.data(),
                } as Voyage
                return voyage
              })
              .sort((a, b) => {
                if (a.state === 'started') return -1
                if (b.state === 'started') return 1
                if (a.whenArrived === undefined) return -1
                if (b.whenArrived === undefined) return 1
                return b.whenArrived - a.whenArrived
              })

            const byVessel = {} as {
              [category: string]: Voyage[]
            }
            const vessels: string[] = []
            const byCategory = {} as {
              [category: string]: Voyage[]
            }
            const categories: string[] = []
            _voyages.forEach(item => {
              // Setup vessels
              if (byVessel[item.vesselId] === undefined) {
                vessels.push(item.vesselId)
                byVessel[item.vesselId] = []
              }
              byVessel[item.vesselId].push(item)

              // Setup categories
              const whenArrived = item.whenArrived ? formatMonthISO(item.whenArrived) : formatMonthISO(Date.now())
              if (byCategory[whenArrived] === undefined) {
                categories.push(whenArrived)
                byCategory[whenArrived] = []
              }
              byCategory[whenArrived].push(item)
            })
            categories.sort().reverse()

            setCompletedVoyages({
              all: _voyages,
              byVessel,
              vessels,
              byCategory,
              categories,
            })
          },
          error => {
            // This should be very rare
            console.log(`Failed to access voyages for vessels ${selectedVesselIds.join(', ')}, ${error.message}`)
          }
        )
      }
    }, [dateRange, selectedVesselIds])
  )

  return completedVoyages
}
