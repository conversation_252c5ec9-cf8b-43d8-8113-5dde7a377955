import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { Voyage } from './voyages'
import { useState, useEffect } from 'react'
import { registerFiles } from '@src/shared-state/FileSyncSystem/filesToCache'
import { sharedState } from '@src/shared-state/shared-state'
import { addInterval, DateRange, makeDateTime } from '@src/lib/datesAndTime'
export type VoyagesData = {
  byId: {
    [id: string]: Voyage
  }
  all: Voyage[]
}

export const useVoyages = (tripType: 'singleDay' | 'multiDay' | 'multiTrip', dateRange?: DateRange) => {
  const vesselId = sharedState.vesselId.use()
  const [voyages, setVoyages] = useState<VoyagesData>()

  useEffect(() => {
    setVoyages(undefined)
    if (vesselId && tripType && dateRange) {
      return onSnapshot(
        query(
          collection(firestore, 'voyages'),
          where('vesselId', '==', vesselId),
          where('tripType', '==', tripType),
          where('whenAdded', '>=', makeDateTime(dateRange.from).toMillis()),
          where('whenAdded', '<', addInterval(dateRange.to, '1d').toMillis()),
          where('state', '==', 'completed'),
          orderBy('whenAdded', 'desc')
        ),
        snap => {
          const byId = {} as {
            [id: string]: Voyage
          }
          const all = snap.docs.map(doc => {
            const o = {
              id: doc.id,
              ...doc.data(),
            } as Voyage
            byId[o.id] = o
            registerFiles(o.files, 'voyages', o)
            return o
          })
          setVoyages({
            byId,
            all,
          })
        },
        error => {
          console.log(
            `Failed to access voyages for vesselId=${vesselId} with tripType=${tripType} and dateRange=${dateRange}`,
            error.message,
            error,
            {
              vesselId,
              tripType,
              dateRange,
            }
          )
        }
      )
    }
  }, [vesselId, tripType, dateRange])

  return voyages
}
