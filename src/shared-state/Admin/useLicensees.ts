import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { LicenseeUserType } from '@src/shared-state/Core/user'
import { useEffect, useState } from 'react'

export type LicenseesData = {
  all: LicenseeUserType[]
  byId: Record<string, LicenseeUserType>
  totalActive: number
  totalVessels: number
}

/**
 * Loads all active licensees.
 * This can only be used by superAdmin.
 */
export const useLicensees = () => {
  const [licensees, setLicensees] = useState<LicenseesData>()

  useEffect(() => {
    setLicensees(undefined)
    return onSnapshot(
      query(
        collection(firestore, 'users'),
        where('isLicensee', '==', true),
        where('state', '==', 'active'),
        orderBy('firstName'),
        orderBy('lastName')
      ),
      snap => {
        // if (isDesktop && snapshot.metadata.hasPendingWrites) {
        //     return; // For desktop, we don't want to show data not yet saved to the cloud
        // }
        const all = snap.docs.map(doc => {
          return {
            id: doc.id,
            ...doc.data(),
          }
        })
        const byId = {} as Record<string, LicenseeUserType>
        // Calculate summary stats
        let totalActive = 0
        let totalVessels = 0
        all.forEach((user: LicenseeUserType) => {
          totalActive++
          if (user.numVessels) {
            totalVessels += user.numVessels
          }
          byId[user.id] = user
        })
        setLicensees({
          all,
          byId,
          totalActive,
          totalVessels,
        })
      },
      error => {
        console.log(`error getting licensees`, error)
      }
    )
  }, [])
  return licensees
}
