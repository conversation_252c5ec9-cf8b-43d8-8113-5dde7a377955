import {
  firestore,
  collection,
  onSnapshot,
  query,
  QuerySnapshot,
  DocumentData,
} from '@src/lib/firebase/services/firestore.service'
import { UserType } from '@src/shared-state/Core/user'
import { useEffect, useState } from 'react'

export type AllUsersData = {
  byId: Record<string, UserType>
}

/**
 * Loads all users.
 * This can only be used by superAdmin.
 */
export const useAllUsers = () => {
  const [allUsers, setAllUsers] = useState<AllUsersData>()

  useEffect(() => {
    setAllUsers(undefined)
    return onSnapshot(
      query(
        collection(firestore, 'users')
        // where('state', '==', 'active')
      ),
      (snap: QuerySnapshot<DocumentData>) => {
        const byId = {} as any

        snap.docs.forEach(doc => {
          byId[doc.id] = {
            id: doc.id,
            ...doc.data(),
          }
        })

        setAllUsers({
          byId,
        })
      },
      error => {
        console.log(`error getting allUsers`, error)
      }
    )
  }, [])

  return allUsers
}
