import { collection, onSnapshot, orderBy, query } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { useEffect, useState } from 'react'

export type ServerEvent = {
  id: string
  type: 'message'
  whenEvent: number
  whenExpires?: number
  isPending?: boolean
  whenAdded: number
  addedBy: string
  state: string
  title?: string
  content?: string
}

export type ServerEventData = {
  all: ServerEvent[]
  byId: Record<string, ServerEvent>
}

/**
 * Loads all serverEvents.
 * This can only be used by superAdmin.
 */
export const useServerEvents = () => {
  const [serverEvents, setServerEvents] = useState<ServerEventData>()

  useEffect(() => {
    setServerEvents(undefined)
    return onSnapshot(
      query(collection(firestore, 'serverEvents'), orderBy('whenEvent', 'desc')),
      snap => {
        const all = snap.docs.map(doc => {
          return {
            id: doc.id,
            ...doc.data(),
          } as ServerEvent
        })
        const byId = {} as Record<string, ServerEvent>
        all.forEach((serverEvent: ServerEvent) => {
          byId[serverEvent.id] = serverEvent
        })
        setServerEvents({
          all,
          byId,
        })
      },
      error => {
        console.log(`error getting serverEvents`, error)
      }
    )
  }, [])
  return serverEvents
}
