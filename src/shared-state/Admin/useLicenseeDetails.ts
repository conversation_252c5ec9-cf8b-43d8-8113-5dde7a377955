import { collection, onSnapshot, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { useEffect, useState } from 'react'
import { UserDetails } from '@src/shared-state/Crew/userDetails'

/**
 * Loads all active userDetails accross all licensee accounts.
 * This is A LOT loading ALL userDetails when we just want those that are licensees.
 * Oh well... only superAdmin can/will use this. Maybe we'll optimise this later...
 */
export const useLicenseeDetailsById = () => {
  const [licenseeDetailsById, setLicenseeDetailsById] = useState<Record<string, UserDetails>>()

  useEffect(() => {
    setLicenseeDetailsById(undefined)
    return onSnapshot(query(collection(firestore, 'userDetails'), where('state', '==', 'active')), snap => {
      const byId = {} as Record<string, UserDetails>
      for (const doc of snap.docs) {
        byId[doc.id] = {
          id: doc.id,
          ...doc.data(),
        } as UserDetails
      }
      setLicenseeDetailsById(byId)
    })
  }, [])

  return licenseeDetailsById
}
