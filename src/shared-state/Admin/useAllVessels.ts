import { collection, onSnapshot, query } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { useEffect, useState } from 'react'
import { Vessel } from '@src/shared-state/Core/vessel'

export type AllVesselsData = {
  byId: Record<string, Vessel>
}

/**
 * Loads all vessels.
 * This can only be used by superAdmin.
 */
export const useAllVessels = () => {
  const [allVessels, setAllVessels] = useState<AllVesselsData>()

  useEffect(() => {
    setAllVessels(undefined)
    return onSnapshot(
      query(
        collection(firestore, 'vessels')
        // where('state', '==', 'active')
      ),
      snap => {
        const byId = {} as any

        snap.docs.forEach(doc => {
          byId[doc.id] = {
            id: doc.id,
            ...doc.data(),
          }
        })

        setAllVessels({
          byId,
        })
      },
      error => {
        console.log(`error getting allVessels`, error)
      }
    )
  }, [])

  return allVessels
}
