import { collection, DocumentData, QueryDocumentSnapshot, where } from '@src/lib/firebase/services/firestore.service'
import { firestore, setupArrayQueryListener } from '@src/lib/firebase'
import {
  ArchivableDocument,
  CreateableDocument,
  SharedStateConfig,
  SyncableDocument,
  UpdateableDocument,
  sharedState,
} from '@src/shared-state/shared-state'

export type LinkCollectionOption =
  | 'customForms'
  | 'drills'
  | 'external'
  | 'risks'
  | 'scheduledMaintenanceTasks'
  | 'SOPs'
  | 'trainingTasks'
  | 'companyDocuments'
  | 'vesselDocuments'
  | 'safetyCheckItems'

export interface LinkType extends CreateableDocument, UpdateableDocument, ArchivableDocument, SyncableDocument {
  id: string
  aType: Exclude<LinkCollectionOption, 'external'> // The left side of a link cannot be external
  aId: string
  bType: LinkCollectionOption
  bId: string // In the case of an external link (bType === 'external'), bId will be the URL
  vesselIds: string[]
}

export type LinksData = {
  byLinkId: {
    [id: string]: LinkType[]
  }
  count: number
}

export const linksConfig: SharedStateConfig<LinksData> = {
  isAlwaysActive: false,
  dependencies: ['vesselIds', 'licenseeId'],
  countLiveDocs: () => sharedState.links.current?.count ?? 0,
  run: (done, set, clear) => {
    clear()
    const licenseeId = sharedState.licenseeId.current
    const vesselIds = sharedState.vesselIds.current
    if (licenseeId) {
      return setupArrayQueryListener(
        'links',
        collection(firestore, 'links'),
        [where('licenseeId', '==', licenseeId), where('state', '==', 'active')],
        'vesselIds',
        'array-contains-any',
        ['na', ...(vesselIds || [])], // na is added to include links that are not specific to any vessel
        [],
        (
          docs: QueryDocumentSnapshot<DocumentData>[],
          isCombined: boolean // (not needed as there is no sorting)
        ) => {
          // processDocs
          done()

          const byLinkId = {} as {
            [linkId: string]: LinkType[]
          }

          const addLink = (linkId: string, link: LinkType) => {
            if (byLinkId[linkId] === undefined) {
              byLinkId[linkId] = []
            }
            byLinkId[linkId].push(link)
          }

          docs.forEach(doc => {
            const link = {
              id: doc.id,
              ...doc.data(),
            } as LinkType
            addLink(link.aId, link)
            if (link.bType !== 'external') {
              addLink(link.bId, link)
            }
          })

          set({
            byLinkId,
            count: docs.length,
          })
        },
        error => {
          done()
          console.log(`Failed to get links for vesselIds=${vesselIds}`, error)
        }
      )
    }
  },
}
