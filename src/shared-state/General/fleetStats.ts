import { sharedState, SharedStateConfig } from '@src/shared-state/shared-state'
import {
  doc,
  docExists,
  DocumentData,
  DocumentSnapshot,
  firestore,
  onSnapshot,
} from '@src/lib/firebase/services/firestore.service'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { ServiceContainer } from '@src/domain/di/ServiceContainer'
import { ILogger } from '@src/domain/util/logger/ILogger'

export enum VesselCollectionsUsedForStats {
  // Safety
  DRILLS = 'drills',
  SAFETY_CHECK_ITEMS = 'safetyCheckItems',
  SAFETY_EQUIPMENT_ITEMS = 'safetyEquipmentItems',

  // Maintenance
  SCHEDULED_MAINTENANCE_TASKS = 'scheduledMaintenanceTasks',
  JOBS = 'jobs',
  SPARE_PARTS = 'spareParts',

  // Vessel Document Register
  VESSEL_CERTIFICATES = 'vesselCertificates',
  VESSEL_DOCUMENTS = 'vesselDocuments',

  /** Licensee based collections */
  // Health & Safety
  INCIDENTS = 'incidents',
  RISK_ASSESSMENTS = 'risks',
  DANGEROUS_GOODS = 'dangerousGoods',
}

export enum CompanyCollectionsUsedForStats {
  // Company Document Register
  COMPANY_DOCUMENTS = 'companyDocuments',

  // Crew
  CREW_CERTIFICATES = 'crewCertificates',
}
export const defaultListOfVesselCollections = Object.values(VesselCollectionsUsedForStats)
export const defaultListOfCompanyCollections = Object.values(CompanyCollectionsUsedForStats)

export type CollectionsUsedForStats = VesselCollectionsUsedForStats | CompanyCollectionsUsedForStats

export type CalculatedStats = {
  [key: string]: number
}

export type CollectionStats = {
  whenUpdated: number
  stale: boolean | undefined
  stats: CalculatedStats
}

export type VesselStats = {
  whenUpdated: number
  stale: boolean | undefined
  collections: {
    [key in VesselCollectionsUsedForStats]: CollectionStats
  }
}
export type CompanyStats = {
  whenUpdated: number
  stale: boolean | undefined
  collections: {
    [key in CompanyCollectionsUsedForStats]: CollectionStats
  }
}

export type FleetStats = {
  whenUpdated: number
  touched: number
  stale: boolean | undefined
  vessels: {
    [vesselId: string]: VesselStats
  }
  company: CompanyStats

  // New Fields for internal use
  vesselCount?: number
  hasStaleCollections: boolean
}

export const fleetStatsConfig: SharedStateConfig<FleetStats> = {
  isAlwaysActive: false,
  default: {
    whenUpdated: 0,
    touched: 0,
    stale: true,
    vessels: {},
    // @ts-ignore
    company: {},

    // New Fields for internal use
    vesselCount: 0,
    hasStaleCollections: false,
  },
  countLiveDocs: () => (sharedState.fleetStats.current ? 1 : 0),
  dependencies: ['userPermissions'],
  notes: 'Source: Fleet stats for the Licensee/Fleet',
  run: (done, set, clear) => {
    clear()
    const licenseeId = sharedState.licenseeId.current
    const logger = ServiceContainer.get<ILogger>(SERVICES.ILogger)

    if (licenseeId) {
      return onSnapshot(
        doc(firestore, 'fleetStats', licenseeId),
        (snapDoc: DocumentSnapshot<DocumentData>) => {
          done()
          if (docExists(snapDoc)) {
            const fleetStats = snapDoc.data()
            const hasStaleCollections = hasStaleStats(fleetStats as FleetStats)

            // @ts-ignore
            set({
              ...fleetStats,
              touched: fleetStats?.touched ? fleetStats?.touched.toMillis() : 0,
              vesselCount: Object.keys(fleetStats?.vessels ?? {}).length,
              hasStaleCollections: hasStaleCollections,
            })
          } else {
            // If the document does not exist, set default values
            set({ ...fleetStatsConfig.default, hasStaleCollections: true } as FleetStats)
          }
        },
        error => {
          done()
          logger.error('SharedState: Error getting `fleetStats`', { error, licenseeId })
        }
      )
    }
  },
}

const hasStaleStats = (fleetStats: FleetStats | undefined): boolean => {
  if (!fleetStats) return true

  // Check if overall fleet stats are stale
  if (fleetStats?.stale === undefined || fleetStats.stale) return true

  // Check if company stats are stale
  if (fleetStats?.company?.stale === undefined || fleetStats.company?.stale) return true

  // Check company collections
  for (const collection of Object.values(fleetStats.company?.collections || {})) {
    if (collection?.stale === undefined || collection.stale) return true
  }

  // Check vessel stats
  for (const vessel of Object.values(fleetStats.vessels || {})) {
    if (vessel?.stale === undefined || vessel.stale) return true

    // Check vessel collections
    for (const collection of Object.values(vessel.collections || {})) {
      if (collection?.stale === undefined || collection.stale) return true
    }
  }

  return false
}
