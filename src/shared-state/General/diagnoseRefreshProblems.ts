import { collection, doc, serverTimestamp, setDoc } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { sharedState, SharedStateConfig } from '@src/shared-state/shared-state'
import { appActivityToJson } from './appActivity'
import packageJson from '../../../package.json'
import { buildNumber, physicalDeviceInfo } from '@src/shared-state/Core/deviceInfo'
import { toInt } from '@src/lib/util'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { Platform } from 'react-native'

export let iosRefreshProblem = undefined as any
//export let reportRefresh = '';

export const setAppIsOkToRestart = (appIsOkToRestart: boolean, lastState: string) => {
  // If true, it means if the app restarts we should consider it ok/normal
  // If false, it means the app restarted unexpectedly
  // App is ok to restart if the app is in background mode
  return AsyncStorage.setItem('__appIsOkToRestart', appIsOkToRestart ? 'true' : 'false').then(() => {
    return AsyncStorage.setItem('__appLastState', lastState)
  })
}

export const checkForIosRefreshProblem = () => {
  return AsyncStorage.getItem('__appIsOkToRestart').then(appIsOkToRestart => {
    if (appIsOkToRestart === 'false') {
      let appLastState: any
      return AsyncStorage.getItem('__appLastState')
        .then(_appLastState => {
          appLastState = _appLastState
          return AsyncStorage.getItem('__appActivity')
        })
        .then(appActivity => {
          iosRefreshProblem = {
            lastState: appLastState,
            appActivityJson: appActivity,
          }
        })
    }
  })
}

export const handleRefreshProblemReportingConfig: SharedStateConfig<string> = {
  isAlwaysActive: true,
  dependencies: ['deviceId', 'licenseeId'],
  default: 'Not run',
  run: (done, set, clear) => {
    done()
    const deviceId = sharedState.deviceId.current
    const licenseeId = sharedState.licenseeId.current
    if (deviceId && licenseeId) {
      //reportRefresh += `maybe... `;
      if (iosRefreshProblem) {
        // Report!
        //reportRefresh += `Report! `;

        //reportRefresh += `userId=${sharedState.userId.current} licenseeId=${sharedState.licenseeId.current} `;
        setDoc(doc(collection(firestore, '_unintentionalRefreshes')), {
          userId: sharedState.userId.current,
          licenseeId: licenseeId,
          deviceId: deviceId,
          lastState: iosRefreshProblem.lastState,
          appActivity: appActivityToJson(iosRefreshProblem.appActivityJson),
          whenInit: sharedState.appActivity.current!.init,
          whenReported: Date.now(),
          touched: serverTimestamp(),
          version: packageJson.version,
          build: toInt(buildNumber),
          device: physicalDeviceInfo,
          platform: Platform.OS,
        })
          .then(() => {
            console.log(`Successfully reported unintentional refresh!`)
            //reportRefresh += `Success `;
          })
          .catch((error: any) => {
            console.log(`Failed to report refresh problem!`, error)
            //reportRefresh += `Fail `;
            //reportRefresh += error?.message + ' ';
          })
      }
    } else {
      set('Not ready')
    }
  },
}
