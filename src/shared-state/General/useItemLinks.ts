import { useState, useEffect } from 'react'
import { LinkType } from './links'
import { sharedState } from '@src/shared-state/shared-state'

/**
 * Returns an array of LinkType that reference itemId (via aId or bId)
 */
export const useItemLinks = (itemId?: string) => {
  const _links = sharedState.links.use()
  const [links, setLinks] = useState<LinkType[]>()

  useEffect(() => {
    setLinks(undefined)
    if (itemId && _links?.byLinkId) {
      setLinks(_links.byLinkId[itemId] ?? [])
    }
  }, [itemId, _links])

  return links
}

/**
 * Similar to useItemLinks, but can combine links from multiple itemIds
 */
export const useItemLinksFromArray = (itemIds?: string[]) => {
  const _links = sharedState.links.use()
  const [links, setLinks] = useState<LinkType[]>()

  useEffect(() => {
    setLinks(undefined)
    if (itemIds && _links?.byLinkId) {
      const result = [] as LinkType[]
      itemIds.forEach(itemId => {
        if (_links.byLinkId[itemId]) {
          result.push(..._links.byLinkId[itemId])
        }
      })
      setLinks(result)
    }
  }, [itemIds, _links])

  return links
}
