import { useState, useEffect } from 'react'
import {
  doc,
  docExists,
  DocumentData,
  DocumentSnapshot,
  onSnapshot,
} from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { sharedState } from '@src/shared-state/shared-state'

export interface OverdueStat {
  safetyCheckItems: { overdue: number; stale: boolean }
  safetyEquipmentItems: { overdue: number; stale: boolean }
  drills: { overdue: number; stale: boolean }
  scheduledMaintenanceTasks: { overdue: number; stale: boolean }
  jobs: { overdue: number; stale: boolean }
  vesselCertificates: { overdue: number; stale: boolean }
  vesselDocuments: { overdue: number; stale: boolean }
  safetyMeetings: { overdue: number; stale: boolean }
}

export interface OverdueStats {
  whenUpdated: number
  [vesselId: string]: OverdueStat | number // whenUpdated is also a property of OverdueStats, hence the union type
}

export const useOverdueStats = (triggerRefreshOverdueStats: () => void) => {
  const licenseeId = sharedState.licenseeId.use()
  const [overdueStats, setOverdueStats] = useState<OverdueStats>()

  useEffect(() => {
    setOverdueStats(undefined)
    if (licenseeId) {
      return onSnapshot(
        doc(firestore, 'overdueStats', licenseeId),
        (doc: DocumentSnapshot<DocumentData>) => {
          if (!docExists(doc)) {
            setOverdueStats(undefined)
            triggerRefreshOverdueStats()
          } else {
            setOverdueStats(doc.data() as OverdueStats)
          }
        },
        error => {
          console.log(`Failed to access overdueStats for licenseeId=${licenseeId}`, error)
        }
      )
    }
  }, [licenseeId, triggerRefreshOverdueStats])

  return overdueStats
}
