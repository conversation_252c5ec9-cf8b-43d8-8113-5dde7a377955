import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { CreateableDocument, DeleteableDocument, UpdateableDocument, sharedState } from '@src/shared-state/shared-state'
import { makeDateTime } from '@src/lib/datesAndTime'
import { canView } from '@src/shared-state/Core/userPermissions'
import { UserType } from '@src/shared-state/Core/user'
import { renderFullName } from '@src/shared-state/Core/users'
import { useEffect, useState } from 'react'

//
// Load training task reports
//

export interface TrainingTaskReport extends CreateableDocument, UpdateableDocument, DeleteableDocument {
  completedBy: string[]
  licenseeId: string
  notes?: string
  state: string
  taskId: string
  trainer: string
  vesselId: string
  dateCompleted: string
  dateDue: string
  dateDueDiff?: number
}

export type TrainingTaskReportsData = {
  all: TrainingTaskReport[] // ordered by dateDue (desc)
  users: UserType[] // users that have completed at least one task, sorted alphabetically
  byId: {
    [reportId: string]: TrainingTaskReport
  }
  byTaskId: {
    [taskId: string]: TrainingTaskReport[] // ordered by dateDue (desc)
  }
  byUserId: {
    [userId: string]: TrainingTaskReport[] // ordered by dateDue (desc)
  }
  byTaskAndUser: {
    [taskIdAndUserId: string]: TrainingTaskReport // will be the single latest report
  }
}

export const useTrainingTaskReports = (selectedVesselId: string | undefined, priority: number) => {
  const trainingTasks = sharedState.trainingTasks.use(priority)
  const today = sharedState.today.use()!
  const users = sharedState.users.use(priority)
  const [trainingTaskReports, setTrainingTaskReports] = useState<TrainingTaskReportsData>()

  useEffect(() => {
    setTrainingTaskReports(undefined)
    if (priority && selectedVesselId && trainingTasks && users && canView('crewTraining')) {
      return onSnapshot(
        query(
          collection(firestore, 'trainingTaskReports'),
          where('vesselId', '==', selectedVesselId),
          where('state', '==', 'active'),
          orderBy('dateDue', 'desc')
        ),
        snap => {
          const reports = snap.docs.map(doc => {
            return {
              id: doc.id,
              ...doc.data(),
            } as TrainingTaskReport
          })
          const byId = {} as {
            [reportId: string]: TrainingTaskReport
          }
          const byTaskId = {} as {
            [taskId: string]: TrainingTaskReport[]
          }
          const byTaskAndUser = {} as {
            [taskIdAndUserId: string]: TrainingTaskReport
          }
          const byUserId = {} as {
            [userId: string]: TrainingTaskReport[]
          }
          const trainingUsers = [] as UserType[]

          reports.forEach((report: TrainingTaskReport) => {
            if (trainingTasks.byId[report.taskId]?.state === 'active') {
              report.dateDueDiff = Math.round(makeDateTime(report.dateDue).diff(makeDateTime(today), ['days']).days)
              byId[report.id] = report
              if (byTaskId[report.taskId] === undefined) {
                byTaskId[report.taskId] = []
              }
              byTaskId[report.taskId].push(report)
              report.completedBy?.forEach((userId: string) => {
                if (
                  users?.byId[userId]?.state === 'active' &&
                  users.byId[userId].vesselIds?.indexOf(report.vesselId) !== -1
                ) {
                  if (byUserId[userId] === undefined) {
                    byUserId[userId] = []
                    trainingUsers.push(users.byId[userId])
                  }
                  byUserId[userId].push(report)
                  if (byTaskAndUser[`${report.taskId}${userId}`] === undefined) {
                    byTaskAndUser[`${report.taskId}${userId}`] = report
                  }
                }
              })
            }
          })

          trainingUsers.sort((a, b) => {
            return renderFullName(a).localeCompare(renderFullName(b))
          })

          setTrainingTaskReports({
            all: reports, // all reports ordered by dateDue (desc)
            users: trainingUsers, // users that have completed at least one task, sorted alphabetically
            byId, //
            byTaskId, // all reports for a given task, ordered by dateDue (desc)
            byUserId, // lists of reports ordered by dateDue (desc)
            byTaskAndUser, // will be set to the latest report
          })
        },
        error => {
          // This should be very rare
          console.log('Failed to access training task reports ', error)
        }
      )
    }
  }, [trainingTasks, users, selectedVesselId, priority, today])

  return trainingTaskReports
}
