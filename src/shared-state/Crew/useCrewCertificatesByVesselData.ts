import { sharedState } from '@src/shared-state/shared-state'
import { useMemo } from 'react'
import { UserType } from '@src/shared-state/Core/user'
import { CrewCertificate } from '@src/shared-state/Crew/crewCertificates'

export const useCrewCertificatesByVesselData = (
  selectedVesselIds: string[],
  filteredUsers?: {
    list: UserType[]
    byId: {
      [userId: string]: UserType
    }
  }
) => {
  const crewCertificates = sharedState.crewCertificates.use()

  const prioritisedCrewCertificates = useMemo(() => {
    if (selectedVesselIds?.length === 0) {
      // Nothing to load
      return []
    }
    if (filteredUsers?.byId && crewCertificates) {
      const array: CrewCertificate[] = []
      for (let i = 0; i < crewCertificates.numHighestPriority; i++) {
        const certificate = crewCertificates.prioritised[i]
        if (!filteredUsers.byId[certificate.heldBy]) {
          continue
        }
        array.push(certificate)
      }
      return array
    }
    return undefined
  }, [selectedVesselIds?.length, filteredUsers?.byId, crewCertificates])

  return prioritisedCrewCertificates
}
