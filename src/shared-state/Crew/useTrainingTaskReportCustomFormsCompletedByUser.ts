import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { useEffect, useState } from 'react'
import { registerFiles } from '@src/shared-state/FileSyncSystem/filesToCache'
import { CustomFormCompleted } from '@src/shared-state/CompanyDocuments/CustomForms/useCustomFormsCompleted'
import { sharedState } from '@src/shared-state/shared-state'

export type TrainingTaskReportCustomFormsCompletedData = {
  byId: { [id: string]: CustomFormCompleted }
  byTaskId: { [id: string]: CustomFormCompleted[] }
  byReportId: { [id: string]: CustomFormCompleted[] }
  all: CustomFormCompleted[]
}

export const useTrainingTaskReportCustomFormsCompletedByUser = (personnelId?: string) => {
  const [trainingTaskReportCustomFormsCompleted, setTrainingTaskReportCustomFormsCompleted] =
    useState<TrainingTaskReportCustomFormsCompletedData>()
  const licenseeId = sharedState.licenseeId.current

  useEffect(() => {
    setTrainingTaskReportCustomFormsCompleted(undefined)
    if (licenseeId && personnelId) {
      const collectionRef = collection(firestore, 'customFormsCompleted')
      const queryConstraints = [
        where('licenseeId', '==', licenseeId),
        where('personnelIds', 'array-contains', personnelId),
        where('state', '==', 'active'),
        where('attachTo', '==', 'trainingTaskReport'),
        orderBy('whenAdded', 'desc'),
      ]

      const q = query(collectionRef, ...queryConstraints)

      return onSnapshot(
        q,
        snap => {
          const byId = {} as {
            [id: string]: CustomFormCompleted
          }
          const byReportId = {} as {
            [id: string]: CustomFormCompleted[]
          }
          const byTaskId = {} as {
            [id: string]: CustomFormCompleted[]
          }
          const all = snap.docs.map(doc => {
            const o = {
              id: doc.id,
              ...doc.data(),
            } as CustomFormCompleted
            byId[o.id] = o
            if (o.attachToId) {
              if (byReportId[o.attachToId] === undefined) {
                byReportId[o.attachToId] = []
              }
              byReportId[o.attachToId].push(o)
            }
            if (o.attachToTrainingTaskId) {
              if (byTaskId[o.attachToTrainingTaskId] === undefined) {
                byTaskId[o.attachToTrainingTaskId] = []
              }
              byTaskId[o.attachToTrainingTaskId].push(o)
            }
            registerFiles(o.files, 'customFormsCompleted', o)
            return o
          })
          setTrainingTaskReportCustomFormsCompleted({
            byId,
            byReportId,
            byTaskId,
            all,
          })
        },
        error => {
          // This should be very rare
          console.log(`Failed to access trainingTaskReport custom forms for personnelId ${personnelId} `, error)
        }
      )
    }
  }, [personnelId, licenseeId])

  return trainingTaskReportCustomFormsCompleted
}
