import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { sharedState } from '@src/shared-state/shared-state'
import { useEffect, useState } from 'react'
import { TrainingTaskReport } from './useTrainingTaskReports'
import { UserType } from '@src/shared-state/Core/user'
import { registerFiles } from '@src/shared-state/FileSyncSystem/filesToCache'

export type UserTrainingTaskReportsData = TrainingTaskReport[]

export const useUserTrainingTaskReports = (selectedUser: UserType | undefined) => {
  const licenseeId = sharedState.licenseeId.use()
  const [userTrainingTaskReports, setUserTrainingTaskReports] = useState<TrainingTaskReport[]>()

  useEffect(() => {
    setUserTrainingTaskReports(undefined)
    if (licenseeId && selectedUser) {
      return onSnapshot(
        query(
          collection(firestore, 'trainingTaskReports'),
          where('state', '==', 'active'),
          where('licenseeId', '==', licenseeId),
          where('completedBy', 'array-contains', selectedUser.id),
          orderBy('dateDue', 'desc')
        ),
        snap => {
          const all = snap.docs.map(doc => {
            registerFiles(doc.data().files, 'trainingTaskReports', doc.data())
            return {
              id: doc.id,
              ...doc.data(),
            } as TrainingTaskReport
          })

          all.sort((a, b) => {
            return b.dateDue.localeCompare(a.dateDue)
          })

          setUserTrainingTaskReports(all)
        },
        error => {
          // This should be very rare
          console.log('Failed to access training task reports ', error)
        }
      )
    }
  }, [selectedUser, licenseeId])

  return userTrainingTaskReports
}
