import { SharedStateConfig, sharedState } from '@src/shared-state/shared-state'
import { CategoriesData, onCategoriesSnapshot } from '@src/lib/categories'

//
// Loads contactCategories
//

export const contactCategoriesConfig: SharedStateConfig<CategoriesData> = {
  isAlwaysActive: false,
  dependencies: ['licenseeId'],
  countLiveDocs: () => sharedState.contactCategories.current?.ids.length ?? 0,
  run: (done, set, clear) => {
    clear()
    const licenseeId = sharedState.licenseeId.current
    if (licenseeId) {
      return onCategoriesSnapshot(
        'contactCategories',
        'licenseeId',
        licenseeId,
        data => {
          // onLoaded
          done()
          set(data)
        },
        error => {
          done()
          clear()
          console.log(`Error getting contactCategories`, error)
        }
      )
    }
  },
}
