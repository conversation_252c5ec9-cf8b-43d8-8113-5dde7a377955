import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { CreateableDocument, UpdateableDocument, sharedState } from '@src/shared-state/shared-state'
import { UserType } from '@src/shared-state/Core/user'
import { useEffect, useState } from 'react'
import { registerFiles } from '@src/shared-state/FileSyncSystem/filesToCache'

export interface UserDocument extends CreateableDocument, UpdateableDocument {
  deletedBy?: string
  files: string[]
  licenseeId: string
  name: string
  state: string
  userId: string
  vesselIds?: string[]
  whenDeleted?: number
}

export type CrewDocumentsData = {
  byId: { [id: string]: UserDocument }
  all: UserDocument[]
}

export const useCrewDocuments = (selectedUser?: UserType) => {
  const licenseeId = sharedState.licenseeId.current
  const [crewDocuments, setCrewDocuments] = useState<CrewDocumentsData>()

  useEffect(() => {
    setCrewDocuments(undefined)
    if (selectedUser && licenseeId) {
      return onSnapshot(
        query(
          collection(firestore, 'userDocuments'),
          where('licenseeId', '==', licenseeId),
          where('state', '==', 'active'),
          where('userId', '==', selectedUser.id),
          orderBy('whenAdded', 'desc')
        ),
        snap => {
          const byId = {} as {
            [id: string]: UserDocument
          }
          const all = snap.docs.map(doc => {
            const o = {
              id: doc.id,
              ...doc.data(),
            } as UserDocument
            byId[o.id] = o
            registerFiles(o.files, 'userDocuments', o)
            return o
          })
          setCrewDocuments({
            byId,
            all,
          })
        },
        error => {
          // This should be very rare
          console.log(`Failed to access crew documents for user ${selectedUser.id} `, error)
        }
      )
    }
  }, [selectedUser, licenseeId])

  return crewDocuments
}
