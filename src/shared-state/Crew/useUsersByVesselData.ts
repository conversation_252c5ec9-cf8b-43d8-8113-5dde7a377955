import { sharedState } from '@src/shared-state/shared-state'
import { useMemo } from 'react'
import { UserType } from '@src/shared-state/Core/user'

export const useUsersByVesselData = (selectedVesselIds: string[]) => {
  const users = sharedState.users.use()

  const filteredUsers = useMemo(() => {
    if (users && selectedVesselIds?.length) {
      const hasVesselId: {
        [vesselId: string]: boolean
      } = {}
      selectedVesselIds.forEach((vesselId: string) => {
        hasVesselId[vesselId] = true
      })

      const list: UserType[] = []
      const byId: {
        [userId: string]: UserType
      } = {}

      const processUsers = (_users: UserType[]) => {
        for (let i = 0; i < _users.length; i++) {
          const u = _users[i]
          if (u.state !== 'active') {
            continue
          }
          if (u.vesselIds && u.id) {
            for (let j = 0; j < u.vesselIds.length; j++) {
              if (hasVesselId[u.vesselIds[j]]) {
                list.push(u)
                byId[u.id] = u
                break
              }
            }
          }
        }
      }

      processUsers(users?.staff)
      processUsers(users?.nonStaff)

      list.sort((a, b) => {
        if (!a.firstName && !b.firstName) {
          if (!a.lastName && !b.lastName) return 0
          if (!a.lastName) return 1
          if (!b.lastName) return -1
          return a.lastName.localeCompare(b.lastName)
        }
        if (!a.firstName) return 1
        if (!b.firstName) return -1

        if (a.firstName === b.firstName) {
          if (!a.lastName && !b.lastName) return 0
          if (!a.lastName) return 1
          if (!b.lastName) return -1
          return a.lastName.localeCompare(b.lastName)
        }
        return a.firstName.localeCompare(b.firstName)
      })
      return {
        list,
        byId,
      }
    }
    return undefined
  }, [users, selectedVesselIds])

  return filteredUsers
}
