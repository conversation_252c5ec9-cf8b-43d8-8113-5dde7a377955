import { Platform } from 'react-native'
import { sharedState, SharedStateConfig } from '@src/shared-state/shared-state'
import * as Filesystem from 'expo-file-system'

export const mb = 1048576
export const minSpaceOk = 500 * mb // 500MB

export type DiskSpaceStatus = {
  bytesFree: number
  haveEnoughSpace: boolean
}

let alreadyRunning = false

export const diskSpaceStatusConfig: SharedStateConfig<DiskSpaceStatus> = {
  isAlwaysActive: true,
  dependencies: ['appState', 'fileSyncStatus', 'dataSyncStatus'],
  default: {
    bytesFree: 0,
    haveEnoughSpace: true,
    // bytesFree: 32189745,
    // haveEnoughSpace: false
  },
  run: (done, set, clear) => {
    done()
    if (alreadyRunning) {
      return
    }
    if (sharedState.appState.current?.isActive) {
      alreadyRunning = true
      setTimeout(() => {
        // Limit diskSpaceStatus polling to a maximum frequency of once per second
        if (Platform.OS === 'android' || Platform.OS === 'ios') {
          Filesystem.getFreeDiskStorageAsync()
            .then(freeSpace => {
              console.log(`diskSpaceStatusConfig: Free disk space:`, freeSpace)
              if (freeSpace !== null) {
                set({
                  bytesFree: freeSpace,
                  haveEnoughSpace: freeSpace >= minSpaceOk,
                })
              } else {
                clear()
              }
              alreadyRunning = false
            })
            .catch(error => {
              console.error('Error getting disk space:', error)
              clear()
              alreadyRunning = false
            })
        } else {
          clear()
          alreadyRunning = false
        }
      }, 1000)
    }
  },
}
