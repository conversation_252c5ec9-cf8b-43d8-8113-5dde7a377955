import {
  DocumentData,
  QueryDocumentSnapshot,
  collection,
  onSnapshot,
  query,
  where,
} from '@src/lib/firebase/services/firestore.service'
import { firestore, setupArrayQueryListener } from '@src/lib/firebase'
import { CreateableDocument, SharedStateConfig, sharedState } from '@src/shared-state/shared-state'
import { CustomForm } from './customForms'
import { registerFiles } from '@src/shared-state/FileSyncSystem/filesToCache'
import { canView } from '@src/shared-state/Core/userPermissions'

export interface CustomFormVersion extends CreateableDocument {
  customFormId: string
  deletedBy?: string
  forVesselIds: string[]
  form: CustomForm
  historyElementN: number
  licenseeId: string
  numCompleted: number
  state: string
  version: number
  whenDeleted?: number
}

export type CustomFormVersionsData = {
  byFormIdAndVersion: Record<string, Record<number, CustomFormVersion>>
  count: number
}

/**
 * Load customFormVersions
 */
export const customFormVersionsConfig: SharedStateConfig<CustomFormVersionsData> = {
  isAlwaysActive: true, // customFormVersions always needs to be loaded because dataSyncTasks.processTaskQueryResults needs them
  notes: 'Always needs to be loaded for fileSync file registration to work',
  dependencies: ['licenseeId', 'vesselIds'],
  countLiveDocs: () => sharedState.customFormVersions.current?.count ?? 0,
  run: (done, set, clear) => {
    // clear(); <-- commented this out for the same reason isAlwaysActive is set to true
    const licenseeId = sharedState.licenseeId.current
    const vesselIds = sharedState.vesselIds.current
    if (licenseeId && vesselIds && vesselIds.length > 0) {
      const processDocs = (docs: QueryDocumentSnapshot<DocumentData>[]) => {
        const _customFormVersions = docs.map(doc => {
          return {
            id: doc.id,
            ...doc.data(),
          } as CustomFormVersion
        })

        const byFormIdAndVersion = {} as Record<string, Record<number, CustomFormVersion>>
        _customFormVersions.forEach((customFormVersion: CustomFormVersion) => {
          byFormIdAndVersion[customFormVersion.customFormId] ??= {} as Record<number, CustomFormVersion>
          byFormIdAndVersion[customFormVersion.customFormId][customFormVersion.version] = customFormVersion

          for (const key in customFormVersion.form) {
            if (customFormVersion.form.hasOwnProperty(key) && customFormVersion.form[key]?.help?.files) {
              registerFiles(customFormVersion.form[key].help.files, 'customFormVersions', customFormVersion)
            }
          }
        })

        set({
          byFormIdAndVersion,
          count: _customFormVersions.length,
        })
      }

      if (canView('crewParticulars')) {
        // If I can view crewParticulars I need access to ALL customFormVersions across the licensee
        return onSnapshot(
          query(
            collection(firestore, 'customFormVersions'),
            where('licenseeId', '==', licenseeId),
            where('state', 'in', ['active', 'draft'])
          ),
          snap => {
            done()
            processDocs(snap.docs)
          },
          error => {
            done()
          }
        )
      } else {
        return setupArrayQueryListener(
          'customFormVersions', // what
          collection(firestore, 'customFormVersions'),
          [where('state', 'in', ['active', 'draft']), where('licenseeId', '==', licenseeId)],
          'forVesselIds',
          'array-contains-any',
          ['none', ...vesselIds],
          [], // orderByConstraints
          (
            docs: QueryDocumentSnapshot<DocumentData>[],
            isCombined: boolean // (since there is no orderBy, this is irrelevant)
          ) => {
            // processDocs
            done()
            processDocs(docs)
          },
          error => {
            done()
          }
        )
      }
    } else {
      done()
    }
  },
}
