import {
  DocumentData,
  QueryDocumentSnapshot,
  QueryOrderByConstraint,
  collection,
  orderBy,
  where,
} from '@src/lib/firebase/services/firestore.service'
import { firestore, setupArrayQueryListener } from '@src/lib/firebase'
import { CreateableDocument, SharedStateConfig, UpdateableDocument, sharedState } from '@src/shared-state/shared-state'
import { CustomFormElementType } from '@src/components/_atoms/SeaCustomFormElement/SeaCustomFormElement'

export interface CustomForm extends CreateableDocument, UpdateableDocument {
  categoryId: string
  crewElement?: CustomFormElementType
  deletedBy?: string
  forCrew: boolean
  forVesselIds: string[]
  isTemplate?: boolean
  lastCompletedBy?: string
  lastCompletedVesselIds?: string[]
  latestVersion: number
  licenseeId: string
  state: string
  templateCategory?: string
  title: string
  vesselsElement?: CustomFormElementType
  whenDeleted?: number
  whenLastCompleted?: number
  [key: string]: any
}

export type CustomFormsData = {
  all: CustomForm[]
  byId: Record<string, CustomForm>
  byCategoryId: Record<string, CustomForm[]>
}

/**
 * Load customForms
 */
export const customFormsConfig: SharedStateConfig<CustomFormsData> = {
  isAlwaysActive: false,
  dependencies: ['licenseeId', 'vesselIds'],
  countLiveDocs: () => sharedState.customForms.current?.all.length ?? 0,
  run: (done, set, clear) => {
    clear()
    const licenseeId = sharedState.licenseeId.current
    const vesselIds = sharedState.vesselIds.current
    if (licenseeId && vesselIds && vesselIds.length > 0) {
      return setupArrayQueryListener(
        'customForms', // what
        collection(firestore, 'customForms'),
        [where('state', 'in', ['active', 'draft']), where('licenseeId', '==', licenseeId)], // baseConstraints
        'forVesselIds',
        'array-contains-any',
        ['none', ...vesselIds],
        [orderBy('title', 'asc') as QueryOrderByConstraint],
        (docs: QueryDocumentSnapshot<DocumentData>[], isCombined: boolean) => {
          // processDocs
          done()
          const all = docs.map(doc => {
            return {
              id: doc.id,
              ...doc.data(),
            } as CustomForm
          })

          if (isCombined) {
            // Need to sort by name
            all.sort((a, b) => {
              return a.title.localeCompare(b.title)
            })
          }

          const byId = {} as Record<string, CustomForm>

          const byCategoryId = {} as Record<string, CustomForm[]>
          all.forEach(customForm => {
            byId[customForm.id] = customForm
            byCategoryId[customForm.categoryId] ??= []
            byCategoryId[customForm.categoryId].push(customForm)
          })

          set({
            all,
            byId,
            byCategoryId,
          })
        },
        error => {
          // onError
          done()
        }
      )
    } else {
      done()
    }
  },
}
