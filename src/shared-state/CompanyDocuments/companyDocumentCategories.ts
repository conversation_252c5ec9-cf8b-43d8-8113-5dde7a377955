import { SharedStateConfig, sharedState } from '@src/shared-state/shared-state'
import { CategoriesData, onCategoriesSnapshot } from '@src/lib/categories'

/**
 * Loads companyDocumentCategories
 */
export const companyDocumentCategoriesConfig: SharedStateConfig<CategoriesData> = {
  isAlwaysActive: false,
  dependencies: ['licenseeId'],
  countLiveDocs: () => sharedState.companyDocumentCategories.current?.ids.length ?? 0,
  run: (done, set, clear) => {
    clear()
    const licenseeId = sharedState.licenseeId.current
    if (licenseeId) {
      return onCategoriesSnapshot(
        'companyDocumentCategories',
        'licenseeId',
        licenseeId,
        data => {
          done()
          set(data)
        },
        error => {
          done()
          console.log(`Error getting companyDocumentCategories`, error)
        }
      )
    }
  },
}
