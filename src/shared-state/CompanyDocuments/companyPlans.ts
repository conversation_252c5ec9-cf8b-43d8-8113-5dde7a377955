import { doc, DocumentData, DocumentSnapshot, onSnapshot } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { CreateableDocument, SharedStateConfig, UpdateableDocument, sharedState } from '@src/shared-state/shared-state'
import { canView } from '@src/shared-state/Core/userPermissions'
import { SFDoc } from './companyDocuments'
import { registerRichText } from '@src/shared-state/FileSyncSystem/filesToCache'

export interface CompanyPlan extends CreateableDocument, UpdateableDocument {
  emailReminder?: string
  interval?: string
  dateLastReviewed?: string
  sfdoc: SFDoc
  dateDue?: string
  dateToRemind?: string
}

/**
 * Loads the company plan
 */
export const companyPlanConfig: SharedStateConfig<CompanyPlan> = {
  isAlwaysActive: false,
  dependencies: ['licenseeId', 'userPermissions'],
  countLiveDocs: () => (sharedState.companyPlan.current ? 1 : 0),
  run: (done, set, clear) => {
    clear()
    const licenseeId = sharedState.licenseeId.current
    if (licenseeId && canView('companyPlan')) {
      return onSnapshot(
        doc(firestore, 'companyPlans', licenseeId),
        (doc: DocumentSnapshot<DocumentData>) => {
          done()
          const companyPlan = doc.data() as CompanyPlan
          registerRichText(companyPlan?.sfdoc, 'companyPlans')
          set(companyPlan)
        },
        error => {
          done()
          console.log(`Error getting companyPlan`, error)
        }
      )
    }
  },
}
