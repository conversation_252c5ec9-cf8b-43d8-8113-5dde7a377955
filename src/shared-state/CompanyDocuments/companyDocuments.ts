import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { CreateableDocument, SharedStateConfig, UpdateableDocument, sharedState } from '@src/shared-state/shared-state'
import { canView } from '@src/shared-state/Core/userPermissions'
import { getDayOffset, MAX_DATE, warnDays } from '@src/lib/datesAndTime'
import { registerFiles, registerRichText } from '@src/shared-state/FileSyncSystem/filesToCache'

export interface SFDoc {
  id: string
  state: string
  ext: string
  [key: string | number]: any
}

export interface CompanyDocument extends CreateableDocument, UpdateableDocument {
  categoryId: string
  deletedBy?: string
  emailReminder?: string
  files?: string[]
  interval?: string
  licenseeId: string
  name?: string
  sfdoc?: SFDoc
  state: string
  title: string
  type: string
  whenDeleted?: number
  dateExpires?: string
  dateToRemind?: string
}

export type CompanyDocumentsData = {
  all: CompanyDocument[] // ordered by title
  prioritised: CompanyDocument[] // ordered dateExpires asc, renewable first
  top5: CompanyDocument[]
  byId: Record<string, CompanyDocument>
  byCategoryId: Record<string, CompanyDocument[]>
  numHighestPriority: number
}

/**
 * Loads company documents
 */
export const companyDocumentsConfig: SharedStateConfig<CompanyDocumentsData> = {
  isAlwaysActive: false,
  dependencies: ['licenseeId', 'todayMillis', 'userPermissions'], // todayMillis is used due to calculating around days
  countLiveDocs: () => sharedState.companyDocuments.current?.all.length ?? 0,
  run: (done, set, clear) => {
    clear()
    const licenseeId = sharedState.licenseeId.current

    if (licenseeId && canView('companyDocuments')) {
      return onSnapshot(
        query(
          collection(firestore, 'companyDocuments'),
          where('licenseeId', '==', licenseeId),
          where('state', '==', 'active'),
          orderBy('title', 'asc')
        ),
        snap => {
          done()
          const documents = snap.docs.map(doc => {
            return {
              id: doc.id,
              ...doc.data(),
            } as CompanyDocument
          })

          // all is categorised by renewable and nonExpiring
          const all = [] as CompanyDocument[]
          const byId = {} as Record<string, CompanyDocument>
          documents.forEach((document: CompanyDocument) => {
            registerFiles(document.files, 'companyDocuments', document)
            registerRichText(document.sfdoc, 'companyDocuments')
            byId[document.id] = document
            all.push(document)
          })
          let prioritised = [...documents] as CompanyDocument[]
          prioritised.sort((a, b) => {
            return ((a.type === 'renewable' ? a.dateExpires : MAX_DATE) ?? MAX_DATE).localeCompare(
              (b.type === 'renewable' ? b.dateExpires : MAX_DATE) ?? MAX_DATE
            )
          })

          // prioritised should only contain dateExpires up to warnDays.vesselDocuments days in the future
          // (and should not contain an nonExpiring either)
          const maxDateExpires = getDayOffset(warnDays.vesselDocuments[warnDays.vesselDocuments.length - 1] - 1)
          const minDateExpires = getDayOffset(warnDays.vesselDocuments[0] - 1)
          let numHighestPriority = 0
          for (let i = 0; i < prioritised.length; i++) {
            if (
              prioritised[i].dateExpires &&
              prioritised[i].dateExpires! < minDateExpires &&
              prioritised[i].type !== 'nonExpiring'
            ) {
              numHighestPriority++
            }
            if (
              prioritised[i].type === 'nonExpiring' ||
              (prioritised[i].dateExpires && prioritised[i].dateExpires! > maxDateExpires)
            ) {
              prioritised = prioritised.slice(0, i)
              break
            }
          }

          const byCategoryId = {} as Record<string, CompanyDocument[]>

          all.forEach((companyDocument: CompanyDocument) => {
            byId[companyDocument.id] = companyDocument
            byCategoryId[companyDocument.categoryId] ??= []
            byCategoryId[companyDocument.categoryId].push(companyDocument)
          })
          const top5 = prioritised.slice(0, 5).filter(p => p.dateExpires && p.dateExpires < minDateExpires)
          set({
            all,
            prioritised,
            top5,
            byId,
            byCategoryId,
            numHighestPriority,
          })
        },
        error => {
          // This should be very rare
          done()
          console.log(`Failed to access company documents`, error)
        }
      )
    }
  },
}
