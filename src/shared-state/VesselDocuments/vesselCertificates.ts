import {
  ArchivableDocument,
  CreateableDocument,
  SharedStateConfig,
  UpdateableDocument,
  sharedState,
} from '@src/shared-state/shared-state'
import { canView } from '@src/shared-state/Core/userPermissions'
import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { getDayOffset, MAX_DATE, warnDays } from '@src/lib/datesAndTime'
import { registerFiles } from '@src/shared-state/FileSyncSystem/filesToCache'

export interface VesselCertificate extends CreateableDocument, UpdateableDocument, ArchivableDocument {
  certNum?: string
  deletedBy?: string
  emailReminder?: string
  files?: string[]
  isShoreFacility?: boolean
  issuedBy?: string
  state: string
  title: string
  type: string
  vesselId: string
  categoryId: string
  wasRenewed?: boolean
  whenDeleted?: number
  dateExpires?: string
  dateIssued: string
  dateToRemind?: string
}

export type VesselCertificatesData = {
  all: VesselCertificate[]
  prioritised: VesselCertificate[]
  byId: {
    [id: string]: VesselCertificate
  }
  byCategoryId: {
    [categoryId: string]: VesselCertificate[]
  }
}

export const vesselCertificatesConfig: SharedStateConfig<VesselCertificatesData> = {
  isAlwaysActive: false,
  dependencies: ['vesselId', 'todayMillis', 'userPermissions'], // Depends on todayMillis because we're doing day offset calculations
  countLiveDocs: () => Object.keys(sharedState.vesselCertificates.current?.byId ?? {}).length,
  run: (done, set, clear) => {
    clear()
    const vesselId = sharedState.vesselId.current
    if (vesselId && canView('vesselCertificates')) {
      return onSnapshot(
        query(
          collection(firestore, 'vesselCertificates'),
          where('vesselId', '==', vesselId),
          where('state', '==', 'active'),
          orderBy('title', 'asc')
        ),
        snap => {
          done()
          const certificates = snap.docs.map(doc => {
            return {
              id: doc.id,
              ...doc.data(),
            } as VesselCertificate
          })

          // all is categorised by renewable and nonExpiring
          const all = [] as VesselCertificate[]
          const byId = {} as {
            [id: string]: VesselCertificate
          }
          const byCategoryId: {
            [categoryId: string]: VesselCertificate[]
          } = {}
          certificates.forEach(certificate => {
            registerFiles(certificate.files, 'vesselCertificates', certificate)
            byId[certificate.id] = certificate
            if (!byCategoryId[certificate.categoryId]) {
              byCategoryId[certificate.categoryId] = []
            }
            byCategoryId[certificate.categoryId].push(certificate)
            all.push(certificate)
          })
          let prioritised = [...certificates] as VesselCertificate[]
          prioritised.sort((a: VesselCertificate, b: VesselCertificate) => {
            return (a.type === 'renewable' ? (a.dateExpires ?? MAX_DATE) : MAX_DATE).localeCompare(
              b.type === 'renewable' ? (b.dateExpires ?? MAX_DATE) : MAX_DATE
            )
          })

          // prioritised should only contain dateExpires up to a set amount of days in the future
          // (and should not contain and nonExpiring either)
          const maxDateExpires = getDayOffset(warnDays.vesselCertificates[warnDays.vesselCertificates.length - 1])

          for (let i = 0; i < prioritised.length; i++) {
            if (prioritised[i].type === 'nonExpiring' || (prioritised[i].dateExpires || 0) >= maxDateExpires) {
              prioritised = prioritised.slice(0, i)
              break
            }
          }

          set({
            all,
            prioritised,
            byId,
            byCategoryId,
          })
        },
        error => {
          done()
          // This should be very rare
          console.log(`Failed to access vesselCertificates for vessel ${vesselId}`, error)
        }
      )
    }
  },
}
