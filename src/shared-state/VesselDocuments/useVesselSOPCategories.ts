import { useState, useEffect } from 'react'
import { CategoriesData, onCategoriesSnapshot } from '@src/lib/categories'
import { sharedState } from '@src/shared-state/shared-state'

/**
 * Returns vesselSOPCategories for the selectedVesselId.
 * If selectedVesselId matches sharedState.vesselId we can just use sharedState.vesselSOPCategories
 */
export const useVesselSOPCategories = (selectedVesselId: string | undefined) => {
  const isActive = selectedVesselId ? true : false
  const vesselId = sharedState.vesselId.use(isActive)
  const vesselSOPCategories = sharedState.vesselSOPCategories.use(isActive)
  const [otherVesselSOPCategories, setOtherVesselSOPCategories] = useState<CategoriesData>()

  useEffect(() => {
    setOtherVesselSOPCategories(undefined)
    if (selectedVesselId && (vesselId === undefined || vesselId !== selectedVesselId)) {
      // We need to load vesselSOPCategories for a vesselId other than sharedState.vesselId
      return onCategoriesSnapshot(
        'vesselSopCategories',
        'vesselId',
        selectedVesselId,
        data => {
          // onLoaded
          setOtherVesselSOPCategories(data)
        },
        error => {
          // onError
          console.log(`Error getting vesselSopCategories for vesselId=${selectedVesselId}`, error)
        }
      )
    }
  }, [selectedVesselId, vesselId])

  if (vesselId && vesselId === selectedVesselId) {
    // We have already loaded via sharedState.vesselSOPCategories
    return vesselSOPCategories
  }
  return otherVesselSOPCategories
}
