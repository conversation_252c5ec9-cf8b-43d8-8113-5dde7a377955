import { SharedStateConfig, sharedState } from '@src/shared-state/shared-state'
import { CategoriesData, onCategoriesSnapshot } from '@src/lib/categories'

/**
 * Loads vesselDocumentCategories
 */
export const vesselDocumentCategoriesConfig: SharedStateConfig<CategoriesData> = {
  isAlwaysActive: false,
  dependencies: ['vesselId'],
  countLiveDocs: () => sharedState.vesselDocumentCategories.current?.ids.length ?? 0,
  run: (done, set, clear) => {
    clear()
    const vesselId = sharedState.vesselId.current
    if (vesselId) {
      return onCategoriesSnapshot(
        'vesselDocumentCategories',
        'vesselId',
        vesselId,
        (data: CategoriesData) => {
          // onLoaded
          done()
          set(data)
        },
        error => {
          // onError
          done()
          console.log(`Error getting vesselDocumentCategories`, error)
        }
      )
    } else {
      done()
    }
  },
}
