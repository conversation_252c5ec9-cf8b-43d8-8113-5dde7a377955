import { canView } from '@src/shared-state/Core/userPermissions'
import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { VesselDocument } from './vesselDocuments'
import { useEffect, useState } from 'react'
import { useVesselDocumentCategories } from './useVesselDocumentCategories'

export type VesselDocumentsByCategory = {
  categories: {
    id: string
    name: string
  }[] // Sorted by category name
  byCategoryId: {
    [categoryId: string]: VesselDocument[] // Sorted by name
  }
}

export const useVesselDocumentsByCategory = (isActive?: boolean, selectedVesselId?: string) => {
  const [vesselDocuments, setVesselDocuments] = useState<VesselDocumentsByCategory>()
  const vesselDocumentCategories = useVesselDocumentCategories(isActive ? selectedVesselId : undefined)

  useEffect(() => {
    setVesselDocuments(undefined)
    if (isActive && selectedVesselId && vesselDocumentCategories && canView('vesselDocuments')) {
      return onSnapshot(
        query(
          collection(firestore, 'vesselDocuments'),
          where('vesselId', '==', selectedVesselId),
          where('state', '==', 'active'),
          orderBy('title', 'asc')
        ),
        snap => {
          const categories = [] as {
            id: string
            name: string
          }[]
          const byCategoryId = {} as {
            [categoryId: string]: VesselDocument[]
          }

          snap.docs.forEach(doc => {
            const document = {
              id: doc.id,
              ...doc.data(),
            } as VesselDocument

            if (document.categoryId) {
              if (byCategoryId[document.categoryId] === undefined) {
                byCategoryId[document.categoryId] = []
                categories.push({
                  id: document.categoryId,
                  name: vesselDocumentCategories.byId[document.categoryId].name,
                })
              }
              byCategoryId[document.categoryId].push(document)
            }
          })

          // Sort categories
          categories.sort((a, b) => {
            return a.name.localeCompare(b.name)
          })

          setVesselDocuments({
            categories,
            byCategoryId,
          })
        },
        error => {
          console.error(`Failed to access useVesselDocumentsByCategory ${selectedVesselId}`, error)
          setVesselDocuments(undefined)
        }
      )
    }
  }, [isActive, selectedVesselId, vesselDocumentCategories])

  return vesselDocuments
}
