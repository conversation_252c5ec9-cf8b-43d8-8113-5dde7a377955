import { onSnapshot, query, collection, where, orderBy } from '@src/lib/firebase/services/firestore.service'
import { CreateableDocument, SharedStateConfig, UpdateableDocument, sharedState } from '@src/shared-state/shared-state'
import { registerFiles, registerRichText } from '@src/shared-state/FileSyncSystem/filesToCache'
import { firestore } from '@src/lib/firebase'
import { canView } from '@src/shared-state/Core/userPermissions'
import { SFDoc } from '@src/shared-state/CompanyDocuments/companyDocuments'

export interface SOP extends CreateableDocument, UpdateableDocument {
  categoryId: string
  deletedBy?: string
  files?: string[]
  sfdoc?: SFDoc
  state: string
  title: string
  vesselId: string
  whenDeleted?: number
  dateIssued: string
}

export type VesselSOPsData = {
  byId: {
    [id: string]: SOP
  }
  byCategoryId: {
    [id: string]: SOP[]
  }
  categoryIds: string[]
}

export const vesselSOPsConfig: SharedStateConfig<VesselSOPsData> = {
  isAlwaysActive: false,
  dependencies: ['vesselId', 'vesselSOPCategories', 'userPermissions'],
  countLiveDocs: () => Object.keys(sharedState.vesselSOPs.current?.byId ?? {}).length,
  run: (done, set, clear) => {
    clear()
    const vesselId = sharedState.vesselId.current
    const vesselSOPCategories = sharedState.vesselSOPCategories.current

    if (vesselId && vesselSOPCategories && canView('standardOperatingProcedures')) {
      return onSnapshot(
        query(
          collection(firestore, 'SOPs'),
          where('vesselId', '==', vesselId),
          where('state', '==', 'active'),
          orderBy('title', 'asc')
        ),
        snap => {
          done()
          const items = snap.docs.map(
            doc =>
              ({
                id: doc.id,
                ...doc.data(),
              }) as SOP
          )

          const byId = {} as {
            [id: string]: SOP
          }
          const byCategoryId = {} as {
            [id: string]: SOP[]
          }

          items.forEach(sop => {
            byId[sop.id] = sop
            if (!byCategoryId[sop.categoryId]) {
              byCategoryId[sop.categoryId] = []
            }
            byCategoryId[sop.categoryId].push(sop)

            registerFiles(sop.files, 'SOPs', sop)
            registerRichText(sop.sfdoc, 'SOPs')
          })

          const categoryIds = Object.keys(vesselSOPCategories.byId).filter(categoryId => byCategoryId[categoryId])

          set({
            byId,
            byCategoryId,
            categoryIds,
          })
        },
        error => {
          console.error(`Failed to access SOPs data for vessel ${vesselId}`, error)
          clear()
        }
      )
    } else {
      done()
    }
  },
}
