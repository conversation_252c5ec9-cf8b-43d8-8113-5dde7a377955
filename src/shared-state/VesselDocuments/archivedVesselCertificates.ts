import { SharedStateConfig, sharedState } from '@src/shared-state/shared-state'
import { canView } from '@src/shared-state/Core/userPermissions'
import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { formatYear } from '@src/lib/datesAndTime'
import { VesselCertificate } from './vesselCertificates'
import { registerFiles } from '@src/shared-state/FileSyncSystem/filesToCache'

export type ArchivedVesselCertificatesData = {
  byCategory: {
    [key: string]: VesselCertificate[]
  }
  categories: string[]
}

export const archivedVesselCertificatesConfig: SharedStateConfig<ArchivedVesselCertificatesData> = {
  isAlwaysActive: false,
  dependencies: ['vesselId'],
  countLiveDocs: () => {
    let count = 0
    sharedState.archivedVesselCertificates.current &&
      Object.keys(sharedState.archivedVesselCertificates.current.byCategory).forEach(key => {
        count += (sharedState.archivedVesselCertificates.current as ArchivedVesselCertificatesData).byCategory[key]
          .length
      })
    return count
  },
  run: (done, set, clear) => {
    clear()
    const vesselId = sharedState.vesselId.current
    if (vesselId && canView('vesselCertificates')) {
      return onSnapshot(
        query(
          collection(firestore, 'vesselCertificates'),
          where('vesselId', '==', vesselId),
          where('state', '==', 'archived'),
          orderBy('whenArchived', 'desc')
        ),
        snap => {
          done()
          const _archivedVesselCertificates = snap.docs.map(doc => {
            return {
              id: doc.id,
              ...doc.data(),
            } as VesselCertificate
          })

          const byCategory = {} as {
            [key: string]: VesselCertificate[]
          }
          const categories: string[] = []
          _archivedVesselCertificates.forEach(item => {
            registerFiles(item.files, 'vesselCertificates', item, 'thumbnailsOnly')
            const dateIssued = formatYear(item.dateIssued)
            if (byCategory[dateIssued] === undefined) {
              categories.push(dateIssued)
              byCategory[dateIssued] = []
            }
            byCategory[dateIssued].push(item)
          })
          categories.sort().reverse()
          set({
            byCategory,
            categories,
          })
        },
        error => {
          done()
          // This should be very rare
          console.log(`Failed to access archivedVesselCertificates for vessel ${vesselId}`, error)
        }
      )
    }
  },
}
