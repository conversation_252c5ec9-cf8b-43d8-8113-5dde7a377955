import { onSnapshot, query, collection, where, orderBy } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { canView } from '@src/shared-state/Core/userPermissions'
import { useEffect, useState } from 'react'
import { SOP } from './vesselSOPS'
import { useVesselSOPCategories } from './useVesselSOPCategories'

export type VesselSOPsByCategory = {
  categories: {
    id: string
    name: string
  }[] // Sorted by category name
  byCategoryId: {
    [categoryId: string]: SOP[] // Sorted by name
  }
}

export const useVesselSOPsByCategory = (isActive: boolean, selectedVesselId?: string) => {
  const [vesselSOPs, setVesselSOPs] = useState<VesselSOPsByCategory>()
  const vesselSOPCategories = useVesselSOPCategories(isActive ? selectedVesselId : undefined)

  useEffect(() => {
    setVesselSOPs(undefined)
    if (isActive && selectedVesselId && vesselSOPCategories && canView('standardOperatingProcedures')) {
      return onSnapshot(
        query(
          collection(firestore, 'SOPs'),
          where('vesselId', '==', selectedVesselId),
          where('state', '==', 'active'),
          orderBy('title', 'asc')
        ),
        snap => {
          const categories = [] as {
            id: string
            name: string
          }[]
          const byCategoryId = {} as {
            [categoryId: string]: SOP[]
          }

          snap.docs.forEach(doc => {
            const sop = {
              id: doc.id,
              ...doc.data(),
            } as SOP

            if (sop.categoryId) {
              if (byCategoryId[sop.categoryId] === undefined) {
                byCategoryId[sop.categoryId] = []
                categories.push({
                  id: sop.categoryId,
                  name: vesselSOPCategories.byId[sop.categoryId].name,
                })
              }
              byCategoryId[sop.categoryId].push(sop)
            }
          })

          // Sort categories
          categories.sort((a, b) => {
            return a.name.localeCompare(b.name)
          })

          setVesselSOPs({
            categories,
            byCategoryId,
          })
        },
        error => {
          console.error(`Failed to access useVesselSOPsByCategory ${selectedVesselId}`, error)
          setVesselSOPs(undefined)
        }
      )
    }
  }, [isActive, selectedVesselId, vesselSOPCategories])

  return vesselSOPs
}
