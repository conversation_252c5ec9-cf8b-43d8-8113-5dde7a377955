import { useState, useEffect } from 'react'
import { CategoriesData, onCategoriesSnapshot } from '@src/lib/categories'
import { sharedState } from '@src/shared-state/shared-state'

/**
 * Returns vesselDocumentCategories for the selectedVesselId.
 * If selectedVesselId matches sharedState.vesselId we can just use sharedState.vesselDocumentCategories
 */
export const useVesselDocumentCategories = (selectedVesselId: string | undefined) => {
  const isActive = selectedVesselId ? true : false
  const vesselId = sharedState.vesselId.use(isActive)
  const vesselDocumentCategories = sharedState.vesselDocumentCategories.use(isActive)
  const [otherVesselDocumentCategories, setOtherVesselDocumentCategories] = useState<CategoriesData>()

  useEffect(() => {
    setOtherVesselDocumentCategories(undefined)
    if (selectedVesselId && (vesselId === undefined || vesselId !== selectedVesselId)) {
      // We need to load vesselDocumentCategories for a vesselId other than sharedState.vesselId
      return onCategoriesSnapshot(
        'vesselDocumentCategories',
        'vesselId',
        selectedVesselId,
        data => {
          // onLoaded
          setOtherVesselDocumentCategories(data)
        },
        error => {
          // onError
          console.log(`Error getting vesselDocumentCategories for vesselId=${selectedVesselId}`, error)
        }
      )
    }
  }, [selectedVesselId, vesselId])

  if (vesselId && vesselId === selectedVesselId) {
    // We have already loaded via sharedState.vesselDocumentCategories
    return vesselDocumentCategories
  }
  return otherVesselDocumentCategories
}
