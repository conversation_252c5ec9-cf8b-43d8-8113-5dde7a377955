import { SharedStateConfig, sharedState } from '@src/shared-state/shared-state'
import { CategoriesData, onCategoriesSnapshot } from '@src/lib/categories'

/**
 * Loads vesselSOPCategories
 */
export const vesselSOPCategoriesConfig: SharedStateConfig<CategoriesData> = {
  isAlwaysActive: false,
  dependencies: ['vesselId', 'userPermissions'],
  countLiveDocs: () => sharedState.vesselSOPCategories.current?.ids.length ?? 0,
  run: (done, set, clear) => {
    clear()
    const vesselId = sharedState.vesselId.current
    if (vesselId) {
      return onCategoriesSnapshot(
        'vesselSopCategories',
        'vesselId',
        vesselId,
        data => {
          done()
          set(data)
        },
        error => {
          done()
          console.log(`Error getting vesselSOPCategories`, error)
        }
      )
    } else {
      done()
    }
  },
}
