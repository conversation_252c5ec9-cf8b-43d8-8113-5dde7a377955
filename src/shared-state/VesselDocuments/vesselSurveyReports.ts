import { CreateableDocument, UpdateableDocument, SharedStateConfig, sharedState } from '@src/shared-state/shared-state'
import { canView } from '@src/shared-state/Core/userPermissions'
import { collection, onSnapshot, orderBy, query, where } from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase'
import { formatYear } from '@src/lib/datesAndTime'
import { registerFiles } from '@src/shared-state/FileSyncSystem/filesToCache'

export interface SurveyReport extends UpdateableDocument, CreateableDocument {
  deletedBy?: string
  files: string[]
  inOrOutWater: string
  location?: string
  personnelPresent?: string
  state: string
  surveyor?: string
  title: string
  vesselId: string
  whenDeleted?: number
  dateSurveyed: string
}

export type VesselSurveyReportsData = {
  all: SurveyReport[]
  data: {
    [id: string]: SurveyReport[]
  }
  byId: {
    [id: string]: SurveyReport
  }
  categories: string[]
}

export const vesselSurveyReportsConfig: SharedStateConfig<VesselSurveyReportsData> = {
  isAlwaysActive: false,
  dependencies: ['vesselId', 'userPermissions'],
  countLiveDocs: () => Object.keys(sharedState.vesselSurveyReports.current?.byId ?? {}).length,
  run: (done, set, clear) => {
    clear()
    const vesselId = sharedState.vesselId.current
    if (vesselId && canView('survey')) {
      return onSnapshot(
        query(
          collection(firestore, 'surveyReports'),
          where('vesselId', '==', vesselId),
          where('state', '==', 'active'),
          orderBy('dateSurveyed', 'desc')
        ),
        snap => {
          done()
          const _surveyReports = snap.docs.map(doc => {
            return {
              id: doc.id,
              ...doc.data(),
            } as SurveyReport
          })

          const data = {} as {
            [id: string]: SurveyReport[]
          }
          const byId = {} as {
            [id: string]: SurveyReport
          }
          const categories: string[] = []
          _surveyReports.forEach(report => {
            registerFiles(report.files, 'surveyReports', report)
            const dateSurveyed = formatYear(report.dateSurveyed)
            if (data[dateSurveyed] === undefined) {
              categories.push(dateSurveyed)
              data[dateSurveyed] = []
            }
            data[dateSurveyed].push(report)
            byId[report.id] = report
          })
          categories.sort().reverse()

          set({
            all: _surveyReports,
            data,
            categories,
            byId,
          })
        },
        error => {
          // This should be very rare
          done()
          console.log('Failed to access Survey Documents', error.message, error, {
            vesselId,
          })
        }
      )
    }
  },
}
