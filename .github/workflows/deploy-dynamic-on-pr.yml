name: Deploy to Dynamic Web Hosting (Development)

on:
  pull_request:
    types: [opened, synchronize, reopened]

permissions:
  contents: read
  pull-requests: write
  issues: write

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'yarn'

      - name: Setup EAS
        uses: expo/expo-github-action@v8
        with:
          expo-version: latest
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}
          
      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Find and delete existing deployment
        uses: actions/github-script@v7
        with:
          script: |
            const comments = await github.rest.issues.listComments({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
            });

            // Find deployment comments
            const deploymentComments = comments.data.filter(comment => 
              comment.body.includes('🌐 Web Deployment') && comment.body.includes('<pre>')
            );

            if (deploymentComments.length > 0) {
              const latestComment = deploymentComments[deploymentComments.length - 1];
              const preMatch = latestComment.body.match(/<pre>([^<]+)<\/pre>/);
              
              if (preMatch) {
                const deploymentId = preMatch[1];
                console.log(`Found existing deployment ID: ${deploymentId}`);
                
                // Delete the existing deployment
                const { exec } = require('child_process');
                const util = require('util');
                const execPromise = util.promisify(exec);
                
                try {
                  await execPromise(`eas deploy:delete ${deploymentId} --non-interactive`);
                  console.log(`Successfully deleted deployment: ${deploymentId}`);
                } catch (error) {
                  console.log(`Failed to delete deployment ${deploymentId}: ${error.message}`);
                  // Continue with new deployment even if deletion fails
                }
              }

              // Delete all old deployment comments
              for (const comment of deploymentComments) {
                try {
                  await github.rest.issues.deleteComment({
                    owner: context.repo.owner,
                    repo: context.repo.repo,
                    comment_id: comment.id,
                  });
                  console.log(`Deleted old deployment comment: ${comment.id}`);
                } catch (error) {
                  console.log(`Failed to delete comment ${comment.id}: ${error.message}`);
                }
              }
            } else {
              console.log('No existing deployment comments found');
            }
        
      - name: Pull environment variables
        run: eas env:pull development --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
          
      - name: Export web build
        run: npx expo export --platform web
        
      - name: Deploy to EAS
        id: deploy
        run: |
          echo "Starting web deployment..."
          
          if DEPLOY_OUTPUT=$(eas deploy --non-interactive 2>&1); then
            echo "Deploy command succeeded"
            echo "$DEPLOY_OUTPUT"
            
            # Extract deployment URL from output
            DEPLOY_URL=$(echo "$DEPLOY_OUTPUT" | grep -o 'https://sea-flux--[^[:space:]]*\.expo\.app' | head -1)
            
            if [ -n "$DEPLOY_URL" ]; then
              echo "Found deployment URL: $DEPLOY_URL"
              echo "deploy_url=$DEPLOY_URL" >> $GITHUB_OUTPUT
            else
              echo "Deploy succeeded but no URL found in output"
              echo "Full output:"
              echo "$DEPLOY_OUTPUT"
              exit 1
            fi
          else
            echo "Deploy command failed with exit code $?"
            echo "Error output:"
            echo "$DEPLOY_OUTPUT"
            exit 1
          fi
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

      - name: Parse deployment ID
        if: steps.deploy.outputs.deploy_url != ''
        id: parse_id
        run: |
          DEPLOY_URL="${{ steps.deploy.outputs.deploy_url }}"
          # Extract ID from URL pattern: https://sea-flux--<ID>.expo.app
          DEPLOYMENT_ID=$(echo "$DEPLOY_URL" | sed -n 's/.*sea-flux--\([^.]*\)\.expo\.app.*/\1/p')
          
          if [ -n "$DEPLOYMENT_ID" ]; then
            echo "Extracted deployment ID: $DEPLOYMENT_ID"
            echo "deployment_id=$DEPLOYMENT_ID" >> $GITHUB_OUTPUT
          else
            echo "Failed to extract deployment ID from URL: $DEPLOY_URL"
            exit 1
          fi

      - name: Comment on PR with deployment success
        if: steps.deploy.outputs.deploy_url != '' && steps.parse_id.outputs.deployment_id != ''
        uses: actions/github-script@v7
        with:
          script: |
            const deployUrl = '${{ steps.deploy.outputs.deploy_url }}';
            const deploymentId = '${{ steps.parse_id.outputs.deployment_id }}';
            const timestamp = new Date().toISOString();
            
            const comment = `## 🌐 Web Deployment Ready
            
            A new web deployment has been created:
            
            **Deployment URL:** ${deployUrl}
            **Deployment ID:** <pre>${deploymentId}</pre>
            **Timestamp:** ${timestamp}
            
            The deployment is now live!`;

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });

      - name: Comment on PR if deployment fails
        if: failure()
        uses: actions/github-script@v7
        with:
          script: |
            const timestamp = new Date().toISOString();
            
            const comment = `## ❌ Web Deployment Failed
            
            The web deployment failed during this workflow run.
            
            **Timestamp:** ${timestamp}
            
            Please check the [workflow logs](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}) for more details.`;

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });