name: Deploy to Web (Preview)

on:
  push:
    branches:
      - preview

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'yarn'
          
      - name: Install dependencies
        run: yarn install --frozen-lockfile
        
      - name: Setup EAS CLI
        run: yarn global add eas-cli
        
      - name: Pull environment variables
        run: eas env:pull preview --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
          
      - name: Export web build
        run: npx expo export --platform web
        
      - name: Deploy to EAS
        run: eas deploy --alias staging
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}