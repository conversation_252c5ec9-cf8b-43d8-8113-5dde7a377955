name: Build iOS Dev Client When Config Changes

on:
  workflow_dispatch:
  pull_request:
    paths:
      - 'package.json'
      - 'app.config.js'
      - 'eas.json'

permissions:
  contents: read
  pull-requests: write
  issues: write

jobs:
  build:
    name: Install and build
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
      - uses: actions/setup-node@v4
        with:
          node-version: 20.x
          cache: yarn
      - name: Setup Expo and EAS
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}
      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Build on EAS
        id: build
        run: |
          echo "Starting iOS development build..."

          # Run the build command and capture output
          if BUILD_OUTPUT=$(eas env:pull development --non-interactive && eas build --profile development --platform ios --clear-cache --non-interactive --no-wait 2>&1); then
            echo "Build command succeeded"
            echo "$BUILD_OUTPUT"
            
            # Extract build URL from output
            BUILD_URL=$(echo "$BUILD_OUTPUT" | grep -o 'https://expo\.dev/[^[:space:]]*' | head -1)
            
            if [ -n "$BUILD_URL" ]; then
              echo "Found build URL: $BUILD_URL"
              echo "build_url=$BUILD_URL" >> $GITHUB_OUTPUT
            else
              echo "Build succeeded but no URL found in output"
              echo "Full output:"
              echo "$BUILD_OUTPUT"
            fi
          else
            echo "Build command failed with exit code $?"
            echo "Error output:"
            echo "$BUILD_OUTPUT"
            exit 1
          fi

      - name: Comment on PR with build link
        if: steps.build.outputs.build_url != ''
        uses: actions/github-script@v7
        with:
          script: |
            const buildUrl = '${{ steps.build.outputs.build_url }}';
            const comment = `## 📱 iOS Dev Client Build Ready

            A new iOS development build has been created due to changes in configuration files (package.json, app.config.js, eas.json):

            **Build URL:** ${buildUrl}

            You can install this build on your device by scanning the QR code on the build page`;

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
