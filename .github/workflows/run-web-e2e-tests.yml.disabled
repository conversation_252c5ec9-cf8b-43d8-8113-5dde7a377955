# TO DO: Fix JS bundle issue to get E2E tests running against PR and main branches
name: Run Web E2E Tests (Playwright)
on:
  push:
    branches: main
  pull_request:
    branches: main
jobs:
  run-e2e-tests:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    env:
      EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node
        uses: actions/setup-node@v4
        with:
          node-version: 23.x
          cache: "yarn"

      - name: Install app dependencies
        run: yarn install --frozen-lockfile

      - name: Install EAS CLI
        run: yarn global add eas-cli

      - name: Start Expo server in background
        run: yarn start:test &
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

      - name: Wait for server startup
        timeout-minutes: 3
        run: |
          while ! curl -f -s http://localhost:8081 > /dev/null; do
            echo "Waiting for Expo server to be ready..."
            sleep 5
          done
          echo "✅ Server is ready!"

      - name: Pre-warm bundle and JavaScript compilation
        run: |
          # CRITICAL: Pre-warming the actual JavaScript bundle Metro will serve
          echo "🔥 Pre-warming JavaScript bundle..."
          JS_BUNDLE_URL="http://localhost:8081/index.tsx.bundle?platform=web&dev=true&hot=false&transform.engine=hermes&transform.routerRoot=src%2Fapp&unstable_transformProfile=hermes-stable"

          # Try to trigger bundle compilation by accessing it multiple times
          for i in {1..3}; do
            echo "Attempt $i: Requesting JS bundle..."
            if curl -f -s "$JS_BUNDLE_URL" > /dev/null; then
              echo "✅ JS bundle accessible on attempt $i"
              break
            else
              echo "⚠️ JS bundle not ready on attempt $i, waiting..."
              sleep 15
            fi
          done

          echo "🕐 Pre-warming completed, giving Metro extra time to settle..."
          sleep 60  # Give Metro time to finish any pending compilation

      - name: Install E2E test dependencies
        working-directory: ./e2e-tests/web
        run: yarn install

      - name: Install Playwright browsers
        working-directory: ./e2e-tests/web
        run: yarn playwright install chromium --with-deps

      - name: Run Playwright tests
        working-directory: ./e2e-tests/web
        run: yarn playwright test
        env:
          E2E_ADMIN_PASSWORD: ${{ secrets.E2E_ADMIN_PASSWORD }}

      - name: Upload test report
        uses: actions/upload-artifact@v4
        if: ${{ !cancelled() }}
        with:
          name: ${{ github.event_name == 'pull_request' && format('playwright-report-pr-{0}-run-{1}', github.event.number, github.run_attempt) || format('playwright-report-{0}-{1}', github.run_id, github.run_attempt) }}
          path: e2e-tests/web/playwright-report/
          retention-days: 30
