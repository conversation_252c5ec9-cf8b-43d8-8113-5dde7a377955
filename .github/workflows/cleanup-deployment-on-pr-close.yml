name: Cleanup Deployment on PR Close

on:
  pull_request:
    types: [closed]

permissions:
  contents: read
  pull-requests: write
  issues: write

jobs:
  cleanup:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'yarn'

      - name: Setup EAS
        uses: expo/expo-github-action@v8
        with:
          expo-version: latest
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}
          
      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Find and delete deployment
        uses: actions/github-script@v7
        with:
          script: |
            const comments = await github.rest.issues.listComments({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
            });

            // Find deployment comments
            const deploymentComments = comments.data.filter(comment => 
              comment.body.includes('🌐 Web Deployment') && comment.body.includes('<pre>')
            );

            if (deploymentComments.length > 0) {
              const latestComment = deploymentComments[deploymentComments.length - 1];
              const preMatch = latestComment.body.match(/<pre>([^<]+)<\/pre>/);
              
              if (preMatch) {
                const deploymentId = preMatch[1];
                console.log(`Found deployment ID to cleanup: ${deploymentId}`);
                
                // Delete the deployment
                const { exec } = require('child_process');
                const util = require('util');
                const execPromise = util.promisify(exec);
                
                try {
                  await execPromise(`eas deploy:delete ${deploymentId} --non-interactive`);
                  console.log(`Successfully deleted deployment: ${deploymentId}`);
                  
                  // Comment on PR about cleanup
                  const timestamp = new Date().toISOString();
                  const comment = `## 🧹 Deployment Cleaned Up
                  
                  The web deployment for this PR has been automatically deleted.
                  
                  **Deleted Deployment ID:** <pre>${deploymentId}</pre>
                  **Timestamp:** ${timestamp}
                  
                  Resources have been freed up!`;

                  await github.rest.issues.createComment({
                    issue_number: context.issue.number,
                    owner: context.repo.owner,
                    repo: context.repo.repo,
                    body: comment
                  });
                  
                } catch (error) {
                  console.log(`Failed to delete deployment ${deploymentId}: ${error.message}`);
                  
                  // Comment on PR about cleanup failure
                  const timestamp = new Date().toISOString();
                  const comment = `## ⚠️ Deployment Cleanup Failed
                  
                  Failed to automatically delete the web deployment for this PR.
                  
                  **Deployment ID:** <pre>${deploymentId}</pre>
                  **Timestamp:** ${timestamp}
                  **Error:** ${error.message}
                  
                  You may need to manually delete this deployment using: \`eas deploy:delete ${deploymentId}\``;

                  await github.rest.issues.createComment({
                    issue_number: context.issue.number,
                    owner: context.repo.owner,
                    repo: context.repo.repo,
                    body: comment
                  });
                }
              } else {
                console.log('Found deployment comment but could not extract ID');
              }
            } else {
              console.log('No deployment comments found - nothing to cleanup');
            }
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}