const fs = require("fs");

// Load the metadata JSON
const metadata = JSON.parse(fs.readFileSync("MaterialIcons.json", "utf8"));

// Extract icon names
const iconNames = metadata.icons.map((icon) => icon.name);

// Generate TypeScript union type
const typeDefinition = `export type MaterialIconName =\n  | "${iconNames.join('"\n  | "')}"\n;`;

// Write to a .ts file
fs.writeFileSync("./src/types/MaterialIcons.ts", typeDefinition);
